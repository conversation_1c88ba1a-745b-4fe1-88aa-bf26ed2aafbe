import { ROUTE_PERMISSION_MAP } from "@/constants/routePermissions";

/**
 * Helper function to normalize route path
 */
export const normalizePath = (path: string): string => {
  return path === "/" ? path : path.replace(/\/$/, "");
};

/**
 * Helper function to convert Next.js dynamic route pattern to regex pattern
 */
export const routeToRegex = (pattern: string): RegExp => {
  const regexPattern = normalizePath(pattern)
    .replace(/[.+?^${}()|\\\/]/g, "\\$&")
    .replace(/\[\.\.\.[^\]]+\]/g, ".*")
    .replace(/\[[^\]]+\]/g, "[^/]+");

  return new RegExp(`^${regexPattern}$`);
};

// Cache for compiled route patterns
let _staticRoutePermissions: Map<string, string | string[]> | null = null;
let _dynamicRoutePatterns: Map<RegExp, string | string[]> | null = null;

/**
 * Get static route permissions with lazy initialization and caching
 */
export const getStaticRoutePermissions = (): Map<string, string | string[]> => {
  if (!_staticRoutePermissions) {
    _staticRoutePermissions = new Map<string, string | string[]>(Object.entries(ROUTE_PERMISSION_MAP).filter(([pattern]) => !pattern.includes("[")));
  }
  return _staticRoutePermissions;
};

/**
 * Get dynamic route patterns with lazy initialization and caching
 */
export const getDynamicRoutePatterns = (): Map<RegExp, string | string[]> => {
  if (!_dynamicRoutePatterns) {
    _dynamicRoutePatterns = new Map<RegExp, string | string[]>();

    Object.entries(ROUTE_PERMISSION_MAP)
      .filter(([pattern]) => pattern.includes("["))
      .map(([pattern, permissions]): [RegExp, string | string[]] => [routeToRegex(pattern), permissions])
      .sort((a, b) => {
        const segmentsA = a[0].toString().split("/").length;
        const segmentsB = b[0].toString().split("/").length;
        return segmentsB - segmentsA;
      })
      .forEach(([regex, permissions]) => {
        _dynamicRoutePatterns!.set(regex, permissions);
      });
  }
  return _dynamicRoutePatterns;
};

/**
 * Pre-compile regex patterns for dynamic routes for better performance
 * @deprecated Use getDynamicRoutePatterns() instead for better caching
 */
export const createDynamicRoutePatterns = (): Map<RegExp, string | string[]> => {
  return getDynamicRoutePatterns();
};

/**
 * Create static route permissions map for faster lookups
 * @deprecated Use getStaticRoutePermissions() instead for better caching
 */
export const createStaticRoutePermissions = (): Map<string, string | string[]> => {
  return getStaticRoutePermissions();
};

/**
 * Helper function to check if user has any of the required permissions
 */
export const hasAnyPermission = (userPermissions: string[], required: string | string[]): boolean => {
  return Array.isArray(required) ? required.some((perm) => userPermissions.includes(perm)) : userPermissions.includes(required);
};

/**
 * Helper function to check if a user has permission for a given route
 */
export const hasPermissionForRoute = (
  path: string,
  userPermissions: string[],
  staticRoutePermissions: Map<string, string | string[]>,
  dynamicRoutePatterns: Map<RegExp, string | string[]>
): boolean => {
  if (!userPermissions.length) return false;

  // Check static routes first (faster lookup)
  const staticPermissions = staticRoutePermissions.get(path);
  if (staticPermissions) {
    return hasAnyPermission(userPermissions, staticPermissions);
  }

  // Check dynamic routes
  for (const [pattern, requiredPermissions] of Array.from(dynamicRoutePatterns.entries())) {
    if (pattern.test(path)) {
      return hasAnyPermission(userPermissions, requiredPermissions);
    }
  }

  // No match found in either static or dynamic routes - allow unrestricted access
  return true;
};

/**
 * Helper function to parse cookie data safely
 */
export const parseCookieData = <T>(cookieString: string | undefined | null, cookieName: string): T | null => {
  if (!cookieString) return null;
  const cookieValue = cookieString.split(";").find((c) => c.trim().startsWith(`${cookieName}=`));
  if (!cookieValue) return null;

  try {
    const encodedValue = cookieValue.split("=")[1];
    if (!encodedValue) return null;
    return JSON.parse(decodeURIComponent(encodedValue)) as T;
  } catch (error) {
    console.error(`Error parsing ${cookieName} cookie:`, error);
    return null;
  }
};
