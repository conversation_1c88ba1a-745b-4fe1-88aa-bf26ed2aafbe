# User Walkthrough Component

A reusable, focused tour component for guiding users through different pages in the application.

## Features

- ✅ Auto-start tours for first-time visitors
- ✅ Manual start button option
- ✅ Page-specific tour steps
- ✅ Persistent tour history (local storage)
- ✅ Animation-friendly with configurable delays
- ✅ Reliable element targeting with ID selectors
- ✅ Centralized tour step configuration

## Usage Guide

### Step 1: Add IDs to Target Elements

Add unique IDs to the elements you want to highlight in your tour:

```jsx
<div id="feature-card" className="some-class">
  ...
</div>
```

### Step 2: Define Tour Steps in Config File

Add steps for your page in `tourStepsConfig.ts`:

```typescript
// src/components/commonComponent/UserWalkthrough/tourStepsConfig.ts

export const yourPageSteps: Step[] = [
  {
    target: "#feature-card",
    content: "Feature description goes here",
    disableBeacon: true, // First step should have this
    placement: "top", // Or "bottom", "left", "right"
  },
  // Add more steps...
];
```

### Step 3: Add the Component to Your Page

```jsx
import UserWalkthrough from "@/components/commonComponent/UserWalkthrough";
import { yourPageSteps } from "@/components/commonComponent/UserWalkthrough/tourStepsConfig";

function YourPage() {
  // Your existing code...

  return (
    <>
      <UserWalkthrough
        pageId="yourPageId"
        steps={yourPageSteps}
        isDataLoaded={!isLoading}
        startDelay={1000}
        showButton={true}
        options={{
          // Optional callbacks and settings
          onTourStart: () => console.log("Tour started"),
          onTourEnd: () => console.log("Tour ended"),
          resetOnRevisit: false, // Set to true to always show tour
        }}
      />

      {/* Rest of your page */}
    </>
  );
}
```

## Advanced Usage

````

### Coordinate with Animations

Adjust `startDelay` based on your page's animation timings:

```jsx
<UserWalkthrough
  pageId="animatedPage"
  steps={animatedPageSteps}
  isDataLoaded={!isLoading}
  startDelay={1500} // Allow animations to complete first
  showButton={true}
/>
````

### Best Practices

1. **Use IDs, not classes** for targeting elements to ensure reliable tour steps
2. **Keep content concise** - aim for 1-2 sentences per step
3. **Limit tour length** - 3-5 steps is optimal for user attention
4. **Test on all screen sizes** - ensure tooltips fit properly on mobile
5. **Consider load times** - use `isDataLoaded` to prevent tours starting before content appears
