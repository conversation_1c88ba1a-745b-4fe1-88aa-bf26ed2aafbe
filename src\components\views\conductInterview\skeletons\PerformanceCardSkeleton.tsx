import React from "react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

interface PerformanceCardSkeletonProps {
  count?: number;
}

export const PerformanceCardSkeleton = ({ count = 1 }: PerformanceCardSkeletonProps) => {
  return (
    <div className="row g-4">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="col-md-4">
          <div className="w-100">
            <Skeleton height={168} width="100%" borderRadius={24} />
          </div>
        </div>
      ))}
    </div>
  );
};
