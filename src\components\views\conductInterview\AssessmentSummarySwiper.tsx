import React from "react";

import { Swiper, SwiperSlide } from "swiper/react";
// import { Navigation } from "swiper/modules";
import { Pagination, Navigation } from "swiper/modules";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import NavigationIcon from "@/components/svgComponents/NavigationIcon";

const AssessmentSummarySwiper = () => {
  return (
    <div className="fas-swiper-container">
      <Swiper
        navigation={{
          prevEl: ".custom-prev",
          nextEl: ".custom-next",
          enabled: true,
        }}
        pagination={{
          type: "fraction",
          el: ".swiper-pagination-text",
        }}
        className="mySwiper"
        modules={[Pagination, Navigation]}
      >
        <SwiperSlide>
          <div className="summary-text-card">
            <h3 className="tittle">Organization</h3>
            <h5 className="sub-tittle">Strengths</h5>
            <ul>
              <li>
                <p>Strong note-taking skills (copious notes, uses OneNote and a notebook).</p>
              </li>
              <li>
                <p>Created a handbook for interns, demonstrating initiative and organizational thoughtfulness.</p>
              </li>
              <li>
                <p>Effective use of calendar and email for tracking tasks and communications.</p>
              </li>
            </ul>
            <h5 className="sub-tittle">Potential Gaps</h5>
            <ul>
              <li>
                <p>Might need refinement in prioritizing tasks for higher-pressure, fast-paced environments.</p>
              </li>
            </ul>
            <h5 className="sub-tittle">
              Probability of Success in This Skill: <span className="color-secondary">80%</span>
            </h5>
            <ul>
              <li>
                <p>Might need refinement in prioritizing tasks for higher-pressure, fast-paced environments.</p>
              </li>
            </ul>
          </div>
        </SwiperSlide>
        <SwiperSlide>
          <div className="summary-text-card">
            <h3 className="tittle">Organization</h3>
            <h5 className="sub-tittle">Strengths</h5>
            <ul>
              <li>
                <p>Strong note-taking skills (copious notes, uses OneNote and a notebook).</p>
              </li>
              <li>
                <p>Created a handbook for interns, demonstrating initiative and organizational thoughtfulness.</p>
              </li>
              <li>
                <p>Effective use of calendar and email for tracking tasks and communications.</p>
              </li>
            </ul>
            <h5 className="sub-tittle">Potential Gaps</h5>
            <ul>
              <li>
                <p>Might need refinement in prioritizing tasks for higher-pressure, fast-paced environments.</p>
              </li>
            </ul>
            <h5 className="sub-tittle">
              Probability of Success in This Skill: <span className="color-secondary">80%</span>
            </h5>
            <ul>
              <li>
                <p>Might need refinement in prioritizing tasks for higher-pressure, fast-paced environments.</p>
              </li>
            </ul>
          </div>
        </SwiperSlide>
        <SwiperSlide>
          <div className="summary-text-card">
            <h3 className="tittle">Organization</h3>
            <h5 className="sub-tittle">Strengths</h5>
            <ul>
              <li>
                <p>Strong note-taking skills (copious notes, uses OneNote and a notebook).</p>
              </li>
              <li>
                <p>Created a handbook for interns, demonstrating initiative and organizational thoughtfulness.</p>
              </li>
              <li>
                <p>Effective use of calendar and email for tracking tasks and communications.</p>
              </li>
            </ul>
            <h5 className="sub-tittle">Potential Gaps</h5>
            <ul>
              <li>
                <p>Might need refinement in prioritizing tasks for higher-pressure, fast-paced environments.</p>
              </li>
            </ul>
            <h5 className="sub-tittle">
              Probability of Success in This Skill: <span className="color-secondary">80%</span>
            </h5>
            <ul>
              <li>
                <p>Might need refinement in prioritizing tasks for higher-pressure, fast-paced environments.</p>
              </li>
            </ul>
          </div>
        </SwiperSlide>
        <SwiperSlide>
          <div className="summary-text-card">
            <h3 className="tittle">Organization</h3>
            <h5 className="sub-tittle">Strengths</h5>
            <ul>
              <li>
                <p>Strong note-taking skills (copious notes, uses OneNote and a notebook).</p>
              </li>
              <li>
                <p>Created a handbook for interns, demonstrating initiative and organizational thoughtfulness.</p>
              </li>
              <li>
                <p>Effective use of calendar and email for tracking tasks and communications.</p>
              </li>
            </ul>
            <h5 className="sub-tittle">Potential Gaps</h5>
            <ul>
              <li>
                <p>Might need refinement in prioritizing tasks for higher-pressure, fast-paced environments.</p>
              </li>
            </ul>
            <h5 className="sub-tittle">
              Probability of Success in This Skill: <span className="color-secondary">80%</span>
            </h5>
            <ul>
              <li>
                <p>Might need refinement in prioritizing tasks for higher-pressure, fast-paced environments.</p>
              </li>
            </ul>
          </div>
        </SwiperSlide>
      </Swiper>
      <div className="swiper-footer">
        <div className="custom-nav-buttons">
          <button className="custom-prev rotate">
            <NavigationIcon />
          </button>
          <button className="custom-next">
            <NavigationIcon />
          </button>
        </div>
        <div className="swiper-pagination-text" />
      </div>
    </div>
  );
};

export default AssessmentSummarySwiper;
