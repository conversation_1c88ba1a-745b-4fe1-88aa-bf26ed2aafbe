"use client";
import React, { FC } from "react";
import <PERSON>ton from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import { useTranslations } from "next-intl";

interface IProps {
  onClickCancel: () => void;
  onClickGenerate: () => void;
  disabled?: boolean;
}

const FinalAssessmentConfirmModal: FC<IProps> = ({ onClickCancel, onClickGenerate, disabled }) => {
  const t = useTranslations();

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered modal-xl">
        <div className="modal-content">
          <div className="modal-header text-center pb-0">
            <h4>{t("generate_final_candidate_assessment")}</h4>
            <p className="m-0 textMd w100 text-center">{t("before_proceeding_please_review_the_candidates_overall_performance")}</p>
            <Button className="modal-close-btn" onClick={onClickCancel}>
              <ModalCloseIcon />
            </Button>
          </div>
          <div className="modal-body position-relative">
            <div className="alert alert-warning mb-4">
              <p className="m-0">{t("final_assessment_warning_message")}</p>
            </div>

            <div className="action-btn justify-content-end">
              <Button className="primary-btn rounded-md" onClick={onClickGenerate} disabled={disabled}>
                {t("generate_final_assessment")}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default FinalAssessmentConfirmModal;
