import CandidateQualification from "@/components/views/resume/CandidateQualification";

export const metadata = {
  title: "Candidate Qualification | S9 InnerView",
  description: "Assess candidate qualifications against jobs.",
};

const page = ({ params, searchParams }: { params: Promise<{ jobId: string }>; searchParams: Promise<{ title: string; jobUniqueId: string }> }) => {
  return (
    <div>
      <CandidateQualification params={params} searchParams={searchParams} />
    </div>
  );
};

export default page;
