import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import "bootstrap/dist/css/bootstrap.min.css"; // If not already imported globally

const CalendarSkeleton: React.FC = () => {
  // 6 rows for weeks, 7 columns for days
  return (
    <>
      <div className="row align-items-center justify-content-between mb-4">
        <div className="col d-flex justify-content-start">
          <Skeleton width={80} height={35} borderRadius={16} />
        </div>
        <div className="col d-flex justify-content-center">
          <Skeleton width={128} height={30} borderRadius={8} />
        </div>
        <div className="col d-flex justify-content-end">
          <Skeleton width={121} height={35} borderRadius={16} />
        </div>
      </div>
      <div
        style={{
          padding: "0",
          border: "1.5px solid rgba(51, 51, 51, 0.2)",
          borderRadius: "12px",
          boxShadow: "0px 60px 120px 0px rgba(38, 51, 77, 0.05)",
          overflow: "hidden",
        }}
      >
        <table className="table table-bordered" style={{ marginBottom: 0 }}>
          <thead>
            <tr>
              {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
                <th key={day} className="text-center" style={{ height: 50, verticalAlign: "middle" }}>
                  <Skeleton width={40} height={12} />
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {[...Array(6)].map((_, rowIdx) => (
              <tr key={rowIdx}>
                {[...Array(7)].map((_, colIdx) => (
                  <td key={colIdx} style={{ height: 70, paddingBottom: "15px" }} align="right">
                    <Skeleton width={24} height={18} />
                    <Skeleton width="90%" height={16} borderRadius={4} className="mt-5" />
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </>
  );
};

export default CalendarSkeleton;
