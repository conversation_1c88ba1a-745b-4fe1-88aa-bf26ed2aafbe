/* Animation styles for drag and drop */
@keyframes floatEffect {
  0% {
    transform: scale(1.05) translateY(0px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  }
  50% {
    transform: scale(1.07) translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  }
  100% {
    transform: scale(1.05) translateY(0px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  }
}

.dragGhost {
  animation: floatEffect 1.2s ease-in-out infinite;
  cursor: grabbing !important;
  opacity: 0.95;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  background-color: #f0f6ff !important;
  border-color: #5a9bef !important;
  transform-origin: center center;
  transition: none !important;
  will-change: transform, box-shadow;

  // Override skill box styles for better visual feedback
  :global(.skill-box),
  :global(.skill-item) {
    background: linear-gradient(45deg, #f0f6ff, #e6f0ff) !important;
    border-color: #5a9bef !important;
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3) !important;
    color: #0056b3 !important;
  }
}

.draggingOriginal {
  opacity: 0.35;
  outline: 2px dashed #5a9bef;
  transform: scale(0.98);
}

// Add a hover effect to draggable items to indicate they can be dragged
:global(.draggable-item) {
  cursor: grab;
  transition:
    transform 0.15s ease,
    box-shadow 0.15s ease,
    background-color 0.15s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background-color: #f9f9f9;
  }

  &:active {
    cursor: grabbing;
  }
}

// Highlight drop areas when item is being dragged over them
.dropHighlight {
  background-color: rgba(90, 155, 239, 0.1);
  border: 2px dashed #5a9bef;
  transition:
    background-color 0.2s ease,
    border 0.2s ease;
}
