import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript", "prettier"),
  {
    rules: {
      "react/react-in-jsx-scope": "off",
      "prop-types": "off",
      "no-inferrable-types": "off",
      "react/no-unescaped-entities": "off",
      "react/no-empty-function": "off",

      "no-console": [
        2,
        {
          allow: ["warn", "log", "error"],
        },
      ],

      quotes: ["error", "double"],
      eqeqeq: ["warn", "smart"],
      "no-debugger": "warn",
      "react/prop-types": "off",
      "no-unused-expressions": "warn",
      "no-empty": "off",
      "eol-last": "error",
      "no-loss-of-precision": "off",
      "@typescript-eslint/no-loss-of-precision": "error",
      "@typescript-eslint/no-explicit-any": "warn",
      "@next/next/no-page-custom-font": "off",
      "react/display-name": "off",
    },
  },
];

export default eslintConfig;
