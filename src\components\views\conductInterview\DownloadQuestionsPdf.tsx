import { STRATUM9_LOGO_URL } from "@/constants/commonConstants";
import { IInterviewQuestionResponse } from "@/interfaces/interviewInterfaces";
import { toastMessageSuccess } from "@/utils/helper";
import jsPDF from "jspdf";

export interface IQuestionCategory {
  questions: IInterviewQuestionResponse[];
  score: number;
  isLocked?: boolean;
  interviewerName?: string;
}

export interface IGetInterviewSkillQuestionsResponse {
  roleSpecificQuestions: Record<string, IQuestionCategory>;
  cultureSpecificQuestions: Record<string, IQuestionCategory>;
  careerBasedQuestions: IQuestionCategory;
}

const downloadQuestions = async (
  careerBasedQuestions: IQuestionCategory,
  roleSpecificQuestions: Record<string, IQuestionCategory>,
  cultureSpecificQuestions: Record<string, IQuestionCategory>,
  t: (key: string) => string,
  interviewerName?: string,
  candidateName?: string,
  jobId?: string
) => {
  // Convert logo to base64 for proper PDF embedding
  const convertImageToBase64 = async (url: string): Promise<string> => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error("Error converting image to base64:", error);
      return "";
    }
  };

  const logoBase64 = await convertImageToBase64(STRATUM9_LOGO_URL);

  const doc = new jsPDF({
    orientation: "portrait",
    unit: "mm",
    format: "a4",
  });

  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  const margin = 15;
  const contentWidth = pageWidth - margin * 2;

  let currentY = 20;

  // Header with logo
  const addHeader = () => {
    if (logoBase64) {
      // Center the logo (174px width = ~61mm, 43px height = ~15mm)
      const logoWidth = 61;
      const logoHeight = 15;
      const logoX = (pageWidth - logoWidth) / 2;
      doc.addImage(logoBase64, "PNG", logoX, currentY, logoWidth, logoHeight);
      currentY += logoHeight + 10;
    }

    // Title
    doc.setFontSize(17);
    doc.setFont("helvetica", "bold");
    doc.setTextColor(51, 51, 51);
    doc.text("Pre-Interview Questions", pageWidth / 2, currentY, { align: "center" });
    doc.setFont("helvetica", "normal"); // Reset font to normal after title
    currentY += 12;
  };

  // Function to add a section with rounded border design
  const addSection = (title: string, content: any, isMultiCategory: boolean = false) => {
    // Start section header
    const addSectionHeader = (sectionTitle: string) => {
      // Check if we need a new page for header
      if (currentY > pageHeight - 50) {
        doc.addPage();
        currentY = 20;
      }

      const headerStartY = currentY;

      // Section header background with light orange color (#cb99321a)
      doc.setFillColor(251, 248, 240); // Light orange background equivalent to #cb99321a
      doc.roundedRect(margin, headerStartY, contentWidth, 12, 3, 3, "F");

      // Section header text
      doc.setFontSize(14);
      doc.setTextColor(203, 153, 50);
      doc.text(sectionTitle, margin + 5, headerStartY + 8);

      currentY = headerStartY + 20;
    };

    addSectionHeader(title);

    if (isMultiCategory) {
      // Handle multiple categories with dividers
      const categories = Object.keys(content);
      categories.forEach((category, categoryIndex) => {
        // Check if we need a new page for category title
        if (currentY > pageHeight - 40) {
          doc.addPage();
          currentY = 20;
        }

        // Category title
        doc.setFontSize(14);
        doc.setTextColor(67, 110, 182); // Blue color matching the footer
        doc.text(category, margin + 5, currentY);
        currentY += 8;

        // Questions
        doc.setFontSize(11);
        doc.setTextColor(51, 51, 51); // Reset to gray for questions
        content[category].questions.forEach((q: any, i: number) => {
          const questionText = `${i + 1}. ${q.question}`;
          const lines = doc.splitTextToSize(questionText, contentWidth - 15);

          // Check if question fits on current page
          const questionHeight = lines.length * 5;
          if (currentY + questionHeight > pageHeight - 40) {
            doc.addPage();
            currentY = 20;
          }

          doc.text(lines, margin + 5, currentY);
          currentY += questionHeight + 2;
        });

        // Add divider if not last category
        if (categoryIndex < categories.length - 1) {
          // Check if divider fits on current page
          if (currentY + 15 > pageHeight - 40) {
            doc.addPage();
            currentY = 20;
          }

          currentY += 5;
          doc.setDrawColor(51, 51, 51, 0.2);
          doc.setLineWidth(0.3);
          doc.line(margin + 5, currentY, pageWidth - margin - 5, currentY);
          currentY += 10;
        }
      });
    } else {
      // Handle single category
      doc.setFontSize(11);
      doc.setTextColor(51, 51, 51);
      content.questions.forEach((q: any, i: number) => {
        const questionText = `${i + 1}. ${q.question}`;
        const lines = doc.splitTextToSize(questionText, contentWidth - 15);

        // Check if question fits on current page
        const questionHeight = lines.length * 5;
        if (currentY + questionHeight > pageHeight - 40) {
          doc.addPage();
          currentY = 20;
        }

        doc.text(lines, margin + 5, currentY);
        currentY += questionHeight + 2;
      });
    }

    currentY += 8; // Reduced space after section
  };

  // Add footer with blue border and logo
  const addFooter = () => {
    // Blue bottom border (10px height)
    doc.setFillColor(67, 110, 182);
    doc.rect(0, pageHeight - 3.5, pageWidth, 3.5, "F");

    // Footer logo
    if (logoBase64) {
      const logoWidth = 61;
      const logoHeight = 15;
      const logoX = (pageWidth - logoWidth) / 2;
      const logoY = pageHeight - 25;
      doc.addImage(logoBase64, "PNG", logoX, logoY, logoWidth, logoHeight);
    }
  };

  // Generate the PDF content
  addHeader();

  // Career Based Questions
  addSection("Career-Based Skills", careerBasedQuestions);

  // Role Specific Questions
  addSection("Role-Specific Performance Based Skills", roleSpecificQuestions, true);

  // Culture Specific Questions
  addSection("Culture-Specific Performance Based Skills", cultureSpecificQuestions, true);

  // Add footer to all pages
  const totalPages = doc.getNumberOfPages();
  for (let i = 1; i <= totalPages; i++) {
    doc.setPage(i);
    addFooter();
  }

  // Save PDF
  doc.save(
    `interview-questions-${jobId || "default"}-${interviewerName || "interviewer"}-${candidateName || "candidate"}-${new Date()
      .toISOString()
      .replace(/:/g, "-")}.pdf`
  );

  toastMessageSuccess(t("questions_downloaded_successfully"));
};

export default downloadQuestions;
