import React from "react";

type DeleteDarkIconProps = {
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
};

function DeleteDarkIcon({ className, onClick, disabled }: DeleteDarkIconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="33"
      height="32"
      viewBox="0 0 33 32"
      fill="none"
      className={className}
      onClick={disabled ? undefined : onClick}
    >
      <g clipPath="url(#clip0_9593_10502)">
        <path d="M5.99951 9.33594H27.3328" stroke="#333333" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M13.9995 14.6641V22.6641" stroke="#333333" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M19.3335 14.6641V22.6641" stroke="#333333" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path
          d="M7.3335 9.33594L8.66683 25.3359C8.66683 26.0432 8.94778 26.7215 9.44788 27.2216C9.94797 27.7217 10.6263 28.0026 11.3335 28.0026H22.0002C22.7074 28.0026 23.3857 27.7217 23.8858 27.2216C24.3859 26.7215 24.6668 26.0432 24.6668 25.3359L26.0002 9.33594"
          stroke="#333333"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12.6665 9.33333V5.33333C12.6665 4.97971 12.807 4.64057 13.057 4.39052C13.3071 4.14048 13.6462 4 13.9998 4H19.3332C19.6868 4 20.0259 4.14048 20.276 4.39052C20.526 4.64057 20.6665 4.97971 20.6665 5.33333V9.33333"
          stroke="#333333"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_9593_10502">
          <rect width="32" height="32" fill="white" transform="translate(0.666504)" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default DeleteDarkIcon;
