"use client";
import Head from "next/head";
import Link from "next/link";
import Image from "next/image";
import Logo from "../../../public/assets/images/logo.svg";
import ROUTES from "../../constants/routes";
import { useAuth } from "../../hooks/useAuth";

const PrivacyPolicy = () => {
  const { isAuthenticated } = useAuth();

  return (
    <>
      <Head>
        <title>Privacy Policy</title>
        <meta name="description" content="" key="desc" />
        <meta name="keywords" content=""></meta>
      </Head>
      <div className="logo-header text-center">
        <Link className="navbar-brand" href={isAuthenticated ? ROUTES.DASHBOARD : ROUTES.HOME}>
          <Image src={Logo} alt="logo" width={640} height={320} />
        </Link>
      </div>
      <section className="static-page">
        <div className="container">
          <h1>STRATUM 9 – Acceptable Use Policy &amp; Disclaimers</h1>
          <h3>1. Purpose of the Platform</h3>

          <p>
            The Platform is designed to provide organizations with tools to support decision-making in the hiring process, including AI-driven
            insights, job description generation, and candidate analysis. STRATUM 9 does not make hiring decisions, act as an employer of record, or
            serve as a staffing agency.
          </p>

          <h3>2. No Legal, HR, or Compliance Advice</h3>

          <p>
            STRATUM 9 does not provide legal advice, HR compliance services, or regulatory guarantees. Any AI-generated outputs or recommendations are
            for informational purposes only and must be reviewed by qualified HR or legal professionals before use.
          </p>

          <h3>3. Prohibited Uses</h3>

          <p>Users may not use the Platform to:</p>
          <p>- Engage in or support discrimination based on protected characteristics.</p>
          <p>- Bypass legal or ethical hiring requirements.</p>
          <p>- Misrepresent or falsify candidate data.</p>
          <p>- Use insights from the Platform as the sole basis for rejecting a candidate without lawful justification.</p>
          <p>- Infringe on privacy, intellectual property, or confidentiality rights.</p>

          <h3>4. Candidate Data &amp; Consent</h3>

          <p>
            Users are responsible for obtaining informed consent from candidates before uploading, storing, or analyzing personal data. STRATUM 9 is
            not liable for misuse, mishandling, or unauthorized disclosure of candidate data.
          </p>

          <h3>5. AI &amp; Interview Tools Disclaimer</h3>

          <p>
            AI-generated job descriptions, interview questions, or candidate analyses are suggestions only and may contain inaccuracies or omissions.
            Users must review outputs for accuracy, fairness, and compliance.
          </p>

          <h3>6. Performance &amp; Outcomes Disclaimer</h3>

          <p>
            STRATUM 9 does not guarantee hiring success, retention, or candidate performance. Post-hire outcomes remain the sole responsibility of the
            employer.
          </p>

          <h3>7. Limitation of Liability</h3>

          <p>
            Users agree to indemnify and hold harmless STRATUM 9 from any claims, damages, or legal actions arising from discriminatory or unlawful
            hiring practices, misuse of Platform tools, or disputes between employers and candidates.
          </p>

          <h3>8. Enforcement &amp; Termination</h3>

          <p>
            STRATUM 9 may suspend or terminate access to the Platform for violations of this Policy and reserves the right to cooperate with
            authorities in investigating unlawful use.
          </p>

          <h3>9. Governing Law</h3>

          <p>This Policy shall be governed by the laws of [Insert Jurisdiction].</p>
        </div>
      </section>
    </>
  );
};

export default PrivacyPolicy;
