"use client";
import React from "react";

interface ClientSideLoaderProps {
  size?: "sm" | "md" | "lg";
}

const ClientSideLoader: React.FC<ClientSideLoaderProps> = ({ size }) => {
  const sizeClass = size ? `spinner-border-${size}` : "";

  return (
    <div className={`spinner-border ${sizeClass}`} role="status">
      <span className="visually-hidden">Loading...</span>
    </div>
  );
};

export default ClientSideLoader;
