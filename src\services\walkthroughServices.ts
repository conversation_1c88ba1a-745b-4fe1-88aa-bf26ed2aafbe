import endpoint from "@/constants/endpoint";
import { IApiResponseCommonInterface } from "@/interfaces/commonInterfaces";
import { get, post } from "@/utils/http";

export interface IWalkthroughStatus {
  id: number;
  name: string;
  userId: number;
  updatedTs: string;
}

export interface IUpdateWalkthroughPayload {
  name: string;
}

/**
 * Fetch all walkthrough statuses for the current user
 */
export const getWalkthroughStatus = async (): Promise<IApiResponseCommonInterface<string[]>> => {
  return await get(endpoint.walkthrough.GET_WALKTHROUGH_STATUS);
};

/**
 * Update walkthrough status for a specific page
 * @param name The identifier of the walkthrough page
 */
export const updateWalkthroughStatus = async (name: string): Promise<IApiResponseCommonInterface<null>> => {
  return await post(endpoint.walkthrough.UPDATE_WALKTHROUGH_STATUS, { name });
};
