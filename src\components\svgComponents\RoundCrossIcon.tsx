import React from "react";

function RoundCrossIcon({ DangerColor }: { DangerColor?: boolean }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
      <circle cx="12.3331" cy="11.9996" r="10.9254" fill="white" />
      <path
        d="M12.3325 1C10.1569 1 8.03019 1.64514 6.22125 2.85383C4.41231 4.06253 3.00241 5.7805 2.16985 7.79048C1.33729 9.80047 1.11945 12.0122 1.54389 14.146C1.96832 16.2798 3.01597 18.2398 4.55435 19.7782C6.09273 21.3166 8.05274 22.3642 10.1865 22.7886C12.3203 23.2131 14.5321 22.9952 16.542 22.1627C18.552 21.3301 20.27 19.9202 21.4787 18.1113C22.6874 16.3023 23.3325 14.1756 23.3325 12C23.3291 9.08368 22.1691 6.28778 20.1069 4.22563C18.0447 2.16347 15.2489 1.00344 12.3325 1ZM16.5745 14.829C16.67 14.9212 16.7462 15.0316 16.7986 15.1536C16.851 15.2756 16.8786 15.4068 16.8798 15.5396C16.8809 15.6724 16.8556 15.8041 16.8053 15.927C16.7551 16.0499 16.6808 16.1615 16.5869 16.2554C16.493 16.3493 16.3814 16.4235 16.2585 16.4738C16.1356 16.5241 16.0039 16.5494 15.8711 16.5483C15.7383 16.5471 15.6071 16.5195 15.4851 16.4671C15.3631 16.4147 15.2528 16.3385 15.1605 16.243L12.3325 13.414L9.50453 16.243C9.31592 16.4252 9.06332 16.526 8.80112 16.5237C8.53893 16.5214 8.28812 16.4162 8.10271 16.2308C7.9173 16.0454 7.81213 15.7946 7.80985 15.5324C7.80757 15.2702 7.90837 15.0176 8.09053 14.829L10.9185 12L8.09053 9.171C7.99501 9.07875 7.91883 8.96841 7.86642 8.84641C7.81401 8.7244 7.78643 8.59318 7.78527 8.4604C7.78412 8.32762 7.80942 8.19594 7.8597 8.07305C7.90998 7.95015 7.98424 7.8385 8.07813 7.74461C8.17202 7.65071 8.28367 7.57646 8.40657 7.52618C8.52947 7.4759 8.66115 7.4506 8.79393 7.45175C8.92671 7.4529 9.05793 7.48049 9.17993 7.5329C9.30193 7.58531 9.41228 7.66149 9.50453 7.757L12.3325 10.586L15.1605 7.757C15.2528 7.66149 15.3631 7.58531 15.4851 7.5329C15.6071 7.48049 15.7383 7.4529 15.8711 7.45175C16.0039 7.4506 16.1356 7.4759 16.2585 7.52618C16.3814 7.57646 16.493 7.65071 16.5869 7.74461C16.6808 7.8385 16.7551 7.95015 16.8053 8.07305C16.8556 8.19594 16.8809 8.32762 16.8798 8.4604C16.8786 8.59318 16.851 8.7244 16.7986 8.84641C16.7462 8.96841 16.67 9.07875 16.5745 9.171L13.7465 12L16.5745 14.829Z"
        fill={DangerColor ? "#d00000" : "#333333"}
      />
    </svg>
  );
}

export default RoundCrossIcon;
