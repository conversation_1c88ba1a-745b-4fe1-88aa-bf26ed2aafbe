"use client";
import Button from "@/components/formElements/Button";
import style from "../../../styles/conductInterview.module.scss";
import Image from "next/image";
import { useState, useEffect, useCallback } from "react";
import { generateJobPortalScript, getMyProfile } from "@/services/userProfileService";
import { formatDate, toastMessageError, getUserInitials, toastMessageSuccess } from "@/utils/helper";
import { useTranslations } from "next-intl";
import { useDispatch, useSelector } from "react-redux";
import { selectProfileData, updateUserProfileData, selectRole, selectDepartment, setCurrentPlan } from "@/redux/slices/authSlice";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import EditIcon from "@/components/svgComponents/EditIcon";
import { getCurrentSubscription, getTransactions } from "@/services/subscription";
import { useRouter } from "next/navigation";
import { TransactionItem } from "@/interfaces/subscriptionInterfaces";
import ROUTES from "@/constants/routes";
import {
  SUBSCRIPTION_STATUS,
  SUBSCRIPTION_STATUS_DISPLAY,
  PAYMENT_STATUS,
  PAYMENT_STATUS_DISPLAY,
  PAYMENT_STATUS_CLASS,
  SUBSCRIPTION_PAYMENT_TYPE,
} from "@/constants/subscriptionConstants";
import EditProfileModal from "@/components/commonModals/EditProfileModal";
import InfiniteScroll from "react-infinite-scroll-component";
import { DEFAULT_LIMIT, PERMISSION } from "@/constants/commonConstants";
import { useHasPermission } from "@/utils/permission";
import CommonTableSkelton from "../accessManagement/CommonTableSkelton";
import { RootState } from "@/redux/store";
import CopyIcon from "@/components/svgComponents/CopyIcon";
import InfoIcon from "@/components/svgComponents/InfoIcon";
import { useTranslate } from "@/utils/translationUtils";
import LogoUploadModal from "@/components/commonModals/LogoUploadModal";
import chalk from "chalk";
const UserProfile = () => {
  const router = useRouter();

  // const [selectedTab, setSelectedTab] = useState(true);
  const [showEditModal, setShowEditModal] = useState<boolean>(false);
  const [imageError, setImageError] = useState<boolean>(false);
  const t = useTranslations();
  const translate = useTranslate();
  const dispatch = useDispatch();
  const navigate = useRouter();

  // Get user profile data from Redux store
  const userProfile = useSelector(selectProfileData);
  const userRole = useSelector(selectRole);
  const userDepartment = useSelector(selectDepartment);
  const [isLoading, setIsLoading] = useState(false);
  const [showUploadLogoModal, setShowUploadLogoModal] = useState<boolean>(false);

  const myCurrentSubscriptionPlan = useSelector((state: RootState) => state.auth.currentPlan);
  // Permission checks using the simplified hook
  const hasViewSubscriptionPermission = useHasPermission(PERMISSION.VIEW_SUBSCRIPTION_PLAN);
  const hasManageSubscriptionPermission = useHasPermission(PERMISSION.MANAGE_SUBSCRIPTIONS);

  const fetchUserProfile = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await getMyProfile();

      console.log("Us+++++er++-------Profile", response);

      // Based on the provided response structure
      const responseData = response?.data;

      if (responseData?.success && responseData?.data) {
        // Extract the actual user profile data and store in Redux
        dispatch(
          updateUserProfileData({
            first_name: responseData.data.firstName,
            last_name: responseData.data.lastName,
            image: responseData.data.image,
            logo: responseData.data.logo || null,
          })
        );
      }
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  }, [dispatch]);

  // Always fetch profile data when component mounts
  useEffect(() => {
    fetchUserProfile();
  }, [fetchUserProfile]);

  // Reset image error when user profile changes
  useEffect(() => {
    setImageError(false);
  }, [userProfile?.image]);

  const [transactions, setTransactions] = useState<TransactionItem[]>([]);
  const [isLoadingTransactions, setIsLoadingTransactions] = useState(false);
  const [transactionOffset, setTransactionOffset] = useState(0);
  const [hasMoreTransactions, setHasMoreTransactions] = useState(true);
  const [scriptContent, setScriptContent] = useState<string>("");
  const [isGeneratingScript, setIsGeneratingScript] = useState<boolean>(false);

  // Cancel plan functionality removed - moved to BuySubscription.tsx

  // Navigate to the buy Subscription page with the current plan information
  const handleChangePlan = () => {
    router.push(ROUTES.BUY_SUBSCRIPTION);
  };

  const fetchTransactions = async (currentOffset = 0, reset = false) => {
    try {
      setIsLoadingTransactions(true);

      // For debugging
      console.log("Fetching with offset:", currentOffset);

      // API supports pagination with limit and offset parameters
      const response = await getTransactions({
        limit: DEFAULT_LIMIT,
        offset: currentOffset,
      });

      if (response?.data?.success && response?.data?.data) {
        const { transactions: transactionsFetched, pagination } = response.data.data;

        // For debugging
        console.log("Received transactions:", transactionsFetched.length);
        console.log("Pagination info:", pagination);

        // If reset is true, replace the transactions array, otherwise append to it
        setTransactions((prevTransactions) => (reset ? transactionsFetched : [...prevTransactions, ...transactionsFetched]));

        // Calculate the total transactions loaded so far (current + new)
        const totalLoaded = (reset ? 0 : currentOffset) + transactionsFetched.length;

        // Check if we've loaded all transactions by comparing with the total count
        // from the pagination info
        const allTransactionsLoaded = totalLoaded >= pagination.total;
        console.log("Total loaded:", totalLoaded, "out of", pagination.total);

        // Update hasMore based on whether there are more transactions to load
        setHasMoreTransactions(!allTransactionsLoaded);

        // Calculate the next offset - make sure it's incremented correctly
        const nextOffset = currentOffset + transactionsFetched.length;
        console.log("Setting next offset to:", nextOffset);

        // Update the offset for the next fetch
        setTransactionOffset(nextOffset);
      } else {
        setHasMoreTransactions(false);
        const errorMessage = response?.data?.message || "failed_to_load_transactions";
        toastMessageError(translate(errorMessage));
      }
    } catch (error) {
      console.error("Error fetching transactions:", error);
      toastMessageError(t("error_fetching_transactions"));
      setHasMoreTransactions(false);
    } finally {
      setIsLoadingTransactions(false);
    }
  };

  const fetchSubscription = async () => {
    try {
      setIsLoading(true);
      const response = await getCurrentSubscription();
      if (response.data?.success) {
        dispatch(setCurrentPlan(response.data.data));
      } else {
        const errorMessage = response.data?.message || "failed_to_fetch_subscription_data";
        toastMessageError(translate(errorMessage));
      }
    } catch (error) {
      console.error("Error fetching subscription:", error);
      toastMessageError(t("error_fetching_subscription"));
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch subscription data on component mount
  useEffect(() => {
    fetchSubscription();
    fetchTransactions(0, true); // Initialize with offset 0 and reset=true
  }, []);

  const handleGenerateScript = async () => {
    try {
      setIsGeneratingScript(true);
      const response = await generateJobPortalScript();
      if (response.data?.success) {
        const scriptContent = response?.data?.data;
        setScriptContent(scriptContent);
      } else {
        const errorMessage = response?.data?.message || "failed_to_generate_script";
        toastMessageError(translate(errorMessage));
      }
    } catch (error) {
      console.error("Error generating job portal script:", error);
      toastMessageError(t("error_generating_script"));
    } finally {
      setIsGeneratingScript(false);
    }
  };

  const copyScriptToClipboard = () => {
    if (scriptContent) {
      navigator.clipboard
        .writeText(scriptContent)
        .then(() => {
          toastMessageSuccess(t("script_copied"));
        })
        .catch((err) => {
          console.error("Error copying text: ", err);
        });
    }
  };

  // Render the main content of the profile page

  const renderMainContent = () => (
    <div className={style.conduct_interview_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section d-flex justify-content-between align-items-center">
            <div className="main-heading">
              <h2>
                My <span>Profile</span>
              </h2>
            </div>
            <div className="user-profile-btn">
              <Button
                onClick={(e) => {
                  e.preventDefault();
                  setShowEditModal(true);
                }}
                className="clear-btn text-btn primary p-0 m-0"
              >
                <EditIcon className="me-2 py-1" fillNone />
                {t("edit_profile")}
              </Button>
              <Button
                onClick={(e) => {
                  e.preventDefault();
                  setShowUploadLogoModal(true);
                }}
                className="clear-btn text-btn primary p-0 m-0"
              >
                <EditIcon className="me-2 py-1" fillNone />
                {t("upload_logo")}
              </Button>
              {/* <Button
                onClick={() => {
                  navigate.push(ROUTES.INTERVIEW.CALENDAR);
                }}
                className="clear-btn text-btn primary p-0 m-0"
              >
                <ChatIcon className="me-2" />
                {t("my_interviews")}
              </Button> */}
            </div>
          </div>
        </div>
        <div className="inner-section profile-section">
          <div className="candidate-profile user-profile">
            {isLoading ? (
              <div className="position-relative">
                <div style={{ width: "100px", height: "100px", borderRadius: "12px", overflow: "hidden" }}>
                  <Skeleton width={100} height={100} />
                </div>
              </div>
            ) : (
              <div className="position-relative">
                {userProfile?.image && userProfile.image !== "" && !imageError ? (
                  <Image
                    src={userProfile.image}
                    alt="profile image"
                    className="candidate-image"
                    width={100}
                    height={100}
                    onError={() => setImageError(true)}
                  />
                ) : (
                  <div className="position-relative d-inline-block" style={{ width: "100px", height: "100px" }}>
                    <div className={`candidate-image ${style.profile_image_fallback}`}>
                      {getUserInitials(userProfile?.first_name, userProfile?.last_name)}
                    </div>
                    {/* Overlay with edit icon similar to EditProfileModal */}
                    <div
                      className="position-absolute top-0 start-0 w-100 h-100"
                      style={{ backgroundColor: "rgba(0, 0, 0, 0.8)", cursor: "pointer", borderRadius: "12px" }}
                      onClick={() => setShowEditModal(true)}
                    >
                      <div className="position-absolute d-flex justify-content-center align-items-center w-100 h-100">
                        <div
                          style={{
                            backgroundColor: "transparent",
                            display: "flex",
                            flexDirection: "column",
                            justifyContent: "center",
                            alignItems: "center",
                            gap: "6px",
                            padding: "8px",
                          }}
                        >
                          <EditIcon fillNone fillColor="white" />
                          <span style={{ color: "white", fontSize: "12px", fontWeight: 300, lineHeight: "1.3", textAlign: "center" }}>
                            {t("update")}
                            <br />
                            {t("picture")}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
            <div>
              {isLoading ? (
                <>
                  <Skeleton width={150} height={25} />
                  <Skeleton width={120} height={20} style={{ marginTop: "15px" }} />
                </>
              ) : (
                <>
                  <h3 className="candidate-name">{userProfile ? `${userProfile.first_name} ${userProfile.last_name}`.trim() : "User"}</h3>
                  <h3 className="candidate-role">{userRole?.roleName || "Role Not Assigned"}</h3>
                </>
              )}
            </div>
          </div>
          <div className="candidate-info user-info-md">
            <div className="info-container">
              <div className="info-item">
                <p className="info-title ">{t("email_address")}</p>
                {isLoading ? <Skeleton width={150} /> : <p className="info-value ">{userProfile?.email}</p>}
              </div>
              <div className="info-item">
                <p className="info-title ">{t("organization_name")}</p>
                {isLoading ? <Skeleton width={150} /> : <p className="info-value ">{userProfile?.organizationName}</p>}
              </div>
              <div className="info-item">
                <p className="info-title ">{t("organization_code")}</p>
                {isLoading ? <Skeleton width={150} /> : <p className="info-value ">{userProfile?.organizationCode}</p>}
              </div>
              <div className="info-item">
                <p className="info-title ">{t("department")}</p>
                {isLoading ? <Skeleton width={150} /> : <p className="info-value ">{userDepartment?.departmentName || "Not Available"}</p>}
              </div>
              <div className="info-item">
                <p className="info-title ">{t("account_created_on")}</p>
                {isLoading ? (
                  <Skeleton width={150} />
                ) : (
                  <p className="info-value ">{userProfile?.createdTs ? formatDate(userProfile.createdTs) : "Not Available"}</p>
                )}
              </div>
            </div>
          </div>

          <div className="row py-5 g-5">
            {hasViewSubscriptionPermission || hasManageSubscriptionPermission ? (
              <div className="col-lg-6 col-md-12 col-sm-12">
                <div className="plan-info-card h-100">
                  <div className="row align-items-center g-4">
                    <div className="col">
                      <h3 className="plan-title">{myCurrentSubscriptionPlan?.subscriptionPlanName}</h3>{" "}
                      <div className="plan-status-container">
                        <span
                          className={`plan-status ${myCurrentSubscriptionPlan?.status === SUBSCRIPTION_STATUS.CANCEL_AT_PERIOD_END ? "inactive" : "active"}`}
                        >
                          {myCurrentSubscriptionPlan?.status === SUBSCRIPTION_STATUS.CANCEL_AT_PERIOD_END
                            ? SUBSCRIPTION_STATUS_DISPLAY.CANCELED
                            : SUBSCRIPTION_STATUS_DISPLAY.ACTIVE}
                        </span>
                        <p className="plan-price">
                          $
                          {myCurrentSubscriptionPlan?.subscriptionPlanPaymentType === SUBSCRIPTION_PAYMENT_TYPE.FREE
                            ? "0.00"
                            : myCurrentSubscriptionPlan?.price}
                          <span className=""> Monthly</span>
                          <span className="plan-access-text"> {myCurrentSubscriptionPlan?.subscriptionPlanPaymentType}</span>
                        </p>
                      </div>
                      {myCurrentSubscriptionPlan?.status === SUBSCRIPTION_STATUS.CANCEL_AT_PERIOD_END ? (
                        <p className="plan-expiry">
                          <span className="label">
                            {myCurrentSubscriptionPlan?.subscriptionPlanPaymentType === SUBSCRIPTION_PAYMENT_TYPE.FREE
                              ? t("plan_expires_on")
                              : t("expires_on")}
                          </span>{" "}
                          {myCurrentSubscriptionPlan?.expiryDate
                            ? new Date(myCurrentSubscriptionPlan.expiryDate).toLocaleDateString("en-US", {
                                year: "numeric",
                                month: "long",
                                day: "numeric",
                              })
                            : "-"}
                        </p>
                      ) : (
                        <p className="plan-billing">
                          <span className="label">
                            {myCurrentSubscriptionPlan?.subscriptionPlanPaymentType === SUBSCRIPTION_PAYMENT_TYPE.FREE
                              ? t("plan_expires_on")
                              : t("next_billing_cycle")}
                          </span>{" "}
                          {myCurrentSubscriptionPlan?.nextBillingDate
                            ? new Date(myCurrentSubscriptionPlan.nextBillingDate).toLocaleDateString("en-US", {
                                year: "numeric",
                                month: "long",
                                day: "numeric",
                              })
                            : "-"}
                        </p>
                      )}
                    </div>
                    <div className="col-md-4">
                      {hasManageSubscriptionPermission && (
                        <div className="plan-actions">
                          <Button className="primary-btn rounded-md w-100 minWidth" onClick={handleChangePlan}>
                            Change Plan
                          </Button>
                          {/* <Button className="dark-outline-btn rounded-md w-100">Cancel Plan</Button> */}
                          {/* Cancel plan button removed from here - functionality moved to BuySubscription.tsx */}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ) : null}
            <div className="col-lg-6 col-md-12 col-sm-12">
              <div className="script-generate-card h-100">
                <div className="script-header">
                  <h3 className="script-title">
                    Create Job Listing Script{" "}
                    <InfoIcon
                      tooltip="Click on ‘Generate Script’ to generate custom script. Copy and paste it onto your website to let candidates apply directly, with resumes automatically pulled into S9 InnerView for seamless screening."
                      id="IdealCandidateTraits"
                      place="right"
                      ToolTipMaxWidth={300}
                    />
                  </h3>
                </div>

                <div className="script-generator">
                  {!scriptContent ? (
                    <Button
                      className="primary-btn rounded-md"
                      loading={isGeneratingScript}
                      onClick={() => handleGenerateScript()}
                      disabled={isGeneratingScript}
                    >
                      Generate Script
                    </Button>
                  ) : (
                    <>
                      <div className="script-card">{scriptContent}</div>
                      <div onClick={() => copyScriptToClipboard()}>
                        <CopyIcon className="copy-icon" />
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>

          {(hasViewSubscriptionPermission || hasManageSubscriptionPermission) && (
            <div className="section-heading">
              <h2>Billing History</h2>
              <div className="table-responsive" id="transactionsScrollableDiv">
                <InfiniteScroll
                  dataLength={transactions.length}
                  next={() => fetchTransactions(transactionOffset)}
                  hasMore={hasMoreTransactions}
                  height={window.innerHeight - 300}
                  loader={
                    isLoadingTransactions && (
                      <table className="table w-100">
                        <tbody className="text-center">
                          <CommonTableSkelton Rowcount={3} ColumnCount={6} ColumnWidth="16%" center />
                        </tbody>
                      </table>
                    )
                  }
                  endMessage={
                    !isLoadingTransactions &&
                    transactions.length > 0 && (
                      <table className="table w-100">
                        <tbody>
                          <tr>
                            <td colSpan={7} style={{ textAlign: "center", backgroundColor: "#fff" }}>
                              No more transactions
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    )
                  }
                >
                  <table className="table w-100 overflow-auto mb-0">
                    <thead>
                      <tr>
                        <th style={{ width: "22%", textAlign: "left" }}>Invoice ID</th>
                        <th style={{ width: "10%", textAlign: "left" }}>Plan</th>
                        <th style={{ width: "14%", textAlign: "center" }}>Transaction Method</th>
                        <th style={{ width: "14%", textAlign: "center" }}>Amount</th>
                        <th style={{ width: "14%", textAlign: "center" }}>Duration</th>
                        <th style={{ width: "14%", textAlign: "center" }}>Payment Date</th>
                        <th style={{ width: "14%", textAlign: "center" }}>Status</th>
                      </tr>
                    </thead>
                    {transactions.length > 0 ? (
                      <tbody>
                        {transactions.map((transaction) => (
                          <tr key={transaction.id}>
                            <td style={{ width: "22%", textAlign: "left" }}>{transaction.invoice_id ? transaction.invoice_id : "-"}</td>
                            <td style={{ width: "10%", textAlign: "left" }}>{transaction.plan_name}</td>
                            <td style={{ width: "14%", textAlign: "center" }}>{transaction.transaction_method}</td>
                            <td style={{ width: "14%", textAlign: "center" }}>${parseFloat(transaction.amount)}</td>
                            <td style={{ width: "14%", textAlign: "center" }}>Monthly</td>
                            <td style={{ width: "14%", textAlign: "center" }}>{formatDate(transaction.transaction_date)}</td>
                            <td style={{ width: "14%", textAlign: "center" }}>
                              <span
                                className={`${transaction.payment_status === PAYMENT_STATUS.SUCCESS ? PAYMENT_STATUS_CLASS.SUCCESS : PAYMENT_STATUS_CLASS.FAILED}`}
                              >
                                {transaction.payment_status === PAYMENT_STATUS.SUCCESS ? PAYMENT_STATUS_DISPLAY.PAID : PAYMENT_STATUS_DISPLAY.FAILED}
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    ) : (
                      !isLoadingTransactions && (
                        <tbody>
                          <tr>
                            <td colSpan={7} style={{ textAlign: "center", backgroundColor: "#fff" }}>
                              <p>No transaction records found</p>
                            </td>
                          </tr>
                        </tbody>
                      )
                    )}
                  </table>
                </InfiniteScroll>
              </div>
            </div>
          )}
        </div>
      </div>
      {showEditModal && (
        <EditProfileModal
          onClickCancel={() => setShowEditModal(false)}
          onSubmitSuccess={() => {
            setShowEditModal(false);
            setIsLoading(true); // Set loading state to true to show skeleton
            // Add a small delay before fetching to ensure skeleton is visible
            setTimeout(() => {
              fetchUserProfile(); // Refresh user profile data after update
            }, 500);
          }}
        />
      )}
      {showUploadLogoModal && (
        <LogoUploadModal
          onClickCancel={() => setShowUploadLogoModal(false)}
          onSubmitSuccess={() => {
            setShowUploadLogoModal(false);
            setIsLoading(true); // Set loading state to true to show skeleton
            // Add a small delay before fetching to ensure skeleton is visible
            setTimeout(() => {
              fetchUserProfile(); // Refresh user profile data after update
            }, 500);
          }}
        />
      )}
      {/* Cancellation Confirmation Modal removed - moved to BuySubscription.tsx */}
    </div>
  );
  return renderMainContent();
};

export default UserProfile;
