import React from "react";

function DownloadResumeIcon({ className }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} width="24" height="23" viewBox="0 0 32 32" fill="#436EB6">
      <g>
        <path
          d="M20.0625 -0.162109C20.3006 -0.161974 20.5276 -0.0563589 20.6914 0.116211H20.6904L28.5752 8.38672L28.5771 8.38867L28.6768 8.51855C28.7671 8.65777 28.8261 8.82269 28.8262 8.99609V27.7402C28.8262 30.0992 26.8799 32.0371 24.5215 32.0371H7.5791C5.22064 32.0371 3.27441 30.0992 3.27441 27.7402V4.13477C3.27441 1.77607 5.22064 -0.162109 7.5791 -0.162109H20.0625ZM7.5791 1.58301C6.18101 1.58301 5.01953 2.74487 5.01953 4.13477V27.7402C5.01953 29.1378 6.18845 30.292 7.5791 30.292H24.5215C25.9198 30.292 27.0811 29.138 27.0811 27.7402V9.85352H22.6904C20.7573 9.85352 19.1963 8.30187 19.1963 6.36816V1.58301H7.5791ZM20.9424 6.36816C20.9424 7.33265 21.7248 8.1084 22.6904 8.1084H25.8887L20.9424 2.91504V6.36816Z"
          strokeWidth="0.2"
        />
        <path
          d="M22.4121 24.959C22.8993 24.9592 23.292 25.351 23.292 25.832C23.2917 26.3119 22.8998 26.7038 22.4199 26.7041H9.68945C9.20934 26.7041 8.81667 26.3121 8.81641 25.832C8.81641 25.3515 9.2092 24.959 9.68945 24.959H22.4121Z"
          strokeWidth="0.2"
        />
        <path
          d="M16.0498 10.4824C16.5301 10.4824 16.9229 10.8749 16.9229 11.3555V19.542L19.9424 16.3027C20.2651 15.9542 20.8224 15.928 21.1729 16.2598L21.2354 16.3223C21.5055 16.6292 21.5268 17.0958 21.2744 17.4238L21.2168 17.4912L16.6875 22.3525L16.6865 22.3535C16.5222 22.5264 16.2954 22.6318 16.0498 22.6318C15.8044 22.6317 15.5775 22.5264 15.4131 22.3535V22.3525L10.8906 17.4912V17.4902C10.5584 17.1393 10.5871 16.5904 10.9336 16.2607C11.2626 15.9481 11.767 15.9532 12.1006 16.2412L12.165 16.3027L12.166 16.3037L15.1768 19.54V11.3555C15.1768 10.875 15.5697 10.4826 16.0498 10.4824Z"
          strokeWidth="0.2"
        />
      </g>
      <defs>
        <clipPath id="clip0_9593_1697">
          <rect width="32" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default DownloadResumeIcon;
