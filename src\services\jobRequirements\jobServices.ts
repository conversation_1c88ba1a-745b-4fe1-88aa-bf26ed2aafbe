import endpoint from "@/constants/endpoint";
import * as http from "@/utils/http";
import { ApiResponse, IApiResponseCommonInterface } from "@/interfaces/commonInterfaces";
import { GenerateJobSchema } from "@/validations/jobRequirementsValidations";

/**
 * Saves job details to the backend
 * @param formData Job requirement form data
 * @returns Job details response
 */
export const saveJobDetails = (formData: GenerateJobSchema): Promise<ApiResponse> => {
  console.log("saveJobDetails api services===========>>>>>>>>>>>>>>", formData);
  return http.post(endpoint.jobRequirements.SAVE_JOB_DETAILS, formData);
};

export interface Job {
  id: number;
  title: string;
  jobId: string;
  postedDate: string;
  updatedDate: string;
  isActive: boolean;
  applicationCount?: number;
  finalJobDescriptionHtml: string;
}

export type JobsApiResponse = ApiResponse<Job[]>;

/**
 * Fetch paginated jobs metadata from the API
 * @param data Object with pagination and filter params
 * @returns Promise resolving to JobsApiResponse
 */
export const fetchJobsMeta = (data: {
  page?: number;
  limit?: number;
  searchStr?: string;
  isActive?: boolean;
  applicationCount?: number;
}): Promise<IApiResponseCommonInterface<Job[]>> => {
  // Use 'params' to send query parameters in axios GET
  return http.get(endpoint.jobRequirements.GET_JOBS_META, { ...data });
};

/**
 * Fetches the HTML description of a job
 * @param id Job ID
 * @returns Promise resolving to ApiResponse<Job>
 */
export const getJobHtmlDescription = (id: string): Promise<ApiResponse<Job>> => {
  return http.get(endpoint.jobRequirements.GET_JOB_HTML_DESCRIPTION, { id });
};

/**
 * Updates the HTML description of a job
 * @param htmlData Object containing job ID and HTML description
 * @returns Promise resolving to ApiResponse<Job>
 */
export const updateJobDescription = (htmlData: { jobId: number; finalJobDescriptionHtml: string }): Promise<ApiResponse<Job>> => {
  return http.put(endpoint.jobRequirements.UPDATE_JOB_DESCRIPTION, htmlData);
};

/**
 * Generates PDF from job title and editor content
 * @param data Object containing job title and editor content
 * @returns Promise resolving to ApiResponse
 */
export const generatePDF = (data: { jobTitle: string; editorContent: string }): Promise<ApiResponse> => {
  return http.post(endpoint.jobRequirements.GENERATE_PDF, data);
};
