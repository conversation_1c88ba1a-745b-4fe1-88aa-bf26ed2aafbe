@use "../abstracts" as *;

.theme-modal {
  backdrop-filter: blur(3px);
  background: rgba(#436eb6, 0.5);
  &.show-modal {
    display: block;
  }

  .modal-dialog.modal-xl {
    max-width: 1000px;
    width: 90%;
  }
  .modal-dialog {
    .modal-content {
      border-radius: 16px;
      background-color: $white;
      border: 0px;
      box-shadow:
        0px 7px 2px 0px rgba(0, 0, 0, 0),
        0px 5px 2px 0px rgba(0, 0, 0, 0.01),
        0px 3px 2px 0px rgba(0, 0, 0, 0.02),
        0px 1px 1px 0px rgba(0, 0, 0, 0.03),
        0px 0px 1px 0px rgba(0, 0, 0, 0.04);

      .modal-header {
        border: 0px;
        padding: 20px;
        position: relative;
        display: block;
        text-align: center;
        &.secondary-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-bottom: 20px;
          border-bottom: 1px solid rgba($dark, 0.1);
        }

        h2 {
          color: $dark;
          font-size: 26px;
          line-height: normal;
          margin: 15px 0px 15px;
          padding: 0px;
          font-weight: 600;
        }
        h4 {
          color: $dark;
          font-size: 26px;
          line-height: normal;
          margin: 10px 0;
          padding: 0px;
          font-weight: 600;
          span {
            color: $primary;
          }
        }
        p {
          max-width: 70%;
          margin: 0 auto;
          color: $dark;
          font-size: 14px;
          font-weight: 500;
          &.textMd {
            font-size: $text-md;
          }
          &.w100 {
            width: 100%;
            max-width: 100%;
          }
        }

        .modal-close-btn {
          padding: 0px;
          background-color: transparent;
          margin: 0px;
          position: absolute;
          top: -13px;
          right: -9px;
          border: 0px;
          z-index: 100;
          svg {
            width: 30px;
            height: 30px;
            min-width: 30px;
          }
          &.fade-close {
            filter: contrast(0.4);
          }
        }
      }

      .modal-body {
        padding: 20px;

        .information-preview {
          .preview-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 18px;
          }

          .preview-section {
            margin-bottom: 16px;

            .preview-section-title {
              font-size: 20px;
              font-weight: 600;
              margin-bottom: 12px;
              color: $primary;
            }

            .preview-list {
              padding-left: 20px;

              .preview-item {
                font-size: 16px;
                margin-bottom: 8px;
                font-weight: 500;
              }
            }
          }
        }

        .alert-warning {
          font-size: 16px;
          line-height: 1.5;
          font-weight: 500;
        }

        .action-btn {
          display: flex;
          align-items: center;
          gap: 15px;

          .theme-button {
            min-width: 120px;
            font-size: 16px;
          }
        }
      }
    }
  }
  &.applications-sources-modal {
    .modal-body {
      .applications-list {
        margin-bottom: 10px;
        .item {
          display: flex;
          align-items: center;
          gap: 15px;
          justify-content: space-between;
          padding: 10px 0px;
          border-bottom: 1px solid rgba($black, 0.2);
          &:nth-last-child(1) {
            border-bottom: none;
          }
          .left-item {
            color: $dark;
            font-size: 16px;
            font-weight: 500;
            img {
              height: 30px;
              object-fit: contain;
            }
          }
          .item-right {
            color: $dark;
            font-size: 16px;
            font-weight: 500;
          }
        }
      }
    }
  }
  .interview-info-img {
    width: 110px;
    height: 180px;
    object-fit: contain;
    position: absolute;
    top: -60px;
    right: 60px;
    z-index: 0;
  }
  .copy-link-icon {
    width: 20px;
    height: 15px;
    fill: #333;
    margin-right: 10px;
  }
  .model-heading-lottie {
    // display: flex;
    // align-items: center;
    // justify-content: center;
    position: relative;
    .lottie-icon {
      width: 100px;
      height: 80px;
      object-fit: contain;
      position: absolute;
      top: -30px;
      right: 100px;
    }
  }
  // mobile
  @media screen and (max-width: 767px) {
    .modal-dialog {
      .modal-content {
        .modal-header {
          padding: 20px;
          &.secondary-header {
            padding-bottom: 20px;
          }

          h2 {
            font-size: 20px;
            margin: 0;
          }
          h4 {
            font-size: 20px;
            margin: 0;
          }
          p {
            max-width: 70%;
            font-size: 14px;
            &.textMd {
              font-size: $text-md;
            }
          }

          .modal-close-btn {
            top: -7px;
            right: -3px;
            svg {
              width: 20px;
              height: 20px;
              min-width: 20px;
            }
          }
        }

        .modal-body {
          padding: 20px;

          .information-preview {
            .preview-title {
              font-size: 24px;
              font-weight: 600;
              margin-bottom: 18px;
            }

            .preview-section {
              margin-bottom: 16px;

              .preview-section-title {
                font-size: 20px;
                font-weight: 600;
                margin-bottom: 12px;
                color: $primary;
              }

              .preview-list {
                padding-left: 20px;

                .preview-item {
                  font-size: 16px;
                  margin-bottom: 8px;
                  font-weight: 500;
                }
              }
            }
          }

          .alert-warning {
            font-size: 16px;
            line-height: 1.5;
            font-weight: 500;
          }

          .action-btn {
            display: flex;
            align-items: center;
            gap: 15px;

            .theme-button {
              min-width: 120px;
              font-size: 16px;
            }
          }
        }
      }
    }
    .interview-info-img {
      display: none;
    }
  }
}
.interview-details-modal {
  p {
    font-size: $text-md;
    font-weight: $regular;
    color: $dark;
    margin: 0;
    margin-bottom: 10px;
    line-height: 1;
  }
  h4 {
    font-size: $text-md;
    font-weight: $semiBold;
    color: $dark;
    margin: 0;
    margin-bottom: 10px;
  }
  h5 {
    font-size: $text-md;
    font-weight: $semiBold;
    color: $dark;
    margin: 0;
    line-height: 1;
    word-wrap: break-word;
  }
  .high-light-text {
    background: rgba($secondary, 0.3);
    color: $dark;
    padding: 7px 12px;
    border-radius: 8px;
    font-size: $text-sm;
    display: inline-block;
    // line-height: 1;
    &.active-link {
      cursor: pointer;
      align-items: center;
      display: inline-flex;
      gap: 5px;
      svg {
        fill: $primary;
        text-decoration: underline;
      }
      &:hover {
        text-decoration: underline;
        svg {
          fill: $primary;
          text-decoration: underline;
        }
      }
    }
  }
  a {
    font-size: $text-md;
    color: $primary;
    line-height: 1;
  }
  sup {
    color: $danger;
  }
}
// follow-up-modal style
.follow-up-modal {
  width: 35vw;
  position: fixed;
  bottom: 5vw;
  right: 5vw;
  background: $white;
  padding: 1.4vw;
  border-radius: 1vw;
  box-shadow:
    0px 7px 2px 0px rgba(0, 0, 0, 0),
    0px 5px 2px 0px rgba(0, 0, 0, 0.01),
    0px 3px 2px 0px rgba(0, 0, 0, 0.02),
    0px 1px 1px 0px rgba(0, 0, 0, 0.03),
    0px 0px 1px 0px rgba(0, 0, 0, 0.04);
  z-index: 100;

  .follow-up-container {
    .sub-title {
      font-size: 0.8vw;
      font-weight: $regular;
      color: $dark;
      margin: 0;
      margin-bottom: 1vw;
      display: flex;
      align-items: center;
      gap: 0.5vw;
    }
    h4 {
      font-size: 1.1vw;
      font-weight: $bold;
      color: $dark;
      margin: 0;
      margin-bottom: 0.8vw;
      span {
        color: $primary;
      }
    }
    .action-btn {
      display: flex;
      align-items: center;
      gap: 1vw;
      margin-top: 1.8vw;
      button {
        font-size: 0.8vw;
        padding: 0.6vw 1vw;
        border-radius: 0.7vw;
      }
    }
    .question-group {
      .question-label {
        font-weight: $semiBold;
        font-size: clamp(1rem, 1.2vw, 1.4rem);
        margin-bottom: 0.5vw;
        color: $dark;
      }
      .radio-text {
        line-height: 1.5;
        font-size: clamp(0.95rem, 1.1vw, 1.3rem);
        color: #333;
      }
      .radio-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 0.5vw;
      }
      .radio-item input[type="radio"] {
        margin-right: 10px;
        margin-top: 3px;
        accent-color: $primary;
        width: 1vw;
        min-width: 1vw;
        height: 1vw;
        min-height: 1vw;
      }
      .radio-item input[type="checkbox"] {
        margin-right: 10px;
        margin-top: 5px;
        accent-color: $primary;
        width: 15px;
        min-width: 15px;
        height: 15px;
        min-height: 15px;
      }
      .radio-text {
        line-height: 1.5;
        color: $dark;
        font-weight: $medium;
        font-size: clamp(0.95rem, 1.1vw, 1.4rem);
      }
    }
  }
}

// calendar-more-events-modal styles

.calendar-more-events-modal {
  width: 0%;
  max-width: 0;
  min-width: 0;
  position: absolute;
  top: 105px;
  bottom: 3px;
  right: 3px;
  background: rgba($white, 1);
  border-radius: 0px 8px 8px 0;
  z-index: 100;
  // box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
  box-shadow:
    rgba(50, 50, 93, 0.25) 0px 13px 27px -5px,
    rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
  &.open {
    width: 25%;
    transition: all 0.5s ease-in-out;
    max-width: 320px;
    min-width: 300px;
  }
  .events-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: solid 1px rgba($dark, 0.2);
    p {
      font-size: 1.4rem;
      font-weight: $medium;
      margin-bottom: 0;
    }
    .date-day {
      font-size: 1.2rem;
      font-weight: $medium;
      color: rgba($dark, 0.6);
      margin-bottom: 0;
    }
  }
  .events-modal-body {
    overflow: auto;
    max-height: calc(100% - 70px);
    overflow-x: hidden;
    .events-list-card {
      padding: 10px 15px;
      border-bottom: dashed 1px rgba($dark, 0.1);
      padding: 10px 15px;
      border-bottom: 1px dashed #3333;
      display: flex;
      align-items: center;
      gap: 6px;
      cursor: pointer;
      .time-text {
        padding: 4px 5px;
        background: green;
        display: inline-block;
        color: #fff;
        border-radius: 5px;
        line-height: 1;
        font-size: 10px;
        white-space: nowrap;
        min-width: 46px;
        text-align: center;
      }
      .event-text {
        font-size: 1.4rem;
        font-weight: $medium;
        color: rgba($dark, 0.8);
        line-height: 1.2;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        width: 100%;
        margin-bottom: 0;
      }
      &.future {
        .time-text {
          background: rgba($primary, 0.1);
          color: $dark;
          border: 1px solid $primary;
        }
        .event-text {
          color: $dark;
        }
        &.canceledInterview {
          .time-text {
            background: rgba($dark, 0.1);
            color: $dark;
            border: 1px dashed $dark;
          }
          .event-text {
            color: $dark;
          }
        }
      }
      &.past {
        .time-text {
          background: $white;
          color: $danger;
          border: 1px dashed $danger;
        }
        .event-text {
          color: $dark;
        }
        &.completedInterview {
          .time-text {
            background: $white;
            color: $primary;
            border: 1px dashed $primary;
          }
          .event-text {
            color: $dark;
          }
        }
        &.canceledInterview {
          .time-text {
            background: rgba($dark, 0.1);
            color: $dark;
            border: 1px dashed $dark;
          }
          .event-text {
            color: $dark;
          }
        }
      }
    }
  }
}
