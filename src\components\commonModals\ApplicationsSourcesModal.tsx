"use client";
import React from "react";
import Button from "../formElements/Button";
import LinkedinIcon from "../svgComponents/LinkedinIcon";

interface IProps {
  onCancel?: () => void;
}

const ApplicationsSourcesModal: React.FC<IProps> = ({ onCancel }) => {
  return (
    <div className="modal theme-modal show-modal applications-sources-modal">
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header justify-content-center">
            <h2 className="text-left">Applications Sources</h2>
          </div>
          <div className="modal-body">
            <div className="applications-list">
              <div className="item">
                <div className="left-item">
                  <LinkedinIcon />
                </div>
                <div className="item-right">42 Applicants</div>
              </div>
              <div className="item">
                <div className="left-item">
                  <LinkedinIcon />
                </div>
                <div className="item-right">42 Applicants</div>
              </div>
              <div className="item">
                <div className="left-item">
                  <LinkedinIcon />
                </div>
                <div className="item-right">42 Applicants</div>
              </div>
              <div className="item">
                <div className="left-item">Other</div>
                <div className="item-right">42 Applicants</div>
              </div>
            </div>

            <div className="action-btn justify-content-center">
              <Button className="primary-btn rounded-md w-100" onClick={onCancel}>
                Ok
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default ApplicationsSourcesModal;
