import { Step } from "react-joyride-react-19";

/**
 * Options for walkthrough behavior
 */
export interface WalkthroughOptions {
  /** Callback triggered when tour starts */
  onTourStart?: () => void;
  /** Callback triggered when tour ends */
  onTourEnd?: () => void;
  /** Force tour to show even if already seen */
  resetOnRevisit?: boolean;
}

/**
 * Props for the UserWalkthrough component
 */
export interface WalkthroughProps {
  /** Unique identifier for the page */
  pageId: string;
  /** Array of tour steps */
  steps: Step[];
  /** Flag indicating if page data is loaded */
  isDataLoaded: boolean;
  /** Delay in ms before auto-starting the tour */
  startDelay?: number;
  /** Whether to show a manual tour start button */
  showButton?: boolean;
  /** Additional options for walkthrough behavior */
  options?: WalkthroughOptions;
}
