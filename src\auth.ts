import { AuthOptions } from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import ROUTES from "./constants/routes";
import endpoint from "./constants/endpoint";

const auth: AuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },

      async authorize(credentials) {
        console.log("🔐 NextAuth: Starting authorization...");

        if (!credentials?.email || !credentials?.password) {
          console.log("❌ NextAuth: Missing credentials");
          throw new Error("Missing credentials");
        }

        const { email, password } = credentials as {
          email: string;
          password: string;
        };

        console.log("🔐 NextAuth: Attempting login for:", email);

        try {
          const res = await fetch(endpoint.auth.SIGNIN, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email,
              password,
            }),
          });

          console.log("🔐 NextAuth: API Response status:", res.status);

          if (!res.ok) {
            console.log("❌ NextAuth: API request failed with status:", res.status);
            throw new Error("something_went_wrong");
          }

          const user = await res.json();
          console.log("🔐 NextAuth: API Response data:", user);

          // Check if login was successful
          if (user && user.success) {
            console.log("✅ NextAuth: Login successful");
            return user;
          } else {
            // Throw error with backend message for failed login
            const errorMessage = user?.message || "Login failed";
            console.log("❌ NextAuth: Login failed -", errorMessage);
            throw new Error(errorMessage);
          }
        } catch (error) {
          console.error("❌ NextAuth: Error during authorization:", error);

          // If it's an error we threw with a specific message, preserve it
          if (error instanceof Error && error.message !== "something_went_wrong") {
            throw error;
          }

          // Otherwise, throw generic error
          throw new Error("something_went_wrong");
        }
      },
    }),
  ],
  secret: process.env.NEXTAUTH_SECRET,
  pages: {
    signIn: ROUTES.LOGIN,
  },
  callbacks: {
    async jwt({ token, user }) {
      console.log("🔐 NextAuth JWT Callback:", {
        hasUser: !!user,
        tokenSub: token.sub,
        userData: (user as unknown as { data: unknown })?.data ? "present" : "missing",
      });

      // If user is present (first time login), merge user data into token
      if (user) {
        return { ...token, ...user };
      }

      // Return existing token for subsequent requests
      return token;
    },
    async session({ session, token }) {
      console.log("🔐 NextAuth Session Callback:", {
        tokenSub: token.sub,
        hasTokenData: !!token.data,
      });

      // Pass token data to session
      session.user = token;
      return session;
    },
  },
};

export default auth;
