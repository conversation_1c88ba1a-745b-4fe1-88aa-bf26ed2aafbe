"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslate } from "@/utils/translationUtils";
// import noInternetImage from "../../../../public/assets/images/No_Internet.png";
// import Image from "next/image";
import { NETWORK_STATUS } from "@/constants/commonConstants";
import { NoInternetIcon } from "@/components/svgComponents/NoInternetIcon";
export default function NoInternetPage() {
  const [isOnline, setIsOnline] = useState(true);
  const router = useRouter();
  const t = useTranslate();

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      router.refresh();
    };

    const handleOffline = () => {
      setIsOnline(false);
    };

    window.addEventListener(NETWORK_STATUS.ONLINE, handleOnline);
    window.addEventListener(NETWORK_STATUS.OFFLINE, handleOffline);

    return () => {
      window.removeEventListener(NETWORK_STATUS.ONLINE, handleOnline);
      window.removeEventListener(NETWORK_STATUS.OFFLINE, handleOffline);
    };
  }, [router]);

  console.log("isOnline========>", isOnline);
  return (
    <div className="no-internet-page">
      <div className="no-internet-container">
        <div className="no-internet-content">
          {/* Animated Background Elements */}
          <div className="bg-elements">
            <div className="floating-circle circle-1"></div>
            <div className="floating-circle circle-2"></div>
            <div className="floating-circle circle-3"></div>
          </div>

          {/* Main Content */}
          <div className="main-content">
            <div className="image-container">
              <NoInternetIcon />
              <div className="pulse-ring"></div>
            </div>

            <div className="text-content">
              <h1 className="title">{t("no_internet_title")}</h1>
              <p className="description">{t("no_internet_desc")}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
