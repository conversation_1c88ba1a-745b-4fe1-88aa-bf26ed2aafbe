const LeaveIcon = (props: { className?: string }) => {
  const { className } = props;
  return (
    <div className={className}>
      <svg xmlns="http://www.w3.org/2000/svg" width="29" height="28" viewBox="0 0 29 28" fill="none">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M8.93179 3.50001C9.36499 1.70864 11.1689 0.500406 13.067 0.907153L22.4003 2.90715C24.0141 3.25296 25.167 4.67908 25.167 6.32946V7.00001V21V21.6706C25.167 23.3209 24.0141 24.7471 22.4003 25.0929L13.067 27.0929C11.1689 27.4996 9.36499 26.2914 8.93179 24.5H7.66699C5.734 24.5 4.16699 22.933 4.16699 21V13.4167V12.8333V7.00001C4.16699 5.06701 5.734 3.50001 7.66699 3.50001H8.93179ZM8.83366 5.83334H7.66699C7.02266 5.83334 6.50033 6.35567 6.50033 7.00001V12.8333V13.4167V21C6.50033 21.6443 7.02266 22.1667 7.66699 22.1667H8.83366V5.83334ZM13.5003 14C13.5003 14.6443 12.978 15.1667 12.3337 15.1667C11.6893 15.1667 11.167 14.6443 11.167 14C11.167 13.3557 11.6893 12.8333 12.3337 12.8333C12.978 12.8333 13.5003 13.3557 13.5003 14Z"
          fill="white"
        />
      </svg>
    </div>
  );
};

export default LeaveIcon;
