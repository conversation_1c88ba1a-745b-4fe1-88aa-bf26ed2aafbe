import { PERMISSION } from "./commonConstants";
import RO<PERSON><PERSON> from "./routes";

/**
 * Route permission mapping - Define which permissions are required for each route
 * This configuration determines access control for protected routes
 */
export const ROUTE_PERMISSION_MAP: { [key: string]: string | string[] } = {
  // User role routes
  [ROUTES.ROLE_EMPLOYEES.ROLES_PERMISSIONS]: [PERMISSION.CREATE_NEW_ROLE, PERMISSION.MANAGE_USER_PERMISSIONS],

  // Archive route (contains both candidates and jobs tabs)
  [ROUTES.JOBS.ARCHIVE]: [PERMISSION.ARCHIVE_RESTORE_CANDIDATES, PERMISSION.ARCHIVE_RESTORE_JOB_POSTS],

  // Manual Resume Screening routes - protect all paths including dynamic routes
  [ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD]: PERMISSION.MANUAL_RESUME_SCREENING,
  [ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD + "/[...path]"]: PERMISSION.MANUAL_RESUME_SCREENING,

  // Candidates List routes - require top candidates management permission
  [ROUTES.SCREEN_RESUME.CANDIDATE_LIST]: PERMISSION.MANAGE_TOP_CANDIDATES,
  [ROUTES.SCREEN_RESUME.CANDIDATE_LIST + "/[id]"]: PERMISSION.MANAGE_TOP_CANDIDATES,

  // Candidate Qualification routes - require top candidates management permission
  [ROUTES.SCREEN_RESUME.CANDIDATE_QUALIFICATION]: [PERMISSION.MANUAL_RESUME_SCREENING, PERMISSION.MANAGE_TOP_CANDIDATES],
  [ROUTES.SCREEN_RESUME.CANDIDATE_QUALIFICATION + "/[id]"]: [PERMISSION.MANUAL_RESUME_SCREENING, PERMISSION.MANAGE_TOP_CANDIDATES],

  // Employee management routes
  [ROUTES.ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT]: [PERMISSION.ADD_EMPLOYEE, PERMISSION.CREATE_NEW_DEPARTMENT],
  [ROUTES.ROLE_EMPLOYEES.EMPLOYEE_MANAGEMENT_DETAIL]: PERMISSION.ADD_EMPLOYEE,
  [ROUTES.ROLE_EMPLOYEES.ADD_EMPLOYEE]: PERMISSION.ADD_EMPLOYEE,

  // Additional candidate info routes - require additional candidate info permission
  [ROUTES.INTERVIEW.ADD_CANDIDATE_INFO]: PERMISSION.ADD_ADDITIONAL_CANDIDATE_INFO,
  [ROUTES.INTERVIEW.ADD_CANDIDATE_INFO + "/[id]"]: PERMISSION.ADD_ADDITIONAL_CANDIDATE_INFO,

  // Candidate profile routes - require candidate profile management permission
  [ROUTES.JOBS.CANDIDATE_PROFILE]: [PERMISSION.MANAGE_CANDIDATE_PROFILE, PERMISSION.VIEW_HIRED_CANDIDATES, PERMISSION.HIRE_CANDIDATE],
  [ROUTES.JOBS.CANDIDATE_PROFILE + "/[id]"]: [PERMISSION.MANAGE_CANDIDATE_PROFILE, PERMISSION.VIEW_HIRED_CANDIDATES, PERMISSION.HIRE_CANDIDATE],

  // Final assessment routes - require final assessment permission
  [ROUTES.FINAL_ASSESSMENT.FINAL_ASSESSMENT]: PERMISSION.MANAGE_CANDIDATE_PROFILE,

  // Job routes - require job post creation/editing permission
  [ROUTES.JOBS.HIRING_TYPE]: PERMISSION.CREATE_OR_EDIT_JOB_POST,
  [ROUTES.JOBS.GENERATE_JOB]: PERMISSION.CREATE_OR_EDIT_JOB_POST,
  [ROUTES.JOBS.CAREER_BASED_SKILLS]: PERMISSION.CREATE_OR_EDIT_JOB_POST,
  [ROUTES.JOBS.ROLE_BASED_SKILLS]: PERMISSION.CREATE_OR_EDIT_JOB_POST,
  [ROUTES.JOBS.CULTURE_BASED_SKILLS]: PERMISSION.CREATE_OR_EDIT_JOB_POST,
  [ROUTES.JOBS.JOB_EDITOR]: PERMISSION.CREATE_OR_EDIT_JOB_POST,
  [ROUTES.JOBS.EDIT_SKILLS]: PERMISSION.CREATE_OR_EDIT_JOB_POST,

  // Buy subscription routes
  [ROUTES.BUY_SUBSCRIPTION]: PERMISSION.MANAGE_SUBSCRIPTIONS,

  // Hired candidates routes - require either VIEW_HIRED_CANDIDATES or HIRE_CANDIDATE permission
  [ROUTES.SCREEN_RESUME.HIRED_CANDIDATES]: [PERMISSION.VIEW_HIRED_CANDIDATES, PERMISSION.HIRE_CANDIDATE],

  // Activity logs route - require view audit logs permission
  [ROUTES.ACTIVITY_LOGS]: PERMISSION.VIEW_AUDIT_LOGS_UPCOMING,
};

export default ROUTE_PERMISSION_MAP;
