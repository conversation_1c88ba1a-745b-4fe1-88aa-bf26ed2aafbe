"use client";
import Button from "@/components/formElements/Button";
import React, { use, useEffect, useState, useCallback, useMemo, useRef } from "react";
import { useRouter } from "next/navigation";
import InputWrapper from "@/components/formElements/InputWrapper";
import { useForm, Resolver } from "react-hook-form";
import LetterFoldIcon from "@/components/svgComponents/LetterFoldIcon";
import AiMarkIcon from "@/components/svgComponents/AiMarkIcon";
import EditIcon from "@/components/svgComponents/EditIcon";
import Textbox from "@/components/formElements/Textbox";
import Textarea from "@/components/formElements/Textarea";
import { conductInterviewStaticInformation, getInterviewFeedback, updateInterviewFeedback } from "@/services/interviewServices";
import { IInterviewState, setInterviewStaticInformation } from "@/redux/slices/interviewSlice";
import { toastMessageError, toastMessageSuccess, getDecryptedData } from "@/utils/helper";
import { useDispatch, useSelector } from "react-redux";
import { useTranslations } from "next-intl";
import RejectedIcon from "@/components/svgComponents/RejectedIcon";
import ApprovedIcon from "@/components/svgComponents/ApprovedIcon";
import { AIDecisionForNextRound, INTERVIEW_FEEDBACK, TOUR_STEPS } from "@/constants/commonConstants";
import DeleteIcon from "@/components/svgComponents/DeleteIcon";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import ROUTES from "@/constants/routes";
import UserWalkthrough from "@/components/commonComponent/UserWalkthrough";
import { interviewFeedbackSteps } from "@/components/commonComponent/UserWalkthrough/tourStepsConfig";
import { addAnswerValidation } from "@/validations/interviewValidations";
import { yupResolver } from "@hookform/resolvers/yup";
import { useTranslate } from "@/utils/translationUtils";
import ConfirmationModal from "@/components/commonModals/ConfirmationModal";
import ModalCloseIcon from "@/components/svgComponents/ModalCloseIcon";

// Define the form values interface with index signature for dynamic fields
interface CareerFormValues {
  behavioralInfo?: string | null;
  [key: string]: string | null | undefined; // This allows for dynamic fields like 'career-1', 'career-2', etc.
}

interface IHighlights {
  overAllFeedback: string[];
}

interface ICareerFeedback {
  id?: number;
  score: number;
  highlights: IHighlights;
}
interface SkillRecord {
  id: number;
  jobSkillId: number;
  highlights: IHighlights;
  score: number;
  skillTitle: string;
  type: string;
  createdTs: Date;
  updatedTs: Date;
}

export interface IFeedbackResponse {
  applicantAiBehavioralAnalysis: string;
  roundNumber: number;
  candidateName: string;
  jobTitle: string;
  candidateEmail: string;
  isFeedbackFilled: boolean;
  aiDecisionForNextRound: string;
}

const initialConfirmationModalData = {
  isOpen: false,
  title: "",
  message: "",
  confirmButtonText: "",
  confirmButtonClass: "primary-btn", // Default button style
};

const InterviewFeedback = ({ params }: { params: Promise<{ interviewId: string }> }) => {
  const paramsPromise = use(params);
  const t = useTranslations();
  const translate = useTranslate();
  const dispatch = useDispatch();
  const router = useRouter();
  const [interviewId, setInterviewId] = useState<number>(0);
  const initialFetchDone = useRef(false);
  const [loading, setLoading] = useState(false);
  const [processingAdvance, setProcessingAdvance] = useState(false);
  const [processingReject, setProcessingReject] = useState(false);

  const [careerFeedbackData, setCareerFeedbackData] = useState<ICareerFeedback | null>(null);
  const [roleSpecificSkillsFeedback, setRoleSpecificSkillsFeedback] = useState<SkillRecord[]>([]);
  const [cultureSpecificSkillsFeedback, setCultureSpecificSkillsFeedback] = useState<SkillRecord[]>([]);
  const [feedbackData, setFeedbackData] = useState<IFeedbackResponse | null>(null);

  const [originalCareerFeedbackData, setOriginalCareerFeedbackData] = useState<ICareerFeedback | null>(null);
  const [originalRoleSpecificSkillsFeedback, setOriginalRoleSpecificSkillsFeedback] = useState<SkillRecord[]>([]);
  const [originalCultureSpecificSkillsFeedback, setOriginalCultureSpecificSkillsFeedback] = useState<SkillRecord[]>([]);

  const [editCareerBasedSkills, setEditCareerBasedSkills] = useState(false);
  const [editBehavioralNotes, setEditBehavioralNotes] = useState(false);

  const [confirmationModal, setConfirmationModal] = useState(initialConfirmationModalData);

  const interviewStaticInformation = useSelector((state: { interview: IInterviewState }) => state.interview.interviewStaticInformation);

  const [editMode, setEditMode] = useState({
    isEdit: false,
    skillId: null as number | null,
  });

  // Career feedback form - creating a properly typed resolver
  const validationSchema = addAnswerValidation(translate);
  const {
    control: careerControl,
    setValue: careerSetValue,
    handleSubmit: careerHandleSubmit,
    getValues: careerGetValues,
    reset: careerReset,
    formState: { errors: careerErrors },
  } = useForm<CareerFormValues>({
    mode: "onChange",
    resolver: yupResolver(validationSchema) as unknown as Resolver<CareerFormValues>,
  });

  // Role and culture specific skills form
  const {
    control: skillsControl,
    setValue: skillsSetValue,
    handleSubmit: skillsHandleSubmit,
    getValues: skillsGetValues,
    reset: skillsReset,
  } = useForm();

  // Handle interviewId decryption in useEffect
  useEffect(() => {
    if (initialFetchDone.current) return;
    if (!paramsPromise?.interviewId) {
      console.error("No interviewId found in URL parameters");
      toastMessageError(translate("invalid_or_malformed_url_parameters"));
      router.back();
      return;
    }

    try {
      const decryptedData = getDecryptedData(paramsPromise.interviewId);
      console.log("decryptedData===>>>", decryptedData);
      const interviewId = decryptedData?.interviewId ? Number(decryptedData.interviewId) : Number(paramsPromise.interviewId);
      setInterviewId(interviewId);
      fetchInterviewFeedback(interviewId);
    } catch (error) {
      console.error("Error processing interviewId:", error);
      toastMessageError(translate("something_went_wrong"));
      router.back();
    }
    initialFetchDone.current = true;
  }, [paramsPromise, router]);

  const getInterviewStaticInformation = useCallback(async () => {
    try {
      const response = await conductInterviewStaticInformation();

      if (response?.data?.success) {
        dispatch(setInterviewStaticInformation(response?.data?.data));
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch {
      toastMessageError(t("something_went_wrong"));
    }
  }, [dispatch, t]);

  const aiDecisionForNextRound = useMemo(() => {
    return !feedbackData?.aiDecisionForNextRound ? null : feedbackData?.aiDecisionForNextRound;
  }, [feedbackData]);

  console.log("aiDecisionForNextRound===>>>", aiDecisionForNextRound);

  const fetchInterviewFeedback = useCallback(
    async (interviewId: number) => {
      if (!interviewId) return;

      try {
        setLoading(true);
        const response = await getInterviewFeedback(interviewId);

        if (response.data && response.data.success) {
          const careerSkills = response.data.data.careerBasedSkills;

          // Make sure role skills have the type field set properly
          const roleSkills = (response.data.data.roleSpecificSkills || []).map((skill: SkillRecord) => ({
            ...skill,
            type: "role", // Ensure type field is explicitly set to "role"
          }));

          // Make sure culture skills have the type field set properly
          const cultureSkills = (response.data.data.cultureSpecificSkills || []).map((skill: SkillRecord) => ({
            ...skill,
            type: "culture", // Ensure type field is explicitly set to "culture"
          }));

          // Set feedback data for different skill types
          setCareerFeedbackData(careerSkills);
          setRoleSpecificSkillsFeedback(roleSkills);
          setCultureSpecificSkillsFeedback(cultureSkills);

          setFeedbackData(response.data.data);

          // Show toast message if feedback is already filled
          if (response.data.data.isFeedbackFilled) {
            toastMessageSuccess("Feedback has already been submitted for this candidate.");
          }

          // Pre-populate form data with existing feedback
          if (response.data.data && response.data.data.applicantAiBehavioralAnalysis) {
            careerSetValue("behavioralInfo", response.data.data.applicantAiBehavioralAnalysis);
          }
        } else {
          const errorMessage = response.data?.message || "Failed to fetch interview feedback";
          toastMessageError(errorMessage);
        }
      } catch (error) {
        console.error("Error fetching interview feedback:", error);
        toastMessageError("Something went wrong while loading interview feedback");
      } finally {
        setLoading(false);
      }
    },
    [interviewId, careerSetValue]
  );

  // Fetch interview feedback data when component mounts or interviewId changes
  // useEffect(() => {
  //   if (initialFetchDone.current) return;
  //   if (interviewId) {
  //     fetchInterviewFeedback();
  //   }
  //   initialFetchDone.current = true;
  // }, [interviewId, fetchInterviewFeedback]);

  // Fetch static information when component mounts or interviewId changes
  useEffect(() => {
    console.log("interviewStaticInformation===>>>", interviewStaticInformation);

    if (
      interviewId &&
      (!interviewStaticInformation ||
        !interviewStaticInformation.stratumDescription ||
        !Object.keys(interviewStaticInformation.stratumDescription).length)
    ) {
      getInterviewStaticInformation();
    }
  }, []);

  if (loading) {
    return (
      <div className="container">
        <div className="common-page-header pb-4">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                Interview <span>Feedback</span>
              </h2>
            </div>
          </div>
          <div className="section-heading mt-5">
            <Skeleton height={24} width={200} borderRadius={6} />
            {/* <Skeleton height={18} width={100} borderRadius={6} className="mt-2" /> */}
            <Skeleton height={16} width={"50%"} borderRadius={6} className="mt-4" />
          </div>
        </div>
        <div className="inner-section">
          <div className="row">
            <div className="col-md-8">
              {/* Career Based Feedback Skeleton */}
              <div className="interview-summary">
                <div className="summary-header">
                  <Skeleton height={24} width={250} borderRadius={6} />
                </div>
                <Skeleton height={300} width={"100%"} borderRadius={12} className="mb-4" />
              </div>
              <div className="interview-summary">
                <div className="summary-header">
                  <Skeleton height={24} width={250} borderRadius={6} />
                </div>
                <Skeleton height={300} width={"100%"} borderRadius={12} className="mb-4" />
              </div>
            </div>
            <div className="col-md-4">
              {/* Summary and Score section skeleton */}
              <Skeleton height={550} width={"100%"} borderRadius={0} className="mb-3" />
            </div>
          </div>
        </div>
      </div>
    );
  } else if (!careerFeedbackData) {
    return (
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                Interview <span>Feedback</span>
              </h2>
            </div>
          </div>
          <div className="text-center py-5">
            <h4>No feedback data available.</h4>
          </div>
        </div>
      </div>
    );
  }

  const enableEditMode = (skillId: number) => {
    // Store the original states before entering edit mode
    setOriginalRoleSpecificSkillsFeedback(JSON.parse(JSON.stringify(roleSpecificSkillsFeedback)));
    setOriginalCultureSpecificSkillsFeedback(JSON.parse(JSON.stringify(cultureSpecificSkillsFeedback)));

    setEditMode({ isEdit: true, skillId });

    // Find and pre-populate the form fields for the selected skill
    const skill = [...roleSpecificSkillsFeedback, ...cultureSpecificSkillsFeedback].find((s) => s.id === skillId);
    if (skill && skill.highlights && skill.highlights.overAllFeedback) {
      skill.highlights.overAllFeedback.forEach((feedback, index) => {
        skillsSetValue(`skill-${skillId}-${index}`, feedback);
      });
    }
  };

  // Add a new feedback point for a specific skill
  const addSkillFeedbackPoint = (skill: SkillRecord): void => {
    if (skill && skill.highlights) {
      // Check if any existing field is empty in the form data
      const hasEmptyField = skill.highlights.overAllFeedback?.some((_, index) => {
        const fieldName = `skill-${skill.id}-${index}`;
        const value = skillsGetValues(fieldName);
        return !value || (typeof value === "string" && !value.trim());
      });
      if (hasEmptyField) {
        toastMessageError("Please fill in existing required fields before adding a new one");
        return;
      }
      // Create a deep copy of the current skill lists to ensure React detects the changes
      const roleSkillsCopy = JSON.parse(JSON.stringify(roleSpecificSkillsFeedback));
      const cultureSkillsCopy = JSON.parse(JSON.stringify(cultureSpecificSkillsFeedback));

      // Choose the correct list based on skill type
      const workingList = skill.type === "role" ? roleSkillsCopy : cultureSkillsCopy;

      const skillIndex = workingList.findIndex((s: SkillRecord) => s.id === skill.id);

      if (skillIndex !== -1) {
        // Ensure overAllFeedback is initialized if it doesn't exist
        if (!workingList[skillIndex].highlights.overAllFeedback) {
          workingList[skillIndex].highlights.overAllFeedback = [];
        }

        // Add an empty string as a new feedback point
        workingList[skillIndex].highlights.overAllFeedback.push("");

        // Set the empty value for the new field in the form
        const newIndex = workingList[skillIndex].highlights.overAllFeedback.length - 1;
        skillsSetValue(`skill-${skill.id}-${newIndex}`, "");

        // Update the appropriate state with the modified list
        if (skill.type === "role") {
          setRoleSpecificSkillsFeedback(roleSkillsCopy);
        } else {
          setCultureSpecificSkillsFeedback(cultureSkillsCopy);
        }
      }
    }
  };

  // Remove a feedback point for a specific skill
  const removeSkillFeedbackPoint = (skill: SkillRecord, indexToRemove: number): void => {
    if (skill && skill.highlights) {
      const updatedSkillsList = skill.type === "role" ? [...roleSpecificSkillsFeedback] : [...cultureSpecificSkillsFeedback];

      const skillIndex = updatedSkillsList.findIndex((s) => s.id === skill.id);

      if (skillIndex !== -1) {
        // Filter out the removed feedback point
        const updatedFeedbackPoints = updatedSkillsList[skillIndex].highlights.overAllFeedback.filter((_, index: number) => index !== indexToRemove);

        // Create updated skill object
        const updatedSkill = {
          ...updatedSkillsList[skillIndex],
          highlights: {
            ...updatedSkillsList[skillIndex].highlights,
            overAllFeedback: updatedFeedbackPoints,
          },
        };

        // Update the list
        updatedSkillsList[skillIndex] = updatedSkill;

        if (skill.type === "role") {
          setRoleSpecificSkillsFeedback(updatedSkillsList);
        } else {
          setCultureSpecificSkillsFeedback(updatedSkillsList);
        }

        // Update form values to match new indices
        updatedFeedbackPoints.forEach((feedback: string, newIndex: number) => {
          skillsSetValue(`skill-${skill.id}-${newIndex}`, feedback);
        });
      }
    }
  };

  const renderCultureAndRoleSpecificSkills = (skills: SkillRecord[]) => {
    return skills.length
      ? skills.map((feedback, index) => {
          return editMode.isEdit && editMode.skillId === feedback.id ? (
            <div className={index === 0 ? "interview-summary-inner-card saprator-none" : "interview-summary-inner-card"} key={feedback.id}>
              <div className="section-heading">
                <h2>{feedback.skillTitle}</h2>
              </div>
              {feedback.highlights && feedback.highlights?.overAllFeedback?.length
                ? feedback.highlights &&
                  feedback.highlights?.overAllFeedback?.map((feedbackPoint, index) => (
                    <InputWrapper key={index}>
                      <div className="d-flex align-items-center justify-content-between">
                        <InputWrapper.Label htmlFor={`skill-${feedback.id}-${index}`} required>
                          Point {index + 1 < 10 ? `0${index + 1}` : index + 1}
                        </InputWrapper.Label>
                        <Button type="button" className="clear-btn text-danger p-0" onClick={() => removeSkillFeedbackPoint(feedback, index)}>
                          <DeleteIcon className="p-2" />
                        </Button>
                      </div>
                      <Textbox
                        className="form-control"
                        control={skillsControl}
                        name={`skill-${feedback.id}-${index}`}
                        type="text"
                        placeholder="Enter your Feedback"
                      ></Textbox>
                    </InputWrapper>
                  ))
                : null}

              <Button className="clear-btn secondary p-0 mb-4" onClick={() => addSkillFeedbackPoint(feedback)}>
                +Add Another Point
              </Button>

              <div className="section-heading">
                <h2 className="m-0">Stratum Score</h2>
              </div>
              <ul className="number-task">
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((item, index) => (
                  <li
                    className={item === 10 ? (feedback.score === item ? "extreme active" : "extreme") : feedback.score === item ? "active" : ""}
                    key={index}
                    onClick={() => {
                      // Update the skill score when clicked
                      const updatedSkillsList = feedback.type === "role" ? [...roleSpecificSkillsFeedback] : [...cultureSpecificSkillsFeedback];
                      const skillIndex = updatedSkillsList.findIndex((s) => s.id === feedback.id);

                      if (skillIndex !== -1) {
                        // Create updated skill object with new score
                        const updatedSkill = {
                          ...updatedSkillsList[skillIndex],
                          score: item,
                        };

                        // Update the appropriate list
                        updatedSkillsList[skillIndex] = updatedSkill;

                        if (feedback.type === "role") {
                          setRoleSpecificSkillsFeedback(updatedSkillsList);
                        } else {
                          setCultureSpecificSkillsFeedback(updatedSkillsList);
                        }
                      }
                    }}
                    style={{ cursor: "pointer" }}
                  >
                    {item === 10 ? "Extreme" : item}
                  </li>
                ))}
              </ul>
              <p className="number-task-text">{interviewStaticInformation.stratumDescription[feedback.score]}</p>
              <div className="d-flex mt-5">
                <Button type="button" className="secondary-btn button-sm minWidth rounded-md" onClick={() => saveSkillFeedback(feedback)}>
                  Save
                </Button>
                <Button type="button" className="dark-outline-btn button-sm minWidth rounded-md ms-3" onClick={() => onCancelEditMode()}>
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <div className={`interview-summary-inner-card ${index === 0 ? "saprator-none" : ""}`} key={feedback.id}>
              <div className="background-heading col-10">
                <h4>
                  {feedback.skillTitle} Stratum Score: {feedback.score < 10 ? `0${feedback.score}` : feedback.score}
                </h4>
                {interviewStaticInformation.stratumDescription[feedback.score] ? (
                  <p>{interviewStaticInformation.stratumDescription[feedback.score]}</p>
                ) : null}
              </div>
              <div className="summary-highlights pb-3">
                <h2 className="summary-title d-flex items-center mb-4">
                  <AiMarkIcon className="me-2" /> Highlights
                </h2>
                <ul className="highlight-list">
                  {feedback.highlights?.overAllFeedback?.map((item, index) => (
                    <li className="highlight-item" key={index}>
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
              {!feedbackData?.isFeedbackFilled ? (
                <Button className="clear-btn secondary p-0 mt-4" onClick={() => enableEditMode(feedback.id)}>
                  <EditIcon className="me-2 p-1" fillNone /> Update Feedback
                </Button>
              ) : null}
            </div>
          );
        })
      : null;
  };

  // Add a new empty feedback point for career-based skills
  const addNewCareerFeedbackPoint = () => {
    if (careerFeedbackData && careerFeedbackData.highlights) {
      // Check if any existing field is empty in the form data
      const hasEmptyField = careerFeedbackData.highlights.overAllFeedback.some((_, index) => {
        const fieldName = `career-${index}`;
        const value = careerGetValues(fieldName);
        return !value || (typeof value === "string" && !value.trim());
      });
      if (hasEmptyField) {
        toastMessageError("Please fill in existing required fields before adding a new one");
        return;
      }
      const updatedCareerFeedback = {
        ...careerFeedbackData,
        highlights: {
          ...careerFeedbackData.highlights,
          overAllFeedback: [
            ...(careerFeedbackData.highlights.overAllFeedback || []),
            "", // Add an empty string as a new feedback point
          ],
        },
      };
      setCareerFeedbackData(updatedCareerFeedback);

      // Set an empty value for the new feedback point in the form
      const newIndex = updatedCareerFeedback.highlights.overAllFeedback.length - 1;
      careerSetValue(`career-${newIndex}`, "");
    }
  };

  // Remove a feedback point by index
  const removeCareerFeedbackPoint = (indexToRemove: number) => {
    if (careerFeedbackData && careerFeedbackData.highlights) {
      const updatedFeedbackPoints = careerFeedbackData.highlights.overAllFeedback.filter((_, index) => index !== indexToRemove);

      const updatedCareerFeedback = {
        ...careerFeedbackData,
        highlights: {
          ...careerFeedbackData.highlights,
          overAllFeedback: updatedFeedbackPoints,
        },
      };

      setCareerFeedbackData(updatedCareerFeedback);

      // Update the form values to match the new indices
      updatedFeedbackPoints.forEach((feedback, newIndex) => {
        careerSetValue(`career-${newIndex}`, feedback);
      });
    }
  };

  // Save the career-based feedback changes
  const saveCareerFeedback = careerHandleSubmit((data) => {
    if (careerFeedbackData && careerFeedbackData.highlights) {
      // Check for empty required fields
      const hasEmptyRequiredFields = careerFeedbackData.highlights.overAllFeedback.some((_, index) => {
        const value = data[`career-${index}`];
        return !value || (typeof value === "string" && !value.trim());
      });

      if (hasEmptyRequiredFields) {
        // Show error message if validation fails
        toastMessageError("Please fill in all required fields marked with *");
        return;
      }
      // Create a new array to store the updated feedback points
      const updatedFeedbackPoints = careerFeedbackData.highlights.overAllFeedback.map((_, index) => {
        // Get the current value from the form data
        return data[`career-${index}`] || "";
      });

      // Update the career feedback data state
      const updatedCareerFeedback = {
        ...careerFeedbackData,
        highlights: {
          ...careerFeedbackData.highlights,
          overAllFeedback: updatedFeedbackPoints,
        },
      };

      setCareerFeedbackData(updatedCareerFeedback);
      setEditCareerBasedSkills(false); // Exit edit mode
    }
  });

  // Cancel editing career-based feedback
  const cancelCareerFeedback = () => {
    // Restore the original data state before editing started
    if (originalCareerFeedbackData) {
      setCareerFeedbackData(originalCareerFeedbackData);
    }
    setEditCareerBasedSkills(false); // Exit edit mode

    // Reset form validation errors
    careerReset(
      { behavioralInfo: feedbackData?.applicantAiBehavioralAnalysis || null },
      {
        keepValues: true,
        keepDirtyValues: false,
        keepErrors: false,
        keepDirty: false,
        keepIsSubmitted: false,
        keepTouched: false,
        keepIsValid: false,
        keepSubmitCount: false,
      }
    );
  };

  // Enable editing behavioral notes
  const enableEditBehavioralNotes = () => {
    // Store the current behavioral notes before editing
    setEditBehavioralNotes(true);

    // Pre-populate the form with existing notes
    if (feedbackData?.applicantAiBehavioralAnalysis) {
      careerSetValue("behavioralInfo", feedbackData.applicantAiBehavioralAnalysis);
    }
  };

  // Save behavioral notes changes (only to state, no API call)
  const saveBehavioralNotes = () => {
    // Get values from the form using handleSubmit
    careerHandleSubmit((formData) => {
      const behavioralNotes = formData.behavioralInfo;

      console.log("Saving behavioral notes:", behavioralNotes);

      // Update the local data in state
      if (feedbackData) {
        console.log(feedbackData, "feedbackData before update");

        // Using the function form of setFeedbackData to get the latest state
        setFeedbackData((prevData) => {
          // Make sure prevData is not null before spreading
          if (!prevData) return null;

          const updatedData = {
            ...prevData,
            applicantAiBehavioralAnalysis: behavioralNotes || "",
          };

          // Now we can log the updated data correctly
          console.log(updatedData, "feedbackData after update");
          return updatedData;
        });
      }

      // Exit edit mode
      setEditBehavioralNotes(false);
      // toastMessageSuccess("Behavioral notes saved");
    })();
  };

  // Cancel editing behavioral notes
  // const cancelBehavioralNotes = () => {
  //   // Restore the original behavioral notes
  //   if (feedbackData) {
  //     careerSetValue("behavioralInfo", originalBehavioralNotes);
  //   }

  //   // Exit edit mode
  //   setEditBehavioralNotes(false);

  //   // Reset form validation errors
  //   careerReset(
  //     { behavioralInfo: originalBehavioralNotes || null },
  //     {
  //       keepValues: true,
  //       keepDirtyValues: false,
  //       keepErrors: false,
  //       keepDirty: false,
  //       keepIsSubmitted: false,
  //       keepTouched: false,
  //       keepIsValid: false,
  //       keepSubmitCount: false,
  //     }
  //   );
  // };

  // Save the skill-specific (role or culture) feedback changes
  const saveSkillFeedback = (skill: SkillRecord) => {
    const onSubmit = skillsHandleSubmit((data) => {
      if (skill && skill.highlights) {
        // Check for empty required fields
        const hasEmptyRequiredFields = skill.highlights.overAllFeedback.some((_, index) => {
          const value = data[`skill-${skill.id}-${index}`];
          return !value || (typeof value === "string" && !value.trim());
        });

        if (hasEmptyRequiredFields) {
          // Show error message if validation fails
          toastMessageError("Please fill in all required fields marked with *");
          return;
        }
        // Determine which skill list to update
        const updatedSkillsList = skill.type === "role" ? [...roleSpecificSkillsFeedback] : [...cultureSpecificSkillsFeedback];

        const skillIndex = updatedSkillsList.findIndex((s) => s.id === skill.id);

        if (skillIndex !== -1) {
          // Create a new array to store the updated feedback points
          const updatedFeedbackPoints = updatedSkillsList[skillIndex].highlights.overAllFeedback.map((_, index) => {
            // Get the current value from the form data
            return data[`skill-${skill.id}-${index}`] || "";
          });

          // Create updated skill object with new feedback points
          const updatedSkill = {
            ...updatedSkillsList[skillIndex],
            highlights: {
              ...updatedSkillsList[skillIndex].highlights,
              overAllFeedback: updatedFeedbackPoints,
            },
          };

          // Update the appropriate list
          updatedSkillsList[skillIndex] = updatedSkill;

          if (skill.type === "role") {
            setRoleSpecificSkillsFeedback(updatedSkillsList);
          } else {
            setCultureSpecificSkillsFeedback(updatedSkillsList);
          }

          // Exit edit mode
          setEditMode({ isEdit: false, skillId: null });
        }
      }
    });

    // Execute the submit handler
    onSubmit();
  };

  // Cancel editing skill-specific feedback
  const onCancelEditMode = () => {
    // Restore the original data states before editing started
    setRoleSpecificSkillsFeedback(originalRoleSpecificSkillsFeedback);
    setCultureSpecificSkillsFeedback(originalCultureSpecificSkillsFeedback);

    // Reset edit mode
    setEditMode({ isEdit: false, skillId: null });

    // Reset form validation errors
    skillsReset(undefined, {
      keepValues: true,
      keepDirtyValues: false,
      keepErrors: false,
      keepDirty: false,
      keepIsSubmitted: false,
      keepTouched: false,
      keepIsValid: false,
      keepSubmitCount: false,
    });
  };

  const handleAdvanceClick = () => {
    setConfirmationModal({
      isOpen: true,
      title: translate(INTERVIEW_FEEDBACK.MODAL.ADVANCE.TITLE),
      message: translate(INTERVIEW_FEEDBACK.MODAL.ADVANCE.MESSAGE),
      confirmButtonText: translate("confirm"),
      confirmButtonClass: "primary-btn", // Default button style
    });
  };

  const handleRejectClick = () => {
    setConfirmationModal({
      isOpen: true,
      title: translate(INTERVIEW_FEEDBACK.MODAL.REJECT.TITLE),
      message: translate(INTERVIEW_FEEDBACK.MODAL.REJECT.MESSAGE),
      confirmButtonText: translate("confirm"),
      confirmButtonClass: "danger-btn", // Red button for rejecting candidate
    });
  };

  const handleCloseConfirmationModal = () => {
    setConfirmationModal({
      ...confirmationModal,
      isOpen: false,
    });
  };

  const onHandleUpdateFeedback = async (isAllowedForNextRound: boolean) => {
    try {
      await saveBehavioralNotes();
      // Set the appropriate processing state based on which button was clicked
      if (isAllowedForNextRound) {
        setProcessingAdvance(true);
      } else {
        setProcessingReject(true);
      }

      // Close the confirmation modal
      handleCloseConfirmationModal();

      const highlightData = [...roleSpecificSkillsFeedback, ...cultureSpecificSkillsFeedback];

      if (careerFeedbackData) {
        highlightData.push(careerFeedbackData as SkillRecord);
      }

      console.log("highlightData===>>>", highlightData);

      // Include behavioral notes in the API call to update them in the database
      const behavioralInfo = feedbackData?.applicantAiBehavioralAnalysis || "";
      console.log("Sending behavioral notes to backend:", behavioralInfo);

      const response = await updateInterviewFeedback({
        interviewId: interviewId,
        feedback: JSON.stringify(highlightData),
        isAdvanced: isAllowedForNextRound,
        behavioralNotes: behavioralInfo, // Include behavioral notes in the API call
      });

      // For now, the backend will need to be updated separately to handle behavioral notes
      // The notes are already stored in state and can be displayed to the user
      if (response.data && response.data.success) {
        const successKey = isAllowedForNextRound ? "feedback_submit_success_advance" : "feedback_submit_success_reject";
        toastMessageSuccess(t(successKey));

        // Redirect to the dashboard after successful submission
        setTimeout(() => {
          router.push(ROUTES.DASHBOARD);
        }, 1000); // Short delay to allow the toast message to be read
      } else {
        const errorMessage = response.data?.message || "feedback_submit_error";
        toastMessageError(t(errorMessage));
      }
    } catch (error) {
      console.error("Error updating feedback:", error);
      toastMessageError(t("feedback_submit_error_generic"));
    } finally {
      // Reset both processing states regardless of which button was clicked
      setProcessingAdvance(false);
      setProcessingReject(false);
    }
  };

  // ...

  return (
    <>
      {/* Check if the user has already seen the interview feedback walkthrough */}
      {/* Add the walkthrough component */}
      <UserWalkthrough pageId={TOUR_STEPS.INTERVIEW_FEEDBACK} steps={interviewFeedbackSteps} isDataLoaded={!loading} startDelay={1000} />

      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                Interview <span>Feedback</span>
              </h2>
            </div>
          </div>
          <div className="section-heading mt-5">
            <h2 className="mb-3">{feedbackData?.candidateName}</h2>

            <p className="operation-text">
              {feedbackData?.jobTitle} <span className="px-2">|</span>Round {feedbackData?.roundNumber} <span className="px-2">|</span>{" "}
              {feedbackData?.candidateEmail}
            </p>
          </div>
        </div>
        <div className="inner-section">
          <div className="row">
            <div className="col-md-8">
              {/* Career Based Feedback */}

              <div className="interview-summary" id="career-feedback-section">
                <div className="summary-header">
                  <h1 className="summary-heading">Career Based Feedback</h1>
                </div>
                {editCareerBasedSkills ? (
                  <>
                    {careerFeedbackData.highlights && careerFeedbackData.highlights?.overAllFeedback?.length
                      ? careerFeedbackData.highlights &&
                        careerFeedbackData.highlights?.overAllFeedback?.map((feedback, index) => (
                          <InputWrapper key={index}>
                            <InputWrapper.Label htmlFor={`career-${index}`} required>
                              Point {index + 1 < 10 ? `0${index + 1}` : index + 1}
                            </InputWrapper.Label>
                            <div className="d-flex align-items-center justify-content-between">
                              <Textbox
                                className="form-control"
                                iconClass="w-100"
                                control={careerControl}
                                name={`career-${index}`}
                                type="text"
                                placeholder="Enter your Feedback"
                              ></Textbox>
                              <Button type="button" className="clear-btn text-danger p-0" onClick={() => removeCareerFeedbackPoint(index)}>
                                <ModalCloseIcon className="p-3 pe-0" />
                              </Button>
                            </div>
                          </InputWrapper>
                        ))
                      : null}

                    <Button className="clear-btn secondary p-0 mb-4" onClick={addNewCareerFeedbackPoint}>
                      +Add Another Point
                    </Button>

                    <div className="section-heading">
                      <h2 className="m-0">Stratum Score</h2>
                    </div>
                    <ul className="number-task">
                      {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((item, index) => (
                        <li
                          className={
                            item === 10
                              ? careerFeedbackData.score === item
                                ? "extreme active"
                                : "extreme"
                              : careerFeedbackData.score === item
                                ? "active"
                                : ""
                          }
                          key={index}
                          onClick={() => {
                            // Update the career feedback score when clicked
                            const updatedCareerFeedback = {
                              ...careerFeedbackData,
                              score: item,
                            };
                            setCareerFeedbackData(updatedCareerFeedback);
                          }}
                          style={{ cursor: "pointer" }}
                        >
                          {item === 10 ? "Extreme" : item}
                        </li>
                      ))}
                    </ul>
                    <p className="number-task-text">{interviewStaticInformation.stratumDescription[careerFeedbackData.score]}</p>
                    <div className="d-flex mt-5">
                      <Button type="button" className="secondary-btn button-sm minWidth rounded-md" onClick={saveCareerFeedback}>
                        Save
                      </Button>
                      <Button type="button" className="dark-outline-btn button-sm minWidth rounded-md ms-3" onClick={cancelCareerFeedback}>
                        Cancel
                      </Button>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="background-heading col-10">
                      <h4>Stratum Score: {careerFeedbackData.score < 10 ? `0${careerFeedbackData.score}` : careerFeedbackData.score}</h4>
                      {interviewStaticInformation.stratumDescription[careerFeedbackData.score] ? (
                        <p>{interviewStaticInformation.stratumDescription[careerFeedbackData.score]}</p>
                      ) : null}
                    </div>
                    <div className="summary-highlights pb-3">
                      <h2 className="summary-title d-flex items-center mb-4">
                        <AiMarkIcon className="me-2" /> Highlights
                      </h2>
                      {careerFeedbackData.highlights && careerFeedbackData.highlights?.overAllFeedback?.length ? (
                        <ul className="highlight-list">
                          {careerFeedbackData.highlights.overAllFeedback.map((item, index) => (
                            <li key={index} className="highlight-item">
                              {item}
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <p>No highlights available.</p>
                      )}
                    </div>
                    {!feedbackData?.isFeedbackFilled ? (
                      <Button
                        className="clear-btn secondary p-0 mt-4"
                        onClick={() => {
                          // Store the original data before entering edit mode
                          setOriginalCareerFeedbackData(JSON.parse(JSON.stringify(careerFeedbackData)));
                          setEditCareerBasedSkills(true);
                          if (careerFeedbackData.highlights && careerFeedbackData.highlights.overAllFeedback) {
                            careerFeedbackData.highlights.overAllFeedback.forEach((feedback, index) => {
                              careerSetValue(`career-${index}`, feedback);
                            });
                          }
                        }}
                      >
                        <EditIcon className="me-2 p-1" fillNone /> Update Feedback
                      </Button>
                    ) : null}
                  </>
                )}
              </div>

              {/* Career Based Feedback End */}

              {/* Role Specific Performance Based Feedback */}
              {roleSpecificSkillsFeedback.length > 0 && (
                <div className="interview-summary" id="role-culture-skills-section">
                  <div className="summary-header">
                    <h1 className="summary-heading">Role Specific Performance Based Feedback</h1>
                  </div>
                  {renderCultureAndRoleSpecificSkills(roleSpecificSkillsFeedback)}
                </div>
              )}

              {/* Culture Specific Performance Based Feedback */}
              {cultureSpecificSkillsFeedback.length > 0 && (
                <div className="interview-summary">
                  <div className="summary-header">
                    <h1 className="summary-heading">Culture Specific Performance Based Feedback</h1>
                  </div>
                  {renderCultureAndRoleSpecificSkills(cultureSpecificSkillsFeedback)}
                </div>
              )}
              {!aiDecisionForNextRound ? null : (
                <div className="feedback-result-card mb-5">
                  <div className="row justify-content-between">
                    <div className="col-xl-10 col-lg-10 col-md-8 col-sm-8 col-7 pe-3">
                      <h4 className="title">
                        Innerview <span>Verdict</span>
                      </h4>
                      <p className="description">
                        {aiDecisionForNextRound === AIDecisionForNextRound.APPROVED
                          ? "The AI has assessed the candidate and recommends advancing them to the next round. Please make your final decision."
                          : "The AI has assessed this candidate and recommends not advancing them to the next round."}
                      </p>
                    </div>
                    <div className="col-xl-2 col-lg-2 col-md-4 col-sm-4 col-5">
                      {aiDecisionForNextRound === AIDecisionForNextRound.APPROVED ? (
                        <ApprovedIcon className="status-icon" />
                      ) : (
                        <RejectedIcon className="status-icon" />
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className="col-md-4">
              <div className="behavioral-letter-card" id="behavioral-performance-section">
                <h5>
                  Behavioral <span>Performance</span>
                </h5>
                {/* <p>Describe Candidate’s Behaviour & Other Observations</p> */}

                <LetterFoldIcon className="fold-svg" />
                {/* {!feedbackData?.isFeedbackFilled ? ( */}

                <div className="mt-4">
                  <InputWrapper>
                    {/* <InputWrapper.Label htmlFor="behavioralNotes" required>
                    Behavioral Notes
                  </InputWrapper.Label> */}
                    <Textarea
                      rows={10}
                      disabled={!editBehavioralNotes}
                      name="behavioralInfo"
                      control={careerControl}
                      placeholder="Describe Candidate's Behaviour & Other Observations"
                      className="form-control"
                    />
                    {careerErrors.behavioralInfo && <InputWrapper.Error message={careerErrors.behavioralInfo.message || ""} />}
                  </InputWrapper>
                </div>
                {!editBehavioralNotes && !feedbackData?.isFeedbackFilled ? (
                  <Button className="clear-btn primary p-0 mt-4 update-btn" onClick={enableEditBehavioralNotes}>
                    <EditIcon className="me-2 p-1" fillNone /> Behavioural Notes
                  </Button>
                ) : null}
                {/* {editBehavioralNotes ? (
	                 <div className="d-flex mt-3 update-btn">
                      <Button type="button" className="secondary-btn button-sm minWidth rounded-md" onClick={saveBehavioralNotes}>
                        Save
                      </Button>
                      <Button type="button" className="dark-outline-btn button-sm minWidth rounded-md ms-3" onClick={cancelBehavioralNotes}>
                        Cancel
                      </Button>
                    </div>
                  ) : null} */}
              </div>
            </div>
          </div>
        </div>

        {!feedbackData?.isFeedbackFilled ? (
          <div className="button-align pb-5" id="interview-verdict-buttons">
            <Button className="primary-btn rounded-md" onClick={handleAdvanceClick} disabled={processingAdvance || processingReject}>
              {processingAdvance ? translate(INTERVIEW_FEEDBACK.BUTTON.PROCESSING) : translate(INTERVIEW_FEEDBACK.MODAL.ADVANCE.ADVANCE_BUTTON)}
            </Button>
            <Button className="dark-outline-btn rounded-md" onClick={handleRejectClick} disabled={processingAdvance || processingReject}>
              {processingReject ? translate(INTERVIEW_FEEDBACK.BUTTON.PROCESSING) : translate(INTERVIEW_FEEDBACK.MODAL.REJECT.BUTTON)}
            </Button>
          </div>
        ) : null}
        {/* Confirmation Modal */}
        <ConfirmationModal
          isOpen={confirmationModal.isOpen}
          onClose={handleCloseConfirmationModal}
          onConfirm={() => {
            // Determine if we're advancing or rejecting based on the modal title
            const isAdvance = confirmationModal.title === translate(INTERVIEW_FEEDBACK.MODAL.ADVANCE.TITLE);
            onHandleUpdateFeedback(isAdvance);
          }}
          title={confirmationModal.title}
          message={confirmationModal.message}
          confirmButtonText={confirmationModal.confirmButtonText}
          confirmButtonClass={confirmationModal.confirmButtonClass}
          loading={processingAdvance || processingReject}
        />
      </div>
    </>
  );
};

export default InterviewFeedback;
