const RolesPermissionsIcon = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <g clipPath="url(#clip0_17060_16160)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6.99967 13.5085H1.96634C1.93101 13.5085 1.89701 13.4939 1.87234 13.4692C1.84701 13.4439 1.83301 13.4105 1.83301 13.3752C1.83301 13.3745 1.83301 12.4085 1.83301 12.4085C1.83301 11.8545 2.22834 11.3665 2.83767 10.9519C3.92567 10.2092 5.68634 9.74121 7.66634 9.74121C7.99301 9.74121 8.31367 9.75454 8.62701 9.77921C8.90167 9.80121 9.14301 9.59588 9.16501 9.32054C9.18634 9.04521 8.98101 8.80454 8.70567 8.78254C8.36701 8.75521 8.02034 8.74121 7.66634 8.74121C5.45167 8.74121 3.49101 9.29521 2.27367 10.1252C1.34501 10.7585 0.833008 11.5632 0.833008 12.4079V13.3752C0.833008 13.6752 0.952341 13.9639 1.16501 14.1765C1.37767 14.3885 1.66567 14.5085 1.96634 14.5079C3.31034 14.5085 6.99967 14.5085 6.99967 14.5085C7.27567 14.5085 7.49967 14.2839 7.49967 14.0085C7.49967 13.7325 7.27567 13.5085 6.99967 13.5085Z"
          fill="#333333"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.66667 0.833008C5.64267 0.833008 4 2.47567 4 4.49967C4 6.52367 5.64267 8.16634 7.66667 8.16634C9.69067 8.16634 11.3333 6.52367 11.3333 4.49967C11.3333 2.47567 9.69067 0.833008 7.66667 0.833008ZM7.66667 1.83301C9.13867 1.83301 10.3333 3.02767 10.3333 4.49967C10.3333 5.97167 9.13867 7.16634 7.66667 7.16634C6.19467 7.16634 5 5.97167 5 4.49967C5 3.02767 6.19467 1.83301 7.66667 1.83301Z"
          fill="#333333"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.1017 13.472C12.9097 13.5933 13.7637 13.3427 14.3857 12.7207C15.427 11.6793 15.427 9.98866 14.3857 8.94799C13.345 7.90666 11.6543 7.90666 10.613 8.94799C9.99099 9.56999 9.74033 10.424 9.86166 11.2327L8.31366 12.78C8.21966 12.874 8.16699 13.0013 8.16699 13.134V14.6667C8.16699 14.9427 8.39099 15.1667 8.66699 15.1667H10.1997C10.3323 15.1667 10.4597 15.114 10.5537 15.02L12.1017 13.472ZM12.067 12.4453C11.895 12.3993 11.711 12.4487 11.585 12.5747L9.99299 14.1667H9.16699V13.3407L10.759 11.7487C10.885 11.6227 10.9343 11.4387 10.8883 11.2667C10.7403 10.71 10.8837 10.0913 11.3203 9.65466C11.971 9.00399 13.0277 9.00399 13.679 9.65466C14.3297 10.306 14.3297 11.3627 13.679 12.0133C13.2423 12.45 12.6237 12.5933 12.067 12.4453Z"
          fill="#333333"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.9879 11.3458C11.7059 11.0638 11.7059 10.6058 11.9879 10.3238C12.2699 10.0418 12.7279 10.0418 13.0099 10.3238C13.2919 10.6058 13.2919 11.0638 13.0099 11.3458C12.7279 11.6278 12.2699 11.6278 11.9879 11.3458Z"
          fill="#333333"
        />
      </g>
      <defs>
        <clipPath id="clip0_17060_16160">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default RolesPermissionsIcon;
