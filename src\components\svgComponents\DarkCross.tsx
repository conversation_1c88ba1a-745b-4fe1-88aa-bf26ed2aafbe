import React, { FC } from "react";

const DarkCross: FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none">
      <circle cx="12.0001" cy="11.9996" r="10.9254" fill="white" />
      <path
        d="M12 1C9.82441 1 7.69767 1.64514 5.88873 2.85383C4.07979 4.06253 2.66989 5.7805 1.83733 7.79048C1.00477 9.80047 0.786929 12.0122 1.21137 14.146C1.6358 16.2798 2.68345 18.2398 4.22183 19.7782C5.76021 21.3166 7.72022 22.3642 9.85401 22.7886C11.9878 23.2131 14.1995 22.9952 16.2095 22.1627C18.2195 21.3301 19.9375 19.9202 21.1462 18.1113C22.3549 16.3023 23 14.1756 23 12C22.9966 9.08368 21.8365 6.28778 19.7744 4.22563C17.7122 2.16347 14.9163 1.00344 12 1ZM16.242 14.829C16.3375 14.9212 16.4137 15.0316 16.4661 15.1536C16.5185 15.2756 16.5461 15.4068 16.5473 15.5396C16.5484 15.6724 16.5231 15.8041 16.4728 15.927C16.4225 16.0499 16.3483 16.1615 16.2544 16.2554C16.1605 16.3493 16.0489 16.4235 15.926 16.4738C15.8031 16.5241 15.6714 16.5494 15.5386 16.5483C15.4058 16.5471 15.2746 16.5195 15.1526 16.4671C15.0306 16.4147 14.9203 16.3385 14.828 16.243L12 13.414L9.17201 16.243C8.9834 16.4252 8.7308 16.526 8.4686 16.5237C8.20641 16.5214 7.9556 16.4162 7.77019 16.2308C7.58478 16.0454 7.47961 15.7946 7.47733 15.5324C7.47505 15.2702 7.57585 15.0176 7.75801 14.829L10.586 12L7.75801 9.171C7.6625 9.07875 7.58631 8.96841 7.5339 8.84641C7.4815 8.7244 7.45391 8.59318 7.45276 8.4604C7.4516 8.32762 7.4769 8.19594 7.52718 8.07305C7.57746 7.95015 7.65172 7.8385 7.74561 7.74461C7.8395 7.65071 7.95116 7.57646 8.07405 7.52618C8.19695 7.4759 8.32863 7.4506 8.46141 7.45175C8.59419 7.4529 8.72541 7.48049 8.84741 7.5329C8.96941 7.58531 9.07976 7.66149 9.17201 7.757L12 10.586L14.828 7.757C14.9203 7.66149 15.0306 7.58531 15.1526 7.5329C15.2746 7.48049 15.4058 7.4529 15.5386 7.45175C15.6714 7.4506 15.8031 7.4759 15.926 7.52618C16.0489 7.57646 16.1605 7.65071 16.2544 7.74461C16.3483 7.8385 16.4225 7.95015 16.4728 8.07305C16.5231 8.19594 16.5484 8.32762 16.5473 8.4604C16.5461 8.59318 16.5185 8.7244 16.4661 8.84641C16.4137 8.96841 16.3375 9.07875 16.242 9.171L13.414 12L16.242 14.829Z"
        fill="#333333"
      />
    </svg>
  );
};

export default DarkCross;
