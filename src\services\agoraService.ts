import * as http from "@/utils/http";
import { ApiResponse } from "@/interfaces/commonInterfaces";
import endpoint from "@/constants/endpoint";

/**
 * Get <PERSON><PERSON><PERSON> token for an interview
 * @param interviewId - ID of the interview
 * @returns Token data including uid, channelName, and token
 */
export const getAgoraToken = (data: {
  interviewId: string;
  personType: string;
  channelName: string;
  candidateEmail?: string;
  candidateId?: string;
  interviewerId?: string;
}): Promise<ApiResponse> => {
  return http.get(endpoint.agoraRecording.CREATE_TOKEN, data);
};

/**
 * Start recording for a meeting
 * @param meetingId - ID of the meeting to start recording for
 * @param data - Recording configuration data (channelName, uid, token, recordingConfig)
 */
export const startRecording = (data: { channelName: string; interviewId: string; agoraInterviewerId: string }): Promise<ApiResponse> => {
  return http.post(endpoint.agoraRecording.START_RECORDING, data);
};

/**
 * Stop recording for a meeting
 * @param data - Object containing meetingId, resourceId, sid, mode, channelName, uid
 */
export const stopRecording = (data: { resourceId: string; sid: string; mode?: string; channelName: string; uid: string }): Promise<ApiResponse> => {
  const url = endpoint.agoraRecording.STOP_RECORDING;
  return http.post(url, data);
};
