/* eslint-disable react-hooks/exhaustive-deps */
"use client";

// Internal libraries
import React, { useState, useEffect, useRef, useCallback } from "react";

// External libraries
import { useRouter } from "next/navigation";
import Image from "next/image";

// Redux, constants, interfaces
import { DEFAULT_LIMIT } from "@/constants/commonConstants";
import ROUTES from "@/constants/routes";
import { JobApplication } from "@/interfaces/jobRequirementesInterfaces";
import { APPLICATION_STATUS } from "@/constants/jobRequirementConstant";
import noImageFound from "@/../public/assets/images/no-candidates-found.svg";

// Components
import Button from "@/components/formElements/Button";
// We will delete this model after mock testing and we have created CandidateStatusModal common modal
// import CandidateQualifiedModal from "@/components/commonModals/CandidateQualifiedModal";
// import CandidateApprovalModal from "@/components/commonModals/CandidateApprovalModal";

// Services
import { getAllPendingJobApplications } from "@/services/screenResumeServices";

// CSS
import style from "@/styles/commonPage.module.scss";
import { useTranslations } from "next-intl";
import { toTitleCase, getDecryptedData, toastMessageError } from "@/utils/helper";
import { useTranslate } from "@/utils/translationUtils";
import CandidateStatusModal from "@/components/commonModals/CandidateStatusModal";

/**
 * CandidateQualification Component
 *
 * Displays a list of candidates pending qualification with options to approve, reject, or place on hold.
 * Includes infinite scrolling to load more candidates as the user scrolls down.
 *
 * @returns {JSX.Element} The rendered CandidateQualification component
 */
function CandidateQualification({
  params,
  searchParams,
}: {
  params: Promise<{ jobId: string }>;
  searchParams: Promise<{ title: string; jobUniqueId: string }>;
}) {
  const router = useRouter();
  // Modal states
  const [showCandidateApprovedModal, setShowCandidateApprovedModal] = useState(false);
  const [showCandidateRejectedModal, setShowCandidateRejectedModal] = useState(false);
  const [showCandidateHoldModal, setShowCandidateHoldModal] = useState(false);
  const searchParamsPromise = React.use(searchParams);
  const paramsPromise = React.use(params);
  const translate = useTranslate();
  // State for decrypted IDs
  const [decryptedJobId, setDecryptedJobId] = useState<number>(0);
  // Track the currently selected candidate for modals
  const [selectedCandidate, setSelectedCandidate] = useState<JobApplication | null>(null);
  // State for candidate applications
  const [applications, setApplications] = useState<JobApplication[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const STATUS = APPLICATION_STATUS; // Status to filter applications
  // const initialFetchDone = useRef(false);
  const t = useTranslations();
  // Handle decryption of jobId and validation in one effect
  useEffect(() => {
    if (!paramsPromise?.jobId) {
      console.error("No jobId found in URL parameters");
      toastMessageError(translate("invalid_or_missing_job_id"));
      router.back();
      return;
    }

    try {
      const decryptedData = getDecryptedData(paramsPromise.jobId);
      // Ensure jobId is always converted to a number
      const jobId = decryptedData?.jobId ? Number(decryptedData.jobId) : Number(paramsPromise.jobId);
      console.log("jobId", jobId);
      setDecryptedJobId(jobId);

      // Validate decrypted jobId and title
      if (isNaN(jobId) || !searchParamsPromise?.title || searchParamsPromise?.title.length === 0) {
        router.push(ROUTES.JOBS.ACTIVE_JOBS);
      }

      console.log("jobId==========================>", jobId);
      fetchApplications(jobId);

      // if (!initialFetchDone.current){
      //   initialFetchDone.current = true;
      // }
    } catch (error) {
      console.error("Error processing jobId:", error);
      toastMessageError(translate("something_went_wrong"));
      router.back();
    }
  }, [paramsPromise, router, searchParamsPromise?.title]);
  // Pagination parameters
  console.log("decryptedJobId", decryptedJobId);
  const [pagination, setPagination] = useState({
    limit: DEFAULT_LIMIT,
    offset: 0,
    status: STATUS.PENDING,
    job_id: decryptedJobId,
    // You can add other filter parameters like job_id if needed
  });

  // Observer for infinite scrolling
  const observer = useRef<IntersectionObserver | null>(null);
  const lastApplicationElementRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (loading) return;
      if (observer.current) observer.current.disconnect();
      observer.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasMore) {
          loadMoreApplications();
        }
      });
      if (node) observer.current.observe(node);
    },
    [loading, hasMore]
  );

  /**
   * Fetches candidate applications from the API
   *
   * Makes an API call to get pending job applications based on pagination parameters.
   * Updates the applications state with the fetched data and handles pagination status.
   *
   * @returns {Promise<void>}
   */
  const fetchApplications = async (_id: number) => {
    try {
      setLoading(true);
      const response = await getAllPendingJobApplications({
        ...pagination,
        job_id: _id,
      });

      if (response.data) {
        // Extract applications from response
        const newApplications = response.data.data || [];
        const paginationInfo = response.data.pagination || { total: 0, hasMore: false };

        // Append new applications to existing ones
        if (pagination.offset === 0) {
          setApplications(newApplications);
        } else {
          setApplications((prev) => [...prev, ...newApplications]);
        }

        // Check if we've loaded all applications based on hasMore flag
        setHasMore(paginationInfo.hasMore);
      }
    } catch (error) {
      console.error(t("error_fetching_applications"), error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Updates pagination to load more applications
   *
   * Increments the offset in the pagination state, which triggers the useEffect
   * to fetch the next batch of applications.
   */
  const loadMoreApplications = () => {
    setPagination((prev) => ({
      ...prev,
      offset: prev.offset + prev.limit,
    }));
  };

  // Fetch applications on component mount or when pagination changes
  // useEffect(() => {
  //   if (!initialFetchDone.current) {
  //     initialFetchDone.current = true;
  //     console.log("pagination.offset", pagination.offset);
  //     fetchApplications();
  //   }
  // }, [pagination.offset]);

  /**
   * Handles closing the candidate approval modal
   *
   * Resets the modal state and clears the selected candidate.
   */
  const onCancelCandidateApprovedModal = async () => {
    setShowCandidateApprovedModal(false);
    setSelectedCandidate(null);
  };

  /**
   * Handles closing the candidate rejection modal
   *
   * Resets the modal state and clears the selected candidate.
   */
  const onCancelCandidateRejectedModal = async () => {
    setShowCandidateRejectedModal(false);
    setSelectedCandidate(null);
  };

  /**
   * Handles closing the candidate hold modal
   *
   * Resets the modal state and clears the selected candidate.
   */
  const onCancelCandidateHoldModal = async () => {
    setShowCandidateHoldModal(false);
    setSelectedCandidate(null);
  };

  /**
   * Handles successful status change for a candidate
   *
   * Updates the application list by removing the candidate that was processed.
   * Closes any open modals and refreshes the application list if needed.
   *
   * @param {any} candidate - The candidate whose status was changed
   * @param {string} newStatus - The new status of the candidate (Approved, Rejected, On-Hold)
   */
  const handleStatusChangeSuccess = (candidate: JobApplication, newStatus: string) => {
    // 1. Optimistic UI update - update the candidate status in the local state immediately
    setApplications((prevApplications) => {
      return prevApplications
        .map((app) => {
          if (app.candidate_id === candidate.candidate_id) {
            // Create a copy of the application with updated status
            return {
              ...app,
              status: newStatus,
              // Remove it from the list if it's no longer in pending status
              hidden: app.status === STATUS.PENDING && newStatus !== STATUS.PENDING,
            };
          }
          return app;
        })
        .filter((app) => !app.hidden);
    });

    // 2. Refresh data from server in the background to ensure data consistency
    fetchApplications(decryptedJobId);
  };

  /**
   * Opens the approval modal for a candidate
   *
   * Sets the selected candidate and shows the approval modal.
   *
   * @param {JobApplication} candidate - The candidate to be approved
   */
  const handleApproveCandidate = (candidate: JobApplication) => {
    setSelectedCandidate(candidate);
    setShowCandidateApprovedModal(true);
  };

  /**
   * Opens the rejection modal for a candidate
   *
   * Sets the selected candidate and shows the rejection modal.
   *
   * @param {JobApplication} candidate - The candidate to be rejected
   */
  const handleRejectCandidate = (candidate: JobApplication) => {
    setSelectedCandidate(candidate);
    setShowCandidateRejectedModal(true);
  };

  /**
   * Opens the on-hold modal for a candidate
   *
   * Sets the selected candidate and shows the on-hold modal.
   *
   * @param {JobApplication} candidate - The candidate to be placed on hold
   */
  const onHoldCandidate = (candidate: JobApplication) => {
    setSelectedCandidate(candidate);
    setShowCandidateHoldModal(true);
  };

  return (
    <>
      <div className={`${style.resume_page} ${style.candidate_qualification_page}`}>
        <div className="container">
          <div className={style.inner_page}>
            <div className="common-page-header">
              <div className="common-page-head-section">
                <div className="main-heading">
                  <h2>
                    {/* <BackArrowIcon onClick={() => router.back()} /> */}
                    {t("resume_analysis")} <span>{searchParamsPromise?.title}</span>
                  </h2>
                  <div className={style.approved_status_indicator}>
                    <p className="approved">
                      <span />
                      {t("approved_by_s9")}
                    </p>
                    <p>
                      <span />
                      {t("rejected_by_s9")}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="row g-4">
              {/* Map through the applications array to render candidate cards */}
              {applications.map((application, index) => {
                // Check if this is the last element to attach the ref for infinite scrolling
                const isLastElement = index === applications.length - 1;
                const isRejected = application.ai_decision === STATUS.REJECTED;

                return (
                  <div
                    className="col-xl-4 col-lg-6 col-md-6 col-sm-12"
                    key={application.application_id || index}
                    ref={isLastElement ? lastApplicationElementRef : null}
                  >
                    <div
                      className={`qualification-card d-flex flex-column align-items-start justify-content-between h-100 ${isRejected ? "rejected-card" : "approved-card"}`}
                    >
                      <div>
                        <div className="qualification-card-top">
                          <div className="name">
                            <h3>{toTitleCase(application?.candidate_name)}</h3>
                            <p>{application?.ai_decision} by S9 InnerView</p>
                          </div>
                          {/* <div
                          className="top-right"
                          onClick={(e) => {
                            e.stopPropagation();
                            onHoldCandidate(application);
                          }}
                        >
                          <HoldIcon className="hold-icon cursor-pointer" />
                        </div> */}
                        </div>
                        <div className="qualification-card-mid">
                          <p>
                            <b>
                              {" "}
                              {application.ai_decision === STATUS.APPROVED
                                ? t("reasons_for_good_match")
                                : "Reasons why they are not good match:"}{" "}
                            </b>
                          </p>
                          <p>{application?.ai_reason}</p>
                        </div>
                      </div>
                      <div className="qualification-buttons">
                        <Button className="approve rounded-md rounded-md p-3" onClick={() => handleApproveCandidate(application)}>
                          {t("approve")}
                        </Button>
                        <Button className="reject rounded-md rounded-md p-3" onClick={() => handleRejectCandidate(application)}>
                          {t("reject")}
                        </Button>
                        <Button
                          className="dark-outline-btn rounded-md rounded-md p-3"
                          onClick={(e) => {
                            e.stopPropagation();
                            onHoldCandidate(application);
                          }}
                        >
                          {t("on_hold")}
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}

              {/* Skeleton loader */}
              {loading && (
                <>
                  {[1, 2, 3, 4, 5, 6].map((item) => (
                    <div className="col-md-6 col-lg-4" key={`skeleton-${item}`}>
                      <div className="qualification-card skeleton-card">
                        <div className="qualification-card-top d-block">
                          <div className="name mb-5">
                            <div className="skeleton-text skeleton-label" style={{ width: "70%", height: "16px" }}></div>
                            <div className="skeleton-text skeleton-subtitle" style={{ height: "14px", width: "50%" }}></div>
                          </div>
                        </div>
                        <div className="qualification-card-mid">
                          <div className="skeleton-text skeleton-label"></div>
                          <div className="skeleton-text"></div>
                          <div className="skeleton-text"></div>
                          <div className="skeleton-text"></div>
                          <div className="skeleton-text"></div>
                          <div className="skeleton-text"></div>
                          <div className="skeleton-text"></div>
                        </div>
                        <div className="button-align">
                          <div className="skeleton-button w-sm-100  "></div>
                          <div className="skeleton-button"></div>
                          <div className="skeleton-button"></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </>
              )}

              {/* No results message */}
              {!loading && applications.length === 0 && (
                <div className="col-12 text-center ">
                  <Image src={noImageFound} alt="No candidates found" className="img-fluid" />
                </div>
              )}
            </div>
          </div>
          {/* Only show buttons if there are pending applications */}
          {
            <div className="row pb-5 pt-3 mt-5">
              <div className="col-xl-2 col-lg-3 col-md-6 col-sm-6">
                <Button
                  className="primary-btn rounded-md w-100"
                  onClick={() =>
                    router.push(
                      `${ROUTES.SCREEN_RESUME.CANDIDATE_LIST}/${paramsPromise.jobId}?title=${searchParamsPromise?.title}&jobUniqueId=${searchParamsPromise?.jobUniqueId}`
                    )
                  }
                >
                  {t("view_all_candidates")}
                </Button>
              </div>
              <div className="col-xl-2 col-lg-3 col-md-6 col-sm-6">
                <Button
                  className="dark-outline-btn rounded-md w-100"
                  onClick={() =>
                    router.push(
                      `${ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD}/${paramsPromise.jobId}` +
                        `?title=${searchParamsPromise?.title}&jobUniqueId=${searchParamsPromise?.jobUniqueId}`
                    )
                  }
                >
                  {t("back_to_screening")}
                </Button>
              </div>
            </div>
          }
        </div>
      </div>

      {showCandidateApprovedModal && selectedCandidate && (
        <CandidateStatusModal
          onClickCancel={onCancelCandidateApprovedModal}
          candidate={selectedCandidate}
          actionType={APPLICATION_STATUS.APPROVED}
          aiDecision={selectedCandidate.ai_decision}
          onSuccess={() => handleStatusChangeSuccess(selectedCandidate, STATUS.APPROVED)}
        />
      )}

      {showCandidateRejectedModal && selectedCandidate && (
        <CandidateStatusModal
          onClickCancel={onCancelCandidateRejectedModal}
          candidate={selectedCandidate}
          actionType={APPLICATION_STATUS.REJECTED}
          aiDecision={selectedCandidate.ai_decision}
          onSuccess={() => handleStatusChangeSuccess(selectedCandidate, STATUS.REJECTED)}
        />
      )}

      {showCandidateHoldModal && selectedCandidate && (
        <CandidateStatusModal
          onClickCancel={onCancelCandidateHoldModal}
          candidate={selectedCandidate}
          actionType={APPLICATION_STATUS.ON_HOLD}
          onSuccess={() => handleStatusChangeSuccess(selectedCandidate, STATUS.ON_HOLD)}
          title={selectedCandidate.ai_reason === "Approved" ? "Reasons why they are good match:" : "Reasons why they are not good match:"}
        />
      )}
    </>
  );
}

export default CandidateQualification;
