import { NextConfig } from "next";
import createNextIntlPlugin from "next-intl/plugin";

const withNextIntl = createNextIntlPlugin();

const config: NextConfig = {
  typescript: {
    // ❗️TEMP: allow production builds to succeed despite TS errors
    ignoreBuildErrors: true,
  },
  images: {
    domains: [
      "dxxd0n8h8rh9s.cloudfront.net",
      "s9-interview-assets.s3.us-east-1.amazonaws.com",
      "s9-interview-assets-prod.s3.us-east-1.amazonaws.com",
      "s9-interview-assets-staging.s3.us-east-1.amazonaws.com",
    ],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "dxxd0n8h8rh9s.cloudfront.net",
        pathname: "/profile-images/**",
      },
    ],
  },
  async headers() {
    return [
      {
        // Apply security headers to all routes
        source: "/(.*)",
        headers: [
          {
            key: "Strict-Transport-Security",
            value: "max-age=31536000; includeSubDomains; preload",
          },
          // {
          //   key: "Content-Security-Policy",
          //   value:
          //     "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https: blob:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https: blob:; font-src 'self' data: https:; connect-src 'self' https: http://localhost:* ws://localhost:* wss://localhost:*; frame-src 'self' https:; frame-ancestors 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; media-src 'self' https:;",
          // },
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "strict-origin-when-cross-origin",
          },
          // {
          //   key: "Permissions-Policy",
          //   value: "geolocation=(), microphone=(), camera=()",
          // },
        ],
      },
    ];
  },
};

export default withNextIntl(config);
