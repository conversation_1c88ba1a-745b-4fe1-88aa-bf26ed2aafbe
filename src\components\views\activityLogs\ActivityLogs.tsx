"use client";

// Internal libraries
import React, { useState, useEffect } from "react";

// External libraries
import { useRouter } from "next/navigation";
import InfiniteScroll from "react-infinite-scroll-component";

// Components
import InputWrapper from "@/components/formElements/InputWrapper";

// import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";

// Services
import { fetchActivityLogs, Logs } from "@/services/activeLogsServices";

// Constants
import { DEFAULT_LIMIT, LOG_TYPE_OPTIONS } from "@/constants/commonConstants";

// CSS
import style from "@/styles/commonPage.module.scss";
import { useTranslations } from "next-intl";
import "react-loading-skeleton/dist/skeleton.css";
import TableSkeleton from "../skeletons/TableSkeleton";
import { useForm } from "react-hook-form";
import Select from "@/components/formElements/Select";
import { toTitleCase } from "@/utils/helper";
// import Loader from "@/components/loader/Loader";

const ActivityLogs = () => {
  const router = useRouter();
  const [logs, setLogs] = useState<Logs[]>([]);
  const [loading, setLoading] = useState(false);
  const [offset, setOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const t = useTranslations();
  const { control, watch } = useForm({ mode: "onChange" });

  useEffect(() => {
    setLogs([]);
    setOffset(0);
    setHasMore(true);
    fetchMoreLogs(0, true);
  }, [watch("log_type")]);

  const fetchMoreLogs = async (currentOffset = 0, reset = false) => {
    try {
      setLoading(true);
      if (reset) {
        setLogs([]);
      }
      const result = await fetchActivityLogs({
        offset: currentOffset,
        limit: DEFAULT_LIMIT,
        logType: watch("log_type"),
      });

      if (result?.data?.success && Array.isArray(result.data.data)) {
        const logsFetched = result.data.data;
        setLogs((prevLogs) => (reset ? logsFetched : [...prevLogs, ...logsFetched]));

        if (logsFetched.length < DEFAULT_LIMIT) {
          setHasMore(false);
        } else {
          setHasMore(true);
        }

        setOffset(currentOffset + logsFetched.length);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error(t("error_fetching_jobs"), error);
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <section className={`${style.resume_page} ${style.candidates_list_page}`}>
        <div className="container">
          <div className="common-page-header">
            <div className="common-page-head-section">
              <div className="main-heading">
                <h2>
                  {/* <BackArrowIcon onClick={() => router.back()} /> */}
                  {t("activity_logs")}
                </h2>
                <div className="right-action w-25">
                  <InputWrapper className="mb-0 w-75 ">
                    <div className="icon-align">
                      <Select name="log_type" options={LOG_TYPE_OPTIONS} control={control} className={"w-100"} placeholder={t("all")} />
                    </div>
                  </InputWrapper>
                </div>
              </div>
            </div>
          </div>

          <div className={style.candidates_list_section}>
            <div className="table-responsive">
              <InfiniteScroll
                dataLength={logs.length}
                next={() => fetchMoreLogs(offset)}
                hasMore={hasMore}
                height={window.innerHeight - 300}
                loader={
                  loading && (
                    <table className="table w-100">
                      <TableSkeleton rows={3} cols={6} colWidths="120,80,100,24,24,24" />
                    </table>
                  )
                }
                endMessage={
                  !loading && logs.length ? (
                    <table className="table w-100">
                      <tbody>
                        <tr>
                          <td colSpan={6} style={{ textAlign: "center", backgroundColor: "#fff" }}>
                            {t("no_more_activity_logs_to_fetch")}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  ) : null
                }
              >
                <table className="table w-100 overflow-auto mb-0">
                  <thead>
                    <tr>
                      <th>{t("log_type")}</th>
                      <th>{t("action_by")}</th>
                      <th>{t("comment")}</th>
                      <th>{t("previous_value")}</th>
                      <th>{t("updated_value")}</th>
                      <th>{t("date")}</th>
                    </tr>
                  </thead>
                  {logs.length > 0 ? (
                    <tbody>
                      {logs.map((log) => (
                        <tr key={log.auditId}>
                          <td style={{ width: "16%", whiteSpace: "normal" }}>{log.logType}</td>
                          <td style={{ width: "16%", whiteSpace: "normal" }}>{toTitleCase(log.actionByName)}</td>
                          <td style={{ width: "16%", whiteSpace: "normal" }}>{log.comments}</td>
                          <td style={{ width: "16%", whiteSpace: "normal" }}>
                            {!log.oldValue ? "—" : typeof log.oldValue === "string" ? log.oldValue.replace(/"/g, "") : log.oldValue}
                          </td>
                          <td style={{ width: "16%", whiteSpace: "normal" }}>
                            {!log.newValue ? "—" : typeof log.newValue === "string" ? log.newValue.replace(/"/g, "") : log.newValue}
                          </td>
                          <td>
                            {log.timestamp
                              ? new Date(log.timestamp).toLocaleDateString("en-US", {
                                  month: "short",
                                  day: "2-digit",
                                  year: "numeric",
                                })
                              : "—"}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  ) : (
                    !loading && (
                      <tbody>
                        <tr>
                          <td colSpan={6} style={{ textAlign: "center" }}>
                            {t("no_activity_logs_found")}
                          </td>
                        </tr>
                      </tbody>
                    )
                  )}
                </table>
              </InfiniteScroll>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default ActivityLogs;
