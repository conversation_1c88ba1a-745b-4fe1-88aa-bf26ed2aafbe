"use client";
import React from "react";
import "react-loading-skeleton/dist/skeleton.css";

// Subscription Success Page Component
const ThankYouAssessment = () => {
  return (
    <div className="subscription-successful-page">
      <div className="subscription-successful">
        <div className="success-icon">
          <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="40" cy="40" r="40" fill="#4BB543" />
            <path d="M56.6668 30L33.3335 53.3333L23.3335 43.3333" stroke="white" strokeWidth="6" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </div>
        <h1 className="subscription-successful-title">Thank You!</h1>
        <p>Your assessment has been submitted successfully.</p>
        <p>We will review your answers and get back to you soon.</p>
      </div>
    </div>
  );
};

export default ThankYouAssessment;
