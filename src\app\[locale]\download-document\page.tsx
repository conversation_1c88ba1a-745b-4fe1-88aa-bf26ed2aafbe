"use client";

import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { decryptInfo, downloadFile, toastMessageError } from "@/utils/helper";
import ROUTES from "@/constants/routes";
import { useRouter } from "next/navigation";
import { AuthState } from "@/redux/slices/authSlice";
import { useSelector } from "react-redux";
import { useTranslate } from "@/utils/translationUtils";
import FullPageLoader from "@/components/commonComponent/FullPageLoader";

const DownloadDocument = () => {
  const params = useSearchParams();
  const info = params.get("info");
  const translate = useTranslate();
  const router = useRouter();
  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);

  console.log("info=======@@@@@@@@@@@@@@@@@############", info);

  useEffect(() => {
    try {
      if (info) {
        const encryptionKey = process.env.NEXT_PUBLIC_DOCUMENT_ENCRYPTION_KEY;
        const decryptedInfo = decryptInfo(info, encryptionKey);
        const parsedData = JSON.parse(decryptedInfo);
        console.log(encryptionKey, "parsedData=======@@@@@@@@@@@@@@@@@############", parsedData);
        // check if user is logged in and email is available
        console.log("authData @@@@@@#####", authData);
        if (authData?.email) {
          console.log("user is logged in and email is available @@@#########");
          // check if user id is same as parsed data user id
          if (authData?.id === parsedData?.userId) {
            console.log("if block @@@#########");
            downloadFile(parsedData.fileUrl);
          } else {
            console.log("else block @@@#########");
            toastMessageError(translate("logged_in_with_another_account"));
          }
          router.replace(ROUTES.DASHBOARD);
        } else {
          // User is not logged in, redirect to login with info
          router.push(`${ROUTES.LOGIN}?info=${info}`);
        }
      }
    } catch (error) {
      console.error("Error processing token:", error);
    }
  }, []);

  return <FullPageLoader />;
};

export default DownloadDocument;
