"use client";
import React, { FC, useState, useMemo } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import Loader from "../loader/Loader";
import InputWrapper from "../formElements/InputWrapper";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { EmployeeInterface } from "@/interfaces/employeeInterface";
import { updateInterviewOrderValidationSchema } from "@/validations/employeeManagementValidations";
import Select from "../formElements/Select";
import { useTranslate } from "@/utils/translationUtils";

interface IProps {
  employeeName: string;
  currentOrder: number;
  onConfirm: (newOrder: number) => void;
  onCancel: () => void;
  isLoading?: boolean;
  employees?: EmployeeInterface[];
}

const UpdateInterviewOrderModal: FC<IProps> = ({ employeeName, currentOrder, onConfirm, onCancel, isLoading = false, employees = [] }) => {
  const t = useTranslate();
  const [submitError, setSubmitError] = useState<string | null>(null);

  const orderOptions = useMemo(() => {
    const existingOrders = employees
      .map((emp) => emp.interviewOrder)
      .filter((order) => order !== undefined && order !== null)
      .sort((a, b) => a - b);

    // Create a set of unique orders from existing only
    const uniqueOrders = new Set(existingOrders);

    // Convert to array and sort
    return Array.from(uniqueOrders).sort((a, b) => a - b);
  }, [employees]);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    watch,
  } = useForm({
    defaultValues: {
      interviewOrder: currentOrder,
    },
    resolver: yupResolver(updateInterviewOrderValidationSchema(t)),
    mode: "onChange",
  });

  const onSubmit = (data: { interviewOrder: number }) => {
    try {
      onConfirm(data.interviewOrder);
    } catch (error) {
      console.error(error);
      setSubmitError(t("unexpected_error"));
    }
  };

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header justify-content-center pb-0">
            <h2 className="m-0">{t("update_interview_order")}</h2>

            <Button className="modal-close-btn" onClick={onCancel} disabled={isLoading}>
              <ModalCloseIcon />
            </Button>
          </div>
          <div className="modal-body pt-2 ">
            <p className="text-center mb-4">
              {t("updating_order_for")} <strong>"{employeeName}"</strong>
            </p>
            <form onSubmit={handleSubmit(onSubmit)}>
              <InputWrapper className="mb-4">
                <InputWrapper.Label htmlFor="interviewOrder" required className="fw-bold">
                  {t("interview_order")}
                </InputWrapper.Label>

                <div className="form-group">
                  {/* Dropdown selector for existing orders */}
                  <Select
                    className="w-100"
                    name="interviewOrder"
                    control={control}
                    disabled={isLoading}
                    placeholder={t("select_interview_order")}
                    options={orderOptions.map((order) => ({ label: order.toString(), value: order }))}
                  />
                </div>

                <InputWrapper.Error message={errors?.interviewOrder?.message || ""} />
              </InputWrapper>

              {submitError && (
                <div className="alert alert-danger mb-3" role="alert">
                  {submitError}
                </div>
              )}

              <div className="button-align mt-4">
                <Button
                  type="submit"
                  className={`primary-btn rounded-md w-100 ${isLoading || !isValid || watch("interviewOrder") === currentOrder ? "truly-disabled" : ""}`}
                  disabled={isLoading || !isValid || watch("interviewOrder") === currentOrder}
                >
                  <div className="d-flex align-items-center justify-content-center">
                    {isLoading && <Loader />}
                    <span className={isLoading ? "ms-2" : ""}>{t("update_order")}</span>
                  </div>
                </Button>
                <Button type="button" className="dark-outline-btn rounded-md w-100" onClick={onCancel} disabled={isLoading}>
                  {t("cancel")}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UpdateInterviewOrderModal;
