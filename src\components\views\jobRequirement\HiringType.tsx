"use client";
// Internal libraries
import { useState } from "react";

// External libraries
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
// Components
import <PERSON><PERSON> from "@/components/formElements/Button";

// Constants
import ROUTES from "@/constants/routes";
import { ACTIVE } from "@/constants/commonConstants";
import { HIRING_TYPE } from "@/constants/jobRequirementConstant";
import { getJobPostingQuota } from "@/services/subscription";
import { toastMessageError } from "@/utils/helper";
import { useTranslate } from "@/utils/translationUtils";

// Assets
import internalHiring from "../../../../public/assets/images/internal-hiring.png";
import externalHiring from "../../../../public/assets/images/external-hiring.png";

// CSS
import style from "@/styles/commonPage.module.scss";

function HiringType() {
  const router = useRouter();
  const [hiringType, setHiringType] = useState(HIRING_TYPE.INTERNAL);
  const [checkingQuota, setCheckingQuota] = useState(false);

  const t = useTranslations("jobRequirement");
  const translate = useTranslate();

  const checkJobPostingQuota = async () => {
    setCheckingQuota(true);
    try {
      const response = await getJobPostingQuota();
      if (response?.data?.success) {
        router.push(`${ROUTES.JOBS.GENERATE_JOB}?hiringType=${hiringType}`);
      } else {
        toastMessageError(translate(response?.data?.message || "failed_to_fetch_quota"));
      }
    } catch (error) {
      console.error("Error checking job posting quota:", error);
      toastMessageError(translate("error_checking_quota"));
    } finally {
      setCheckingQuota(false);
    }
  };

  return (
    <div className={style.job_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                {/* <BackArrowIcon
                  onClick={() => {
                    router.push(ROUTES.DASHBOARD);
                  }}
                /> */}
                {t("select_hiring_type")}
              </h2>
            </div>
            <p className="description">{t("select_the_type_of_hiring_below_to_proceed_with_the_job_description_creation")}</p>
          </div>
        </div>
        <div className="row g-5" style={{ minHeight: window.innerHeight - 430 }}>
          <div className="col-md-6">
            <div
              className={`hiring-card ${hiringType === HIRING_TYPE.INTERNAL && ACTIVE}`}
              onClick={() => setHiringType(HIRING_TYPE.INTERNAL)}
              style={{ cursor: "pointer" }}
            >
              <div className="hiring-content">
                {/* <div className="radio-wrapper">
                  <input className="radio-input" type="radio" name="hiringType" id="internalHiring" checked={hiringType === HIRING_TYPE.INTERNAL} />
                  <label className="radio-label" htmlFor="internalHiring">
                    {t("internal_hiring")}
                  </label>
                </div> */}
                <div className="custom-radio-wrapper">
                  <input className="custom-radio-input" type="radio" name="hiringType" id="internalHiring" checked />
                  <label className="custom-radio-label" htmlFor="internalHiring">
                    Internal Hiring
                  </label>
                </div>
                <p>{t("craft_job_descriptions_for_internal_openings_and_promote_opportunities_within_your_organization")}</p>
              </div>
              <div className="hiring-image">
                <Image src={internalHiring} alt="internal-hiring" className="hiring-img" />
              </div>
            </div>
          </div>
          <div className="col-md-6">
            <div
              className={`hiring-card ${hiringType === HIRING_TYPE.EXTERNAL ? ACTIVE : ""}`}
              onClick={() => setHiringType(HIRING_TYPE.EXTERNAL)}
              style={{ cursor: "pointer" }}
            >
              <div className="hiring-content">
                <div className="custom-radio-wrapper">
                  <input
                    className="custom-radio-input"
                    type="radio"
                    name="hiringType"
                    id="externalHiring"
                    checked={hiringType === HIRING_TYPE.EXTERNAL}
                  />
                  <label className="custom-radio-label" htmlFor="externalHiring">
                    {t("external_hiring")}
                  </label>
                </div>
                <p>{t("attract_top_talent_by_creating_compelling_job_descriptions_for_external_candidates")}</p>
              </div>
              <div className="hiring-image">
                <Image src={externalHiring} alt="external-hiring" className="hiring-img" />
              </div>
            </div>
          </div>
        </div>
        <div className="button-align py-5">
          <Button
            className="primary-btn rounded-md"
            onClick={() => {
              checkJobPostingQuota();
            }}
            disabled={!hiringType || checkingQuota}
          >
            {checkingQuota ? `${t("continue")}...` : t("continue")}
          </Button>
        </div>
      </div>
    </div>
  );
}

export default HiringType;
