import React from "react";
import { toast } from "react-hot-toast";
import html2pdf from "html2pdf.js";
import Button from "@/components/formElements/Button";
import DownloadResumeIcon from "@/components/svgComponents/DownloadResumeIcon";
import { useSelector } from "react-redux";
import { selectProfileData } from "@/redux/slices/authSlice";
type PdfGeneratorProps = {
  content: string;
  fileName: string;
  onLoadingChange?: (isLoading: boolean) => void;
  title?: string;
  subtitle?: string;
  watermark?: string;
  theme?: "default" | "professional" | "modern" | "minimal";
  pageBreaks?: boolean;
  footerLogo?: string;
};

const PdfGenerator = ({ content, fileName, onLoadingChange, title, subtitle, theme = "default" }: PdfGeneratorProps) => {
  const [isLoading, setIsLoading] = React.useState(false);
  const authData = useSelector(selectProfileData);

  const convertImageUrlToBase64 = async (url: string): Promise<string> => {
    try {
      // Use fetch instead of axios for better ArrayBuffer handling
      const response = await fetch(url, {
        method: "GET",
        headers: {
          Accept: "image/*",
        },
      });

      if (!response.ok) {
        console.error("Error converting image to base64:", response.status);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      const base64 = Buffer.from(arrayBuffer).toString("base64");
      console.log("base64------>>>>", base64.substring(0, 50) + "...");

      // Detect image type from URL or use png as default
      const imageType = url.toLowerCase().includes(".jpg") || url.toLowerCase().includes(".jpeg") ? "jpeg" : "png";
      return `data:image/${imageType};base64,${base64}`;
    } catch (error) {
      console.error("Error converting image to base64:", error);
      return ""; // Return empty string if conversion fails
    }
  };
  const getThemeStyles = (theme: string) => {
    const baseStyles = {
      fontFamily: "Arial, sans-serif",
      lineHeight: "1.6",
      color: "#333",
      padding: "15mm 20mm 20mm 20mm",
      boxSizing: "border-box",
      width: "100%",
      minHeight: "100vh",
      position: "relative",
    };

    const themes = {
      default: {
        ...baseStyles,
        fontSize: "11pt",
        backgroundColor: "#ffffff",
      },
      professional: {
        ...baseStyles,
        fontSize: "10pt",
        fontFamily: "Georgia, serif",
        backgroundColor: "#fafafa",
        borderLeft: "4px solid #2563eb",
        paddingLeft: "24mm",
      },
      modern: {
        ...baseStyles,
        fontSize: "11pt",
        fontFamily: "Helvetica, Arial, sans-serif",
        backgroundColor: "#ffffff",
        borderTop: "3px solid #10b981",
      },
      minimal: {
        ...baseStyles,
        fontSize: "12pt",
        fontFamily: "Helvetica, Arial, sans-serif",
        backgroundColor: "#ffffff",
        padding: "20mm",
      },
    };

    return themes[theme as keyof typeof themes] || themes.default;
  };

  const createStyledContentWithLogos = (content: string, companyLogoData?: string, footerLogoData?: string) => {
    const themeStyles = getThemeStyles(theme);

    return `
      <div style="${Object.entries(themeStyles)
        .map(([key, value]) => `${key.replace(/([A-Z])/g, "-$1").toLowerCase()}: ${value}`)
        .join("; ")}">
        
        ${
          companyLogoData
            ? `
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="${companyLogoData}" alt="Company Logo" style="max-height: 40px; max-width: 150px; object-fit: contain;" />
          </div>
        `
            : `
            <div style="text-align: center; margin-bottom: 15px;">
              <h1 style="
                margin: 0; 
                font-size: ${theme === "minimal" ? "18pt" : "16pt"}; 
                font-weight: bold; 
                color: ${theme === "professional" ? "#1f2937" : theme === "modern" ? "#10b981" : "#2563eb"};
                border-bottom: ${theme === "minimal" ? "none" : "2px solid #e5e7eb"};
                padding-bottom: ${theme === "minimal" ? "0" : "10px"};
              ">
                ${authData?.organizationName}
              </h1>
            </div>
          `
        }
        
        ${
          title
            ? `
          <div style="text-align: center; margin-bottom: 15px;">
            <h1 style="
              margin: 0; 
              font-size: ${theme === "minimal" ? "18pt" : "16pt"}; 
              font-weight: bold; 
              color: ${theme === "professional" ? "#1f2937" : theme === "modern" ? "#10b981" : "#2563eb"};
              border-bottom: ${theme === "minimal" ? "none" : "2px solid #e5e7eb"};
              padding-bottom: ${theme === "minimal" ? "0" : "10px"};
            ">
              ${title}
            </h1>
          </div>
        `
            : ""
        }
        
        ${
          subtitle
            ? `
          <div style="text-align: center; margin-bottom: 25px;">
            <p style="
              margin: 0; 
              font-size: 12pt; 
              color: #6b7280; 
              font-style: italic;
            ">
              ${subtitle}
            </p>
          </div>
        `
            : ""
        }
        
        <div style="position: relative; z-index: 1;">
          ${content}
        </div>
        
        <div style="
          position: fixed;
          bottom: 10mm;
          right: 20mm;
          font-size: 8pt;
          color: #9ca3af;
          z-index: 2;
        ">
          Generated on ${new Date().toLocaleDateString()}
        </div>
      </div>

      ${
        footerLogoData
          ? `
          <div style="text-align: center; margin-top: 30px; margin-bottom: 20px;">
            <img src="${footerLogoData}" alt="Footer Logo" style="max-height: 40px; max-width: 150px; object-fit: contain;" />
          </div>
        `
          : `
          <div style="text-align: center; margin-bottom: 15px;">
            <h1 style="
              margin: 0; 
              font-size: ${theme === "minimal" ? "18pt" : "16pt"}; 
              font-weight: bold; 
              color: ${theme === "professional" ? "#1f2937" : theme === "modern" ? "#10b981" : "#2563eb"};
              border-bottom: ${theme === "minimal" ? "none" : "2px solid #e5e7eb"};
              padding-bottom: ${theme === "minimal" ? "0" : "10px"};
            ">
              ${authData?.organizationName}
            </h1>
          </div>
        `
      }
      
      <style>
        /* Enhanced page break controls for html2pdf */
        .question-item {
          page-break-inside: avoid !important;
          break-inside: avoid !important;
          margin-bottom: 15px;
          padding: 10px;
          border: 1px solid #e5e7eb;
          background-color: #fafafa;
        }
        
        .category-container {
          page-break-inside: avoid !important;
          break-inside: avoid !important;
          margin-bottom: 20px;
        }
        
        .section-header {
          page-break-after: avoid !important;
          break-after: avoid !important;
          page-break-inside: avoid !important;
          break-inside: avoid !important;
          margin-top: 25px;
          margin-bottom: 15px;
          padding: 8px 0;
          border-bottom: 2px solid #e5e7eb;
          font-weight: bold;
        }
        
        h1, h2, h3, h4, h5, h6 { 
          page-break-after: avoid !important; 
          break-after: avoid !important;
          page-break-inside: avoid !important; 
          break-inside: avoid !important;
          orphans: 3;
          widows: 3;
        }
        
        p { 
          page-break-inside: avoid !important; 
          break-inside: avoid !important;
          orphans: 2;
          widows: 2;
          margin-bottom: 10px;
        }
        
        li {
          page-break-inside: avoid !important;
          break-inside: avoid !important;
          margin-bottom: 5px;
        }
        
        ul, ol {
          page-break-inside: avoid !important;
          break-inside: avoid !important;
          margin: 10px 0;
          padding-left: 25px;
        }
        
        div {
          page-break-inside: avoid !important;
          break-inside: avoid !important;
        }
        
        .page-break { 
          page-break-before: always !important; 
          break-before: always !important;
        }
        
        .no-break { 
          page-break-inside: avoid !important; 
          break-inside: avoid !important;
        }
        
        /* Section breaks */
        .section-break {
          page-break-before: always !important;
          break-before: always !important;
        }
        
        table { 
          border-collapse: collapse; 
          width: 100%; 
          margin: 10px 0;
          page-break-inside: avoid !important;
          break-inside: avoid !important;
        }
        
        table, th, td { 
          border: 1px solid #ddd; 
        }
        
        th, td { 
          padding: 8px; 
          text-align: left; 
        }
        
        th { 
          background-color: #f8f9fa; 
          font-weight: bold; 
        }
        
        blockquote {
          margin: 15px 0;
          padding: 10px 15px;
          border-left: 4px solid #e5e7eb;
          background-color: #f9fafb;
          font-style: italic;
          page-break-inside: avoid !important;
          break-inside: avoid !important;
        }
        
        code {
          background-color: #f3f4f6;
          padding: 2px 4px;
          border-radius: 3px;
          font-family: 'Courier New', monospace;
          font-size: 90%;
        }
        
        pre {
          background-color: #f3f4f6;
          padding: 15px;
          border-radius: 5px;
          overflow-x: auto;
          font-family: 'Courier New', monospace;
          font-size: 90%;
          line-height: 1.4;
          page-break-inside: avoid !important;
          break-inside: avoid !important;
        }
      </style>
    `;
  };

  const generatePdf = async () => {
    if (!content) {
      toast.error("No content to generate PDF");
      return;
    }

    try {
      setIsLoading(true);
      if (onLoadingChange) onLoadingChange(true);

      // Convert logos to base64 first
      let companyLogoBase64 = "";
      let footerLogoBase64 = "";

      if (authData?.logo) {
        companyLogoBase64 = await convertImageUrlToBase64(authData?.logo);
      }

      if (authData?.logo) {
        footerLogoBase64 = await convertImageUrlToBase64(authData?.logo);
      }

      // Create a temporary div with the styled content
      const element = document.createElement("div");
      element.innerHTML = createStyledContentWithLogos(content, companyLogoBase64, footerLogoBase64);

      // Enhanced PDF options with better page break handling
      const options = {
        filename: `${fileName.replace(/[^a-zA-Z0-9]/g, "_")}_${new Date().toISOString().slice(0, 19).replace(/[-:T]/g, "")}.pdf`,
        image: {
          type: "jpeg",
          quality: 0.98,
        },
        html2canvas: {
          scale: 1.5,
          useCORS: true,
          allowTaint: true,
          backgroundColor: "#ffffff",
          removeContainer: true,
          logging: false,
          width: 794, // A4 width in pixels at 96 DPI
          height: undefined, // Let it calculate height
        },
        jsPDF: {
          unit: "mm",
          format: "a4",
          orientation: "portrait",
          compress: true,
        },
        pagebreak: {
          mode: ["avoid-all", "css", "legacy"],
          before: ".page-break",
          after: ".page-break-after",
          avoid: ".no-break, .question-item, .category-container, h1, h2, h3, h4, h5, h6, p, li, div, ul, ol, blockquote, pre",
        },
      };

      // Generate and download PDF
      await html2pdf().set(options).from(element).save();

      toast.success("PDF generated successfully");
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast.error("Failed to generate PDF. Please try again.");
    } finally {
      setIsLoading(false);
      if (onLoadingChange) onLoadingChange(false);
    }
  };

  return (
    <Button
      className="clear-btn p-0 ms-3"
      disabled={!content || isLoading}
      onClick={generatePdf}
      title={isLoading ? "Generating PDF..." : "Download Job Description in PDF"}
    >
      <DownloadResumeIcon />
    </Button>
  );
};

export default PdfGenerator;
