"use client";
import React from "react";
import Joyride from "react-joyride-react-19";
import { WalkthroughProps } from "./types";
import useWalkthrough from "./useWalkthrough";

const UserWalkthrough: React.FC<WalkthroughProps> = ({ pageId, steps, isDataLoaded, startDelay = 1000 }) => {
  const { runTour, handleJoyrideCallback } = useWalkthrough(pageId, steps, isDataLoaded, startDelay);

  // Don't render if there are no steps or no targets found in the DOM
  React.useEffect(() => {
    if (steps.length > 0) {
      // Check if targets exist in DOM
      const allTargetsExist = steps.every((step) => {
        const target = step.target.toString();
        // Handle both CSS selectors and element IDs
        return target.startsWith("#") ? document.querySelector(target) !== null : document.querySelector(target) !== null;
      });

      if (!allTargetsExist) {
        console.warn("Some tour targets not found in the DOM");
      }
    }
  }, [steps, isDataLoaded]);

  return (
    <>
      <Joyride
        steps={steps}
        run={runTour}
        continuous={true}
        showProgress={true}
        showSkipButton={true}
        disableOverlayClose={true}
        hideBackButton={false}
        locale={{
          skip: "Skip",
          last: "Finish",
        }}
        hideCloseButton={true}
        spotlightClicks={true}
        callback={handleJoyrideCallback}
        disableScrollParentFix={false}
        disableOverlay={true}
        scrollToFirstStep={true}
        scrollDuration={800}
        scrollOffset={120}
        spotlightPadding={20}
        floaterProps={{
          hideArrow: false,
          offset: 10,
          disableAnimation: true,
          wrapperOptions: {
            offset: 0,
            position: true,
          },
        }}
        styles={{
          options: {
            zIndex: 10,
            primaryColor: "#6658ea",
            textColor: "#4a4a4a",
            arrowColor: "#fff",
          },
          tooltip: {
            fontFamily: "Plus Jakarta Sans, sans-serif",
            borderRadius: "8px",
            boxShadow: "0 8px 24px rgba(0, 0, 0, 0.15)",
            animationName: "none",
            animationDuration: "0s",
          },
          buttonNext: {
            backgroundColor: "#436eb6",
          },
          buttonBack: {
            color: "#436eb6",
          },
          overlay: {
            animationDuration: "0.4s",
            animationTimingFunction: "ease",
          },
          spotlight: {
            animationDuration: "0.4s",
            animationTimingFunction: "ease",
          },
        }}
      />
    </>
  );
};

export { useWalkthrough };
export default UserWalkthrough;
