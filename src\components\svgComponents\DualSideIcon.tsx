import React from "react";

function DualSideIcon({ className }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} width="25" height="25" viewBox="0 0 32 32" fill="none">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M28 21.5H8C7.172 21.5 6.5 22.172 6.5 23C6.5 23.828 7.172 24.5 8 24.5H28C28.828 24.5 29.5 23.828 29.5 23C29.5 22.172 28.828 21.5 28 21.5Z"
        fill="#CB9932"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M4 10.5H24C24.828 10.5 25.5 9.828 25.5 9C25.5 8.172 24.828 7.5 24 7.5H4C3.172 7.5 2.5 8.172 2.5 9C2.5 9.828 3.172 10.5 4 10.5Z"
        fill="#CB9932"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M21.94 19.0595L25.878 22.9995L21.94 26.9395C21.354 27.5235 21.354 28.4755 21.94 29.0595C22.524 29.6455 23.476 29.6455 24.06 29.0595L29.06 24.0595C29.646 23.4735 29.646 22.5255 29.06 21.9395L24.06 16.9395C23.476 16.3535 22.524 16.3535 21.94 16.9395C21.354 17.5235 21.354 18.4755 21.94 19.0595Z"
        fill="#CB9932"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M10.06 12.9395L6.12199 8.9995L10.06 5.0595C10.646 4.4755 10.646 3.5235 10.06 2.9395C9.47599 2.3535 8.52399 2.3535 7.93999 2.9395L2.93999 7.9395C2.35399 8.5255 2.35399 9.4735 2.93999 10.0595L7.93999 15.0595C8.52399 15.6455 9.47599 15.6455 10.06 15.0595C10.646 14.4755 10.646 13.5235 10.06 12.9395Z"
        fill="#CB9932"
      />
    </svg>
  );
}

export default DualSideIcon;
