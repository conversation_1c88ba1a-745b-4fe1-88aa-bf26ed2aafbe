"use client";
import Link from "next/link";
import Image from "next/image";
import Logo from "../../../../public/assets/images/logo.svg";
import ROUTES from "@/constants/routes";
import { useAuth } from "@/hooks/useAuth";

const HireCompliance = () => {
  const { isAuthenticated } = useAuth();
  return (
    <>
      <div className="logo-header text-center">
        <Link className="navbar-brand" href={isAuthenticated ? ROUTES.DASHBOARD : ROUTES.HOME}>
          <Image src={Logo} alt="logo" width={640} height={320} />
        </Link>
      </div>
      <section className="static-page py-5">
        <div className="container">
          <p>
            A comprehensive compliance section in a job posting—especially for government jobs or organizations that prioritize diversity, equity, and
            inclusion—should include standard Equal Employment Opportunity (EEO) statements, affirmative action language, and accommodations for
            disabilities and veterans. Below is a standardized compliance section that covers all major criteria:
          </p>

          <h3 className="color-primary">Equal Employment Opportunity (EEO) Statement</h3>
          <p>
            [Company/Agency Name] is an Equal Opportunity Employer. We are committed to creating an inclusive and diverse workplace that values and
            respects individuals of all backgrounds. We do not discriminate on the basis of race, color, national origin, religion, sex (including
            pregnancy, sexual orientation, or gender identity), age, disability, genetic information, veteran status, or any other protected status
            under federal, state, or local laws.
          </p>

          <h3 className="color-primary">Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)</h3>
          <p>
            As a federal contractor, [Company/Agency Name] is committed to affirmative action and equal employment opportunities for individuals in
            all protected categories. We actively seek to hire and promote qualified individuals, including minorities, women, individuals with
            disabilities, and protected veterans.
          </p>

          <h3 className="color-primary">Disability Accommodation Statement</h3>
          <p>
            [Company/Agency Name] is committed to ensuring that all applicants have an equal opportunity to apply for and perform job-related duties.
            If you require a reasonable accommodation due to a disability to complete the application process or to perform the essential functions of
            a position, please contact [HR Contact Name/Email/Phone Number].
          </p>

          <h3 className="color-primary">Veterans Preference Statement (For Government Agencies and Federal Contractors)</h3>
          <p>
            [Company/Agency Name] values the contributions of our nation's veterans and encourages those who have served in the military to apply.
            Eligible veterans and their spouses may be entitled to hiring preference as outlined by federal, state, or local laws. To claim veteran's
            preference, please submit the required documentation with your application.
          </p>

          <h3 className="color-primary">Diversity & Inclusion Commitment</h3>
          <p>
            At [Company/Agency Name], diversity and inclusion are fundamental to our mission. We strive to create a workplace where all employees feel
            valued, respected, and empowered to contribute their unique perspectives. We encourage individuals from all backgrounds to apply and join
            our team.
          </p>

          <h3 className="color-primary">Pay Transparency Non-Discrimination Statement (For Federal Contractors)</h3>
          <p>
            In accordance with Executive Order 11246 and the Pay Transparency Nondiscrimination Provision, [Company/Agency Name] does not discriminate
            against employees or applicants who inquire about, discuss, or disclose their own pay or the pay of another employee or applicant.
            However, employees with access to compensation information as part of their essential job functions are prohibited from disclosing such
            information to individuals who do not have access to it, except as required by law.
          </p>

          <h3 className="color-primary">Background Check and Drug-Free Workplace Policy (If Applicable)</h3>
          <p>
            As a condition of employment, selected candidates must successfully complete a background check and may be required to undergo a
            pre-employment drug screening in compliance with federal and state regulations.
          </p>

          <h3 className="color-primary">Work Authorization & Immigration Statement</h3>
          <p>
            [Company/Agency Name] complies with federal laws requiring verification of identity and legal authorization to work in the United States.
            We participate in E-Verify and will confirm work authorization before employment begins.
          </p>
          <p>
            This section ensures compliance with all major federal requirements, including EEOC, ADA, VEVRAA (Veterans), OFCCP (Affirmative Action),
            and other relevant regulations. If this is for a specific jurisdiction, additional state/local regulations may need to be included.
          </p>

          <h3 className="color-primary">Compliance Best Practices for Web-Based Interview Tools</h3>
          <p>To fully comply with the above regulations, consider implementing the following best practices:</p>

          <h4 className="text-dark mt-4">Bias Audits & AI Explainability</h4>
          <p>Regularly audit AI hiring algorithms for fairness.</p>
          <p>Provide explanations for why a candidate is approved or rejected.</p>

          <h4 className="text-dark mt-4">EEO & ADA Compliance</h4>
          <p>Clearly state non-discrimination policies in job postings.</p>
          <p>Offer accommodations (screen readers, alternative interview formats).</p>

          <h4 className="text-dark mt-4">Data Privacy & Security</h4>
          <p>Use SOC 2, ISO 27001 certified cloud storage for candidate data.</p>
          <p>Encrypt all candidate data at rest and in transit.</p>

          <h4 className="text-dark mt-4">Consent & Transparency</h4>
          <p>Obtain explicit candidate consent for AI-based decisions, recordings, and biometric data.</p>
          <p>Allow manual review of AI decisions before rejecting a candidate.</p>

          <h4 className="text-dark mt-4">Human Oversight in Hiring</h4>
          <p>Ensure humans review AI-generated hiring decisions.</p>
          <p>Give candidates the option to appeal AI-based rejections.</p>
        </div>
      </section>
    </>
  );
};

export default HireCompliance;
