import endpoint from "@/constants/endpoint";
import { IApiResponseCommonInterface } from "@/interfaces/commonInterfaces";
import * as http from "@/utils/http";

/**
 * Update organization logo
 * @param logo New logo URL or base64 string
 * @returns Promise with API response containing updated organization logo
 */

export const updateLogo = (logo: string): Promise<IApiResponseCommonInterface<{ logo: string }>> => {
  return http.put(endpoint.updateLogo.UPDATE_LOGO, { logo });
};
