// validations/screenResumeValidations.ts
import * as yup from "yup";
import { NAME_REGEX, EMAIL_REGEX } from "@/utils/validationSchema";

const candidateSchema = (t: (key: string) => string) =>
  yup.object().shape({
    name: yup
      .string()
      .trim()
      .required(t("name_required"))
      .min(3, t("name_min"))
      .max(50, t("name_max"))
      .matches(NAME_REGEX, {
        message: t("name_validation_requirements"),
        excludeEmptyString: true,
      }),
    email: yup
      .string()
      .trim()
      .required(t("email_required"))
      .matches(EMAIL_REGEX, { message: t("email_valid"), excludeEmptyString: true })
      .max(50, t("email_max")),
    gender: yup.string().required(t("gender_required")),
    resume: yup.mixed().required(t("resume_required")),
    assessment: yup.mixed().optional().nullable(),
    additionalInfo: yup.string().trim().optional().max(200, t("additional_max")),
  });

export const formSchemaValidation = (t: (key: string) => string) =>
  yup.object({
    candidates: yup.array().of(candidateSchema(t)).min(1, t("at_least_one_candidate")),
  });
