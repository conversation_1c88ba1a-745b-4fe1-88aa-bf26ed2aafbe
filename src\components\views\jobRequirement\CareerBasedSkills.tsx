"use client";

// Internal libraries
import { useState, useEffect } from "react";

// External libraries
import { redirect, useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";

// Components
import Button from "@/components/formElements/Button";
import Modal from "@/components/formElements/Modal";
import Loader from "@/components/loader/Loader";
import { CommonInput } from "@/components/formElements/Textbox";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
import EditSkillIcon from "@/components/svgComponents/EditSkillIcon";

// Services (Redux, constants, interfaces)
import { selectJobDetails } from "@/redux/slices/jobDetailsSlice";
import { selectCareerSkills, setSkillsData } from "@/redux/slices/jobSkillsSlice";
import { ISkillData } from "@/interfaces/jobRequirementesInterfaces";
import ROUTES from "@/constants/routes";

// CSS
import style from "@/styles/commonPage.module.scss";
import { useTranslations } from "next-intl";
import NoDataFoundIcon from "@/components/svgComponents/NoDataFoundIcon";

function CareerBasedSkills() {
  const router = useRouter();
  const dispatch = useDispatch();
  const t = useTranslations("careerBasedSkills");
  const tJobRequirement = useTranslations("jobRequirement");
  const tCommon = useTranslations();
  // Get career skills from Redux store
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const reduxCareerSkills = useSelector(selectCareerSkills) || [];
  const jobDetails = useSelector(selectJobDetails);

  // Loading states
  const [isSaving, setIsSaving] = useState(false);

  // Local state for skills that can be edited before saving to Redux
  const [localCareerSkills, setLocalCareerSkills] = useState<ISkillData[]>([]);

  // Edit modal state
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingSkill, setEditingSkill] = useState<{ index: number; skill: ISkillData | null }>({
    index: -1,
    skill: null,
  });

  // Form values for the modal
  const [formValues, setFormValues] = useState({
    name: "",
    description: "",
  });
  const [formErrors, setFormErrors] = useState({
    name: "",
    description: "",
  });

  /**
   * Redirects to career-based skills page if required data is missing
   *
   * @effect
   * @description Checks if the skill type is valid and if necessary Redux data is available
   *              Redirects to the career-based skills page if any validation fails
   *              Runs only once on component mount due to empty dependency array
   */

  useEffect(() => {
    if (!reduxCareerSkills || reduxCareerSkills.length === 0) redirect(`${ROUTES.JOBS.GENERATE_JOB}`);
  }, []);

  /**
   * Initializes local state with career skills from Redux store
   * @description Sets up local state with career skills when component mounts or Redux state changes
   * @param {ISkillData[]} reduxCareerSkills - Career skills from Redux store
   */
  useEffect(() => {
    setLocalCareerSkills([...reduxCareerSkills]);
  }, [reduxCareerSkills]);

  /**
   * Opens the edit modal for a specific skill
   * @description Initializes the edit modal with the skill's data and opens the modal
   * @param {ISkillData} skill - The skill to edit
   * @param {number} index - The index of the skill in the local state array
   */
  const handleEditClick = (skill: ISkillData, index: number) => {
    // Set the editing skill reference
    setEditingSkill({ index, skill: { ...skill } });

    // Initialize form values from the skill with safe null checking
    // This ensures the descriptions and names are shown correctly
    setFormValues({
      name: skill.name !== undefined && skill.name !== null ? skill.name : "",
      description: skill.description !== undefined && skill.description !== null ? skill.description : "",
    });

    setShowEditModal(true);
  };

  /**
   * State for form validation errors
   * @type {{ name: string; description: string }}
   */

  /**
   * Validates a specific field and returns error message if any
   * @param {"name" | "description"} field - The field to validate
   * @param {string} value - The value to validate
   * @returns {string} - Error message or empty string if valid
   */
  const validateField = (field: "name" | "description", value: string): string => {
    if (field === "name") {
      if (!value || value.trim() === "") {
        return t("skill_name_required");
      } else if (/^\d+$/.test(value.trim())) {
        return t("skill_name_not_numeric");
      } else if (value.trim().length < 3) {
        return t("skill_name_length");
      } else if (value.trim().length > 50) {
        return t("skill_name_max_length");
      }

      // Check for duplicate skill names (excluding current skill being edited)
      for (let i = 0; i < localCareerSkills.length; i++) {
        if (editingSkill.index !== -1 && i === editingSkill.index) continue;
        if (localCareerSkills[i].name.toLowerCase() === value.trim().toLowerCase()) {
          return t("skill_name_exists");
        }
      }
    } else if (field === "description") {
      if (!value || value.trim() === "") {
        return t("skill_description_required");
      } else if (value.trim().length > 200) {
        return t("skill_description_max_length");
      } else if (value.trim().length < 3) {
        return t("skill_description_length");
      } else if (/^\d+$/.test(value.trim())) {
        return t("skill_description_not_numeric");
      }
    }
    return "";
  };

  /**
   * Handles input changes in modal - updates form values and shows real-time validation
   * @param {"name" | "description"} field - The field to update
   * @param {string} value - The new value for the field
   */
  const handleInputChange = (field: "name" | "description", value: string) => {
    // Update form values
    const updatedFormValues = {
      ...formValues,
      [field]: value,
    };
    setFormValues(updatedFormValues);

    // Perform real-time validation
    const errorMessage = validateField(field, value);
    setFormErrors({
      ...formErrors,
      [field]: errorMessage,
    });
  };

  /**
   * Saves edited skill to local state with validation
   */
  const handleSaveSkill = () => {
    // Validate form values using the validateField function
    const errors = {
      name: validateField("name", formValues.name),
      description: validateField("description", formValues.description),
    };

    // If there are validation errors, update error state and stop
    if (errors.name || errors.description) {
      setFormErrors(errors);
      return;
    }

    // If validation passes, create updated skill with form values
    const updatedSkill: ISkillData = {
      name: formValues.name.trim(), // Trim whitespace
      description: formValues.description.trim(), // Trim whitespace
    };

    if (editingSkill.index !== -1) {
      // Update existing skill only when save is clicked
      const updatedSkills = [...localCareerSkills];
      updatedSkills[editingSkill.index] = updatedSkill;
      setLocalCareerSkills(updatedSkills);
    }

    // Reset state and close modal
    setShowEditModal(false);
    setEditingSkill({ index: -1, skill: null });
    setFormValues({ name: "", description: "" });
    setFormErrors({ name: "", description: "" }); // Clear validation errors
  };

  /**
   * Handles Save & Next button - save to Redux and navigate
   */
  const handleSaveAndNext = () => {
    // Set loading state
    setIsSaving(true);

    // Save local skills to Redux
    dispatch(
      setSkillsData({
        careerSkills: localCareerSkills,
      })
    );
    setIsSaving(false);
    // Navigate to next page
    router.push(ROUTES.JOBS.ROLE_BASED_SKILLS);
  };

  console.log("===========================>Career Based Skills Page");

  return (
    <div className={style.job_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                <BackArrowIcon
                  onClick={() => {
                    router.push(`${ROUTES.JOBS.GENERATE_JOB}?hiringType=${jobDetails?.hiring_type}`);
                  }}
                />
                {tJobRequirement("top_performance_based_skills")}
                <span> {jobDetails?.title || ""}</span>
              </h2>
            </div>
            <p className="description">
              Based on the job details you provided and the finalized description, we’ve identified the <b>Career-based Skills</b> most critical to
              success. These <b>Career-based skills</b> are essential for basic performance in the role.
              <strong className="color-primary">You can edit or adjust the selected skills at any time.</strong>
            </p>
          </div>
        </div>
        <div className="inner-section career-based-skills">
          <h3 className={style.inner_heading}>
            {tCommon("top")} <span>{tCommon("carrer_based_skills")}</span>
          </h3>
          <div className="row g-4">
            {localCareerSkills.length > 0 ? (
              localCareerSkills.map((skill: ISkillData, index: number) => (
                <div className="col-md-4" key={index}>
                  <div className="career-skill-card">
                    <div className="head">
                      <h3>{skill.name || `Career Skill ${index + 1}`}</h3>
                      <Button className="clear-btn p-0 m-0" onClick={() => handleEditClick(skill, index)}>
                        <EditSkillIcon className="right-img" />
                      </Button>
                    </div>
                    <div className="skill-content">
                      <p>{skill.description || tCommon("no_description_available")}</p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-12">
                <NoDataFoundIcon width={300} height={300} />
                <p> {tCommon("no_carrer_skills_available")} </p>
              </div>
            )}
          </div>
        </div>

        <div className="button-align py-5">
          <Button className="primary-btn rounded-md" onClick={handleSaveAndNext} disabled={isSaving}>
            {tCommon("save_and_next")} {isSaving && <Loader />}
          </Button>
        </div>

        {/* Edit/Add Skill Modal */}
        {showEditModal && (
          <Modal
            title={"Edit Career Skill"}
            isOpen={showEditModal}
            onClose={() => {
              // Close modal and reset states
              setShowEditModal(false);
              setFormValues({ name: "", description: "" });
              setFormErrors({ name: "", description: "" }); // Clear validation errors
            }}
            size="md"
          >
            <div className="p-4">
              <div className="form-group mb-4">
                <label htmlFor="skillName" className="form-label fw-bold mb-2">
                  {tCommon("skill_name")}
                  <span className="text-danger">*</span>
                </label>
                <div className="input-wrapper">
                  <CommonInput
                    id="skillName"
                    value={formValues.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="Enter skill name"
                    className={`form-control ${formErrors.name ? "is-invalid" : ""}`}
                  />
                  {formErrors.name && <div className="invalid-feedback d-block auth-msg error">{formErrors.name}</div>}
                </div>
              </div>

              <div className="form-group mb-4">
                <label htmlFor="skillDescription" className="form-label fw-bold  mb-2">
                  {tCommon("discription")}
                  <span className="text-danger">*</span>
                </label>
                <div className="input-wrapper">
                  <textarea
                    id="skillDescription"
                    value={formValues.description}
                    onChange={(e) => handleInputChange("description", e.target.value)}
                    placeholder="Enter skill description"
                    className={`form-control ${formErrors.description ? "is-invalid" : ""}`}
                    rows={5}
                  />
                  {formErrors.description && <div className="invalid-feedback d-block auth-msg error">{formErrors.description}</div>}
                </div>
              </div>

              <div className="d-flex justify-content-end mt-5">
                <Button className="primary-btn w-100 rounded-md" onClick={handleSaveSkill}>
                  {tCommon("save_cahnges")}
                </Button>
              </div>
            </div>
          </Modal>
        )}
      </div>
    </div>
  );
}

export default CareerBasedSkills;
