import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { JobRequirementState } from "@/interfaces/jobRequirementesInterfaces";

const initialState: JobRequirementState = {
  content: "",
  isGenerated: false,
  generatedAt: null,
};

export const jobRequirementSlice = createSlice({
  name: "jobRequirement",
  initialState,
  reducers: {
    // Set job requirement content
    setJobRequirement: (state, action: PayloadAction<string>) => {
      state.content = action.payload;
      state.isGenerated = true;
      state.generatedAt = new Date().toISOString();
    },
    // Clear job requirement data
    clearJobRequirement: (state) => {
      state.content = "";
      state.isGenerated = false;
      state.generatedAt = null;
    },
  },
});

export const { setJobRequirement, clearJobRequirement } = jobRequirementSlice.actions;

// Selector to use with useSelector
export const selectJobRequirement = (state: { jobRequirement: JobRequirementState }) => state.jobRequirement;

export default jobRequirementSlice.reducer;
