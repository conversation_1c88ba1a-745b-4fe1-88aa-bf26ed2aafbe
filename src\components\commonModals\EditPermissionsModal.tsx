"use client";
import React, { FC, useState, useEffect } from "react";
import Button from "../formElements/Button";
import { getRolePermissionsById, updateRolePermissions } from "@/services/roleService";
import { IProps } from "@/interfaces/rolePermissionInterface";
import Loader from "../loader/Loader";
import { useTranslations } from "next-intl";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import Skeleton from "react-loading-skeleton";
import RefreshAlertIcon from "../svgComponents/RefreshAlertIcon";

interface IPermission {
  id: number;
  name: string;
  description: string;
  checked: boolean;
}

const EditPermissionsModal: FC<IProps> = ({ onClickCancel, onSubmitSuccess, disabled, role }) => {
  const t = useTranslations();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [permissions, setPermissions] = useState<IPermission[]>([]);
  const [originalPermissions, setOriginalPermissions] = useState<IPermission[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [hasAtLeastOneSelected, setHasAtLeastOneSelected] = useState(false);
  const [roleName, setRoleName] = useState(role.name);

  // Fetch permissions from API when component mounts
  useEffect(() => {
    const fetchPermissions = async () => {
      try {
        setIsLoading(true);
        setLoadError(null);

        const response = await getRolePermissionsById(role.id);
        const data = response.data;

        if (data.success && data.data) {
          setRoleName(data.data.role_name);
          // Transform API response to component state format
          const transformedPermissions = data.data.permissions.map((permission) => ({
            id: permission.id,
            name: permission.name,
            description: permission.description,
            checked: permission.selected || false,
          }));
          setPermissions(transformedPermissions);
          setOriginalPermissions(transformedPermissions);

          // Check if at least one permission is selected
          const hasSelected = transformedPermissions.some((p) => p.checked);
          setHasAtLeastOneSelected(hasSelected);
        } else {
          console.error(t("failed_load_permissions"));
          setLoadError(t("failed_load_permissions"));
        }
      } catch (error) {
        console.error(error);
        setLoadError(t("unexpected_error"));
      } finally {
        setIsLoading(false);
      }
    };

    fetchPermissions();
  }, [role.id]);

  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    const updatedPermissions = permissions.map((permission) => ({
      ...permission,
      checked: isChecked,
    }));
    setPermissions(updatedPermissions);

    // Check if permissions have changed from original state
    const hasAnyChanges = updatedPermissions.some((updatedPerm, index) => updatedPerm.checked !== originalPermissions[index].checked);
    setHasChanges(hasAnyChanges);

    // Update whether at least one permission is selected
    setHasAtLeastOneSelected(isChecked);
  };

  const handlePermissionChange = (id: number, checked: boolean) => {
    const updatedPermissions = permissions.map((permission) => (permission.id === id ? { ...permission, checked } : permission));
    setPermissions(updatedPermissions);

    // Check if any permission has changed from its original state
    const hasAnyChanges = updatedPermissions.some((updatedPerm) => {
      const originalPerm = originalPermissions.find((p) => p.id === updatedPerm.id);
      return originalPerm && originalPerm.checked !== updatedPerm.checked;
    });
    setHasChanges(hasAnyChanges);

    // Check if at least one permission is selected
    const hasSelected = updatedPermissions.some((p) => p.checked);
    setHasAtLeastOneSelected(hasSelected);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setIsSubmitting(true);
      setSubmitError(null);

      // Get only the IDs of checked permissions
      const permissionIds = permissions.reduce((acc: number[], p) => {
        if (p.checked) acc.push(p.id);
        return acc;
      }, []);

      // Make the API call to update role permissions with only permissionIds
      const response = await updateRolePermissions(role.id, permissionIds);
      const data = response.data;
      if (data.success) {
        // Call the success callback with success message and updated role data
        onSubmitSuccess(t("permissions_updated_success"), data.data);
        // Close the modal
        onClickCancel();
      } else {
        console.error(data.message);
        setSubmitError(data.message || t("failed_update_permissions"));
      }
    } catch (error) {
      console.error(error);
      setSubmitError(t("unexpected_error"));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered modal-lg">
        <div className="modal-content">
          <div className="modal-header justify-content-center pb-0">
            <h4 className="m-0">
              {t("edit_permissions_for")} <span>{roleName}</span>
            </h4>
            <Button className="modal-close-btn" onClick={onClickCancel} disabled={isSubmitting}>
              <ModalCloseIcon />
            </Button>
          </div>
          <div className="modal-body pt-3">
            <p className="mb-5 text-center px-3">{t("permissions_description")}</p>
            {isLoading ? (
              <div className="permissions-card p-5 w-100 mb-4">
                <Skeleton height={20} width="100%" borderRadius={4} count={8} className="mb-4" />
              </div>
            ) : loadError ? (
              <div className="alert alert-danger mb-3" role="alert">
                {loadError}
                <Button
                  className="clear-btn p-0"
                  onClick={() => {
                    setIsLoading(true);
                    getRolePermissionsById(role.id)
                      .then((response) => {
                        const data = response.data;
                        if (data.success && data.data) {
                          setRoleName(data.data.role_name);
                          const transformedPermissions = data.data.permissions.map((permission) => ({
                            id: permission.id,
                            name: permission.name,
                            description: permission.description,
                            checked: permission.selected || false,
                          }));
                          setPermissions(transformedPermissions);
                          setLoadError(null);
                        } else {
                          setLoadError(t("failed_load_permissions"));
                        }
                      })
                      .catch((error) => {
                        console.error("Error fetching permissions:", error);
                        setLoadError(t("unexpected_error"));
                      })
                      .finally(() => setIsLoading(false));
                  }}
                >
                  <RefreshAlertIcon />
                </Button>
              </div>
            ) : (
              <form onSubmit={handleSubmit}>
                <div className="permissions-card p-5">
                  <h3>{t("role_permissions")}</h3>
                  {permissions.length > 0 ? (
                    <>
                      <label className="container-checkbox">
                        {t("select_all")}
                        <input
                          type="checkbox"
                          onChange={handleSelectAll}
                          checked={permissions.length > 0 && permissions.every((p) => p.checked)}
                          disabled={isSubmitting || disabled}
                        />
                        <span className="checkmark" />
                      </label>
                      <ul className="checbox-group">
                        {permissions.map((permission) => (
                          <li key={permission.id}>
                            <label className="container-checkbox">
                              <div className="permission-item">
                                <div className="permission-name">{permission.name}</div>
                                <div className="permission-description">{permission.description}</div>
                              </div>
                              <input
                                type="checkbox"
                                checked={permission.checked}
                                onChange={(e) => handlePermissionChange(permission.id, e.target.checked)}
                                disabled={isSubmitting || disabled}
                              />
                              <span className="checkmark" />
                            </label>
                          </li>
                        ))}
                      </ul>
                    </>
                  ) : (
                    <p className="text-center py-3">{t("no_permissions_found")}</p>
                  )}
                </div>

                {submitError && (
                  <div className="alert alert-danger mb-3" role="alert">
                    {submitError}
                  </div>
                )}

                <div className="button-align mt-5">
                  <Button type="button" className="dark-outline-btn rounded-md w-100" onClick={onClickCancel} disabled={isSubmitting || disabled}>
                    {t("cancel_department_edit")}
                  </Button>
                  <Button
                    type="submit"
                    className={`primary-btn rounded-md w-100 ${isSubmitting || disabled || permissions.length === 0 || !hasChanges || !hasAtLeastOneSelected ? "truly-disabled" : ""}`}
                    disabled={isSubmitting || disabled || permissions.length === 0 || !hasChanges || !hasAtLeastOneSelected}
                    title={!hasChanges ? t("make_changes_to_enable_save") : !hasAtLeastOneSelected ? t("at_least_one_permission") : ""}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader /> <span className="ms-2">{t("saving")}</span>
                      </>
                    ) : (
                      t("save_permissions")
                    )}
                  </Button>
                </div>
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditPermissionsModal;
