import { IWalkthroughState, updateWalkthrough } from "@/redux/slices/walkthroughSlice";
import { updateWalkthroughStatus } from "@/services/walkthroughServices";
import { useState, useEffect, useCallback, useRef } from "react";
import { CallBackProps, STATUS, EVENTS, Step } from "react-joyride-react-19";
import { useDispatch, useSelector } from "react-redux";

interface WalkthroughOptions {
  resetOnRevisit?: boolean;
  onTourStart?: () => void;
  onTourEnd?: () => void;
}

/**
 * Custom hook to manage walkthrough/tour functionality
 *
 * @param pageId - Unique identifier for the page (used in localStorage key)
 * @param steps - The walkthrough step configuration
 * @param isDataLoaded - Boolean indicating if the page data is loaded
 * @param startDelay - Delay in ms before auto-starting the tour
 * @param options - Additional options for the walkthrough
 */
export const useWalkthrough = (pageId: string, steps: Step[], isDataLoaded: boolean, startDelay: number = 1000, options: WalkthroughOptions = {}) => {
  const [runTour, setRunTour] = useState(false);
  const hasStartedTour = useRef(false);
  // Add a flag to track if the walkthrough status has been updated
  const hasUpdatedStatus = useRef(false);
  const walkthroughState = useSelector((state: { walkthrough: IWalkthroughState }) => state.walkthrough);
  const dispatch = useDispatch();

  // Check if elements are ready for the tour
  const checkElementsReady = useCallback((stepsToCheck: Step[]) => {
    // Check if all steps have targets that exist in the DOM
    return stepsToCheck.every((step) => {
      if (typeof step.target === "string") {
        return document.querySelector(step.target) !== null;
      }
      return true;
    });
  }, []);

  useEffect(() => {
    if (isDataLoaded && !hasStartedTour.current) {
      const hasSeenTour = options.resetOnRevisit ? false : walkthroughState.visitedWalkthroughs.some((walkthrough) => walkthrough === pageId);
      const isTourSeen = Boolean(hasSeenTour);

      if (!isTourSeen) {
        // Wait for start delay and elements to be ready before starting tour
        const timeoutId = setTimeout(() => {
          // Check if all tour targets exist in DOM
          if (checkElementsReady(steps)) {
            hasStartedTour.current = true;

            // Scroll to first step before starting tour
            if (steps.length > 0 && typeof steps[0].target === "string") {
              const firstTarget = document.querySelector(steps[0].target as string);
              if (firstTarget) {
                // Scroll to the first target element
                firstTarget.scrollIntoView({
                  behavior: "smooth",
                  block: "center",
                });

                // Small delay to allow scroll to finish before starting tour
                setTimeout(() => {
                  setRunTour(true);
                  options.onTourStart?.();
                }, 400);
                return;
              }
            }

            // If no scrolling needed or target not found, start tour directly
            setRunTour(true);
            options.onTourStart?.();
          } else {
            console.warn(`Some step targets for tour '${pageId}' could not be found in DOM. Tour will not start.`);
          }
        }, startDelay);

        // Clean up timeout if component unmounts
        return () => clearTimeout(timeoutId);
      }
    }
  }, [isDataLoaded, pageId, startDelay, checkElementsReady, options, steps, walkthroughState.visitedWalkthroughs]);

  const handleJoyrideCallback = useCallback(
    (data: CallBackProps) => {
      console.log(">>>>>>>>>>>>>>>>>>>>>>>>>calling handleJoyrideCallback data", data);

      const { status, type, step, action } = data;

      // Handle tour completion or explicit skip action
      if ((status === STATUS.FINISHED || status === STATUS.SKIPPED) && !hasUpdatedStatus.current) {
        // Set the flag to prevent duplicate API calls
        hasUpdatedStatus.current = true;

        updateWalkthroughStatus(pageId);

        // Update Redux state if backend update is successful
        dispatch(updateWalkthrough(pageId));
        setRunTour(false);
        // options.onTourEnd?.();
      }

      // Auto-scroll to the target element when advancing steps
      if (type === EVENTS.STEP_AFTER || type === EVENTS.TARGET_NOT_FOUND) {
        // If there are more steps and the user clicked "next" or "skip"
        if (action === "next" || action === "skip") {
          const targetElement = document.querySelector(step.target as string);
          if (targetElement) {
            // Using a small timeout to ensure the UI has updated before scrolling
            setTimeout(() => {
              targetElement.scrollIntoView({
                behavior: "smooth",
                block: "center",
              });
            }, 50);
          }
        }
      }

      // Log any errors but don't crash
      if (type === "error" && process.env.NODE_ENV !== "production") {
        // Use allowed console methods only
        console.error("--- Joyride Error ---");
        console.error("Tour error on page:", pageId);
        console.error(data);
        console.error("---------------------");
      }
    },
    [pageId, options, dispatch]
  );

  const startTour = useCallback(() => {
    hasStartedTour.current = true;
    setRunTour(true);
    options.onTourStart?.();
  }, [options]);

  return {
    runTour,
    handleJoyrideCallback,
    startTour,
  };
};

export default useWalkthrough;
