import { JobSelectOption } from "@/interfaces/jobRequirementesInterfaces";

/**
 * Job category options
 */
export const CATEGORY_OPTION: JobSelectOption[] = [
  { label: "Full time", value: "full_time" },
  { label: "Part time", value: "part_time" },
  { label: "Contract", value: "contract" },
  { label: "Internship", value: "internship" },
  { label: "Freelance", value: "freelance" },
];

/**
 * Salary cycle options
 */
export const SALARY_CYCLE_OPTIONS: JobSelectOption[] = [
  { label: "Per Hour", value: "per hour" },
  { label: "Per Month", value: "per month" },
  { label: "Per Annum", value: "per annum" },
];

/**
 * Location type options
 */
export const LOCATION_TYPE_OPTIONS: JobSelectOption[] = [
  { label: "Remote", value: "remote" },
  { label: "Hybrid", value: "hybrid" },
  { label: "On-site", value: "onsite" },
];

/**
 * Tone style options
 */
export const TONE_STYLE_OPTIONS: JobSelectOption[] = [
  { label: "Professional & Formal", value: "Professional_Formal" },
  { label: "Conversational & Approachable", value: "Conversational_Approachable" },
  { label: "Bold & Energetic", value: "Bold_Energetic" },
  { label: "Inspirational & Mission-Driven", value: "Inspirational_Mission-Driven" },
  { label: "Technical & Precise", value: "Technical_Precise" },
  { label: "Creative & Fun", value: "Creative_Fun" },
  { label: "Inclusive & Human-Centered", value: "Inclusive_Human-Centered" },
  { label: "Minimalist & Straightforward", value: "Minimalist_Straightforward" },
];

/**
 * Compliance options
 */
export const COMPLIANCE_OPTIONS: JobSelectOption[] = [
  { label: "Equal Employment Opportunity (EEO) Statement", value: "Equal Employment Opportunity (EEO) Statement" },
  {
    label: "Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)",
    value: "Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)",
  },
  { label: "Disability Accommodation Statement", value: "Disability Accommodation Statement" },
  {
    label: "Veterans Preference Statement (For Government Agencies and Federal Contractors)",
    value: "Veterans Preference Statement (For Government Agencies and Federal Contractors)",
  },
  { label: "Diversity & Inclusion Commitment", value: "Diversity & Inclusion Commitment" },
  {
    label: "Pay Transparency Non-Discrimination Statement (For Federal Contractors)",
    value: "Pay Transparency Non-Discrimination Statement (For Federal Contractors)",
  },
  {
    label: "Background Check and Drug-Free Workplace Policy (If Applicable)",
    value: "Background Check and Drug-Free Workplace Policy (If Applicable)",
  },
  { label: "Work Authorization & Immigration Statement", value: "Work Authorization & Immigration Statement" },
];

export const EXPERIENCE_LEVEL_OPTIONS: JobSelectOption[] = [
  { label: "General", value: "General" },
  { label: "No experience necessary", value: "No experience necessary" },
  { label: "Entry-Level Position", value: "Entry-Level Position" },
  { label: "Mid-Level Professional", value: "Mid-Level Professional" },
  { label: "Senior/Experienced Professional", value: "Senior/Experienced Professional" },
  { label: "Managerial/Executive Level", value: "Managerial/Executive Level" },
  { label: "Specialized Expert", value: "Specialized Expert" },
];

export const DEPARTMENT_OPTION: JobSelectOption[] = [
  { label: "IT", value: "IT" },
  { label: "HR", value: "HR" },
  { label: "Marketing", value: "Marketing" },
  { label: "Finance", value: "Finance" },
  { label: "Sales", value: "Sales" },
];
/**
 * Constants for file upload validation
 */
export const FILE_SIZE_LIMIT = 5 * 1024 * 1024; // 5MB
export const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
export const FILE_TYPE = "application/pdf";
export const FILE_NAME = ".pdf";

/**
 * Remove all $ and space symbols to clean the input
 */
export const SALARY_REMOVE_SYMBOL_REGEX = /[\$\s]/g;

/**
 * Currency symbol
 */
export const CURRENCY_SYMBOL = "$";

/**
 * Button list for SunEditor
 */
export const SUN_EDITOR_BUTTON_LIST = [
  ["font", "fontSize", "formatBlock"],
  ["bold", "underline", "italic"],
  ["fontColor", "hiliteColor"],
  ["align", "list", "lineHeight"],
];

/**
 * HiringType Select [Internal,External]
 */
export const HIRING_TYPE = {
  INTERNAL: "internal",
  EXTERNAL: "external",
};

/**
 * Skill categories
 */
export const SKILL_CATEGORY = {
  Personal_Health: "Personal Health",
  Social_Interaction: "Social Interaction",
  Mastery_Of_Emotions: "Mastery of Emotions",
  Mentality: "Mentality",
  Cognitive_Abilities: "Cognitive Abilities",
};

/**
 * Application status values
 */
export const APPLICATION_STATUS = {
  PENDING: "Pending",
  APPROVED: "Approved",
  REJECTED: "Rejected",
  HIRED: "Hired",
  ON_HOLD: "On-Hold",
  FINAL_REJECT: "Final-Reject",
};

/**
 * Skill type (for filtering/deselection logic etc.)
 */
export const SKILL_TYPE = {
  ROLE: "role",
  CULTURE: "culture",
};

export const EDIT_SKILLS_TYPE = {
  ROLE: "role",
  CULTURE: "culture",
  AVAILABLE: "available",
};

/**
 * Skill type (for filtering/deselection logic etc.)
 */
export type SkillType = (typeof SKILL_TYPE)[keyof typeof SKILL_TYPE];

/**
 * HiringType key for searchParams
 */
export const HIRING_TYPE_KEY = "hiringType";

export const CURSOR_POINT = { cursor: "pointer" };

export const COMPLIANCE_LINK = "https://s9-interview-assets.s3.us-east-1.amazonaws.com/A+comprehensive+compliance+section.pdf";

// Dynamic uploading messages for job generation
export const JOB_GENERATION_UPLOAD_MESSAGES = [
  "Analyzing your job description...",
  "Extracting key requirements...",
  "Processing document content...",
  "Identifying skills and qualifications...",
  "Parsing job details...",
  "Almost ready...",
];

// Dynamic messages for job requirement generation process
export const JOB_REQUIREMENT_GENERATION_MESSAGES = [
  "Generating job requirements...",
  "Analyzing role specifications...",
  "Creating skill assessments...",
  "Building competency framework...",
  "Finalizing job structure...",
  "Almost complete...",
];
export const JOB_SKILLS_GENERATION_MESSAGES = [
  "Generating skills profile...",
  "Analyzing career-based skills for this position...",
  "Determining role-specific performance attributes...",
  "Evaluating culture-specific skills ..",
  "Creating comprehensive skills profile...",
  "Finalizing the optimal skills requirements...",
];

// Dynamic messages for manual candidate upload process
export const MANUAL_CANDIDATE_UPLOAD_MESSAGES = [
  "Uploading candidate files...",
  "Processing resumes and assessments...",
  "Analyzing candidate profiles...",
  "Validating candidate information...",
  "Storing candidate data...",
  "Finalizing candidate uploads...",
];

// Dynamic messages for interview scheduling/updating process
export const INTERVIEW_SCHEDULING_MESSAGES = [
  "Scheduling interview...",
  "Validating interview details...",
  "Checking interviewer availability...",
  "Setting up calendar events...",
  "Sending notifications...",
  "Finalizing interview schedule...",
];

export const FINAL_ASSESSMENT_MESSAGES = [
  "Creating final assessment...",
  "Analyzing candidate data...",
  "Generating assessment questions...",
  "Finalizing assessment structure...",
  "Almost ready...",
];
