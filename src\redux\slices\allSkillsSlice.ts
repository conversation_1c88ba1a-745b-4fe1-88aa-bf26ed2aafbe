import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../store";
import { AllSkillsState, ISkillCategory, ISkillItem } from "@/interfaces/jobRequirementesInterfaces";

// Define the initial state
const initialState: AllSkillsState = {
  categories: [],
  loading: false,
  error: null,
};

// Create the slice
export const allSkillsSlice = createSlice({
  name: "allSkills",
  initialState,
  reducers: {
    fetchSkillsStart: (state: AllSkillsState) => {
      state.loading = true;
      state.error = null;
    },
    fetchSkillsSuccess: (state: AllSkillsState, action: PayloadAction<ISkillCategory[]>) => {
      state.categories = action.payload;
      state.loading = false;
      state.error = null;
    },
    fetchSkillsFailure: (state: AllSkillsState, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
    updateSkillItem: (
      state: AllSkillsState,
      action: PayloadAction<{
        categoryType: string;
        skillId: number;
        updatedSkill: Partial<ISkillItem>;
      }>
    ) => {
      const { categoryType, skillId, updatedSkill } = action.payload;
      const categoryIndex = state.categories.findIndex((cat) => cat.type === categoryType);

      if (categoryIndex !== -1) {
        const skillIndex = state.categories[categoryIndex].items.findIndex((item) => item.id === skillId);

        if (skillIndex !== -1) {
          state.categories[categoryIndex].items[skillIndex] = {
            ...state.categories[categoryIndex].items[skillIndex],
            ...updatedSkill,
          };
        }
      }
    },
  },
});

// Export actions
export const { fetchSkillsStart, fetchSkillsSuccess, fetchSkillsFailure, updateSkillItem } = allSkillsSlice.actions;

// Define selectors
export const selectAllSkills = (state: RootState) => state.allSkills.categories;
export const selectSkillsLoading = (state: RootState) => state.allSkills.loading;
export const selectSkillsError = (state: RootState) => state.allSkills.error;

// Export specific category selector
export const selectSkillsByCategory = (categoryType: string) => (state: RootState) =>
  state.allSkills.categories.find((cat: ISkillCategory) => cat.type === categoryType)?.items || [];

// Export reducer
export default allSkillsSlice.reducer;
