"use client";
import { useState, useEffect } from "react";

export const useAuth = () => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasLoadedOnce, setHasLoadedOnce] = useState<boolean>(false); // NEW

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        setIsLoading(true);

        const response = await fetch("/api/auth/status", {
          method: "GET",
          credentials: "include",
        });

        if (response.ok) {
          const data = await response.json();
          setIsAuthenticated(data.isAuthenticated);
        } else {
          setIsAuthenticated(false);
        }
      } catch (error) {
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
        setHasLoadedOnce(true); // Mark as loaded at least once
      }
    };

    checkAuthStatus();

    const interval = setInterval(checkAuthStatus, 30000);

    const handleFocus = () => {
      checkAuthStatus();
    };

    window.addEventListener("focus", handleFocus);

    return () => {
      clearInterval(interval);
      window.removeEventListener("focus", handleFocus);
    };
  }, []);

  const resetAuthLoaded = () => setHasLoadedOnce(false);

  return { isAuthenticated, isLoading, hasLoadedOnce, resetAuthLoaded };
};
