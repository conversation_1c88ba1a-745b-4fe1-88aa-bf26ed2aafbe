import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

export async function GET(req: NextRequest) {
  try {
    // Use the same logic as your middleware
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
    const isAuthenticated = !!token;

    return NextResponse.json({
      isAuthenticated,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error checking auth status:", error);
    return NextResponse.json(
      {
        isAuthenticated: false,
        error: "Failed to check auth status",
      },
      { status: 500 }
    );
  }
}
