"use client";
import React, { useEffect } from "react";
import { yupResolver } from "@hookform/resolvers/yup";
import Image from "next/image";
import { Controller, useForm } from "react-hook-form";
import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import toast from "react-hot-toast";
import OtpInput from "react-otp-input";
import "react-phone-input-2/lib/style.css";

import logo from "../../../../public/assets/images/logo.svg";
import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import styles from "@/styles/auth.module.scss";
import { SignUpFormValues, verifyOTPValidation } from "@/validations/authValidations";
import { resendOTP, verifyOTP } from "@/services/authServices";
import routes from "@/constants/routes";
import { toastMessageSuccess, toastMessageError, decryptInfo, encryptInfo } from "@/utils/helper";
import ROUTES from "@/constants/routes";
import { OTP_TYPE } from "@/constants/commonConstants";
import { useTranslate } from "@/utils/translationUtils";
import Loader from "@/components/loader/Loader";
import Link from "next/link";

const Verify = () => {
  const translate = useTranslate();
  const [loading, setLoading] = useState(false);
  const [resendOtpLoading, setResendOtpLoading] = useState(false);
  const [parsedInfo, setParsedInfo] = useState<(SignUpFormValues & { type: typeof OTP_TYPE | string }) | null>(null);
  const [counter, setCounter] = useState(0);
  const [linkDisabled, setLinkDisabled] = useState(true);
  const router = useRouter();
  const searchParams = useSearchParams();
  const info = searchParams?.get("info");
  const passwordType = searchParams?.get("passwordType");

  useEffect(() => {
    try {
      if (info) {
        const decryptedInfo = decryptInfo(info);
        if (decryptedInfo) {
          const parsed = JSON.parse(decryptedInfo);
          setParsedInfo(parsed);
        } else {
          moveToLogin();
        }
      } else {
        moveToLogin();
      }
    } catch (error) {
      console.log("error", error);
      moveToLogin();
    }
  }, [info]);

  const moveToLogin = () => {
    toastMessageError(translate("invalid_or_malformed_url_parameters"));
    router.replace(ROUTES.LOGIN);
  };

  const {
    control,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(verifyOTPValidation(translate)),
  });

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (counter > 0) timer = setInterval(() => setCounter(counter - 1), 1000);
    else if (counter === 0) {
      setLinkDisabled(false);
    }
    return () => clearInterval(timer);
  }, [counter]);

  const onSubmit = async (data: { otp: number }) => {
    if (!parsedInfo) {
      return;
    }
    setLoading(true);
    toast.dismiss();
    try {
      setLoading(true);
      const payLoad = {
        otp: data?.otp.toString(),
        ...parsedInfo,
        passwordType: passwordType || "",
      };
      const result = await verifyOTP(payLoad);

      if (result?.data?.success) {
        if (parsedInfo?.type === OTP_TYPE.FORGOT_PASSWORD) {
          const info = encryptInfo(
            JSON.stringify({
              email: parsedInfo?.email,
              otp: data?.otp.toString(),
            })
          );
          const encodedInfo = encodeURIComponent(info);
          router.replace(`${routes.RESET_PASSWORD}?info=${encodedInfo}`);
        } else {
          router.replace(routes.LOGIN);
        }
        toastMessageSuccess(translate(result?.data?.message));
        setLoading(false);
      } else {
        setLoading(false);
        toastMessageError(translate(result?.data?.message));
      }
    } catch (error) {
      console.error(error);
      toastMessageError(translate("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  const handleResendOtp = async () => {
    setResendOtpLoading(true);
    try {
      setLinkDisabled(true);

      const payload = {
        email: parsedInfo?.email || "",
        type: parsedInfo?.type || "",
        name: parsedInfo?.firstName || "",
      };
      const result = await resendOTP(payload);
      if (result?.data?.success) {
        // Start timer and show toast message simultaneously
        setCounter(30);
        setLinkDisabled(true);
        toastMessageSuccess(translate(result?.data?.message as string));
        setCounter(30);
        reset();
      } else {
        toastMessageError(translate((result?.data?.message as string) ?? "something_went_wrong"));
      }
    } catch (error) {
      console.error(error);
      toastMessageError(translate("something_went_wrong"));
    } finally {
      setResendOtpLoading(false);
    }
  };

  const handleOtpChange = (phone: string) => {
    setValue("otp", phone === "0" ? 0 : Number(phone));
  };

  return (
    <div className={styles.auth_main}>
      <div className="container">
        <div className="row">
          <div className={styles.user_auth_main}>
            <div className="container">
              <div className="row row-center">
                <div className={`${styles.hero_image} col-md-6`}>
                  {/* <div className={styles.client_signature_box}>
                    <p>
                      The challenge is great, the effort is extraordinary, the achievement is life changing, and the impact will become your legacy.
                      Where are you now and what are you willing to change to get to where you want to be?
                    </p>
                    <Image src={ClientSignature} alt="client" />
                  </div> */}
                </div>
                <div className="col-md-6">
                  <div className={styles.form_main}>
                    <div className="text-center">
                      <Image src={logo} alt="logo" className={styles.logo} width={200} height={80} />
                      <h1>
                        {translate("verify")} <span>{translate("code")}</span>
                      </h1>
                    </div>
                    <form onSubmit={handleSubmit(onSubmit)}>
                      <InputWrapper>
                        <InputWrapper.Label htmlFor="otp" required>
                          {translate("enter_verification_code")}
                        </InputWrapper.Label>
                        <Controller
                          name="otp"
                          control={control}
                          render={({ field }) => (
                            <div
                              className="otp-main"
                              onPaste={(e) => {
                                e.preventDefault();
                                const pastedData = e.clipboardData.getData("text/plain").trim();
                                if (pastedData.match(/^[0-9]+$/) && pastedData.length <= 4) {
                                  handleOtpChange(pastedData.slice(0, 4));
                                }
                              }}
                            >
                              <OtpInput
                                numInputs={4}
                                value={field.value as unknown as string}
                                onChange={handleOtpChange}
                                renderInput={(props) => <input {...props} />}
                                inputType="number"
                                shouldAutoFocus
                              />
                            </div>
                          )}
                        />
                        <InputWrapper.Error message={errors?.otp?.message || ""} />
                      </InputWrapper>

                      <Button loading={loading} disabled={loading} className="primary-btn rounded-md w-100 mt-5">
                        {translate("verify")}
                      </Button>
                    </form>
                    {resendOtpLoading || !linkDisabled ? (
                      <Link
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          if (!resendOtpLoading && !loading) {
                            handleResendOtp();
                          }
                        }}
                        className={`d-block text-center mt-4 ${styles.signup_link} ${resendOtpLoading || loading ? "text-muted" : ""}`}
                        style={{ textDecoration: "none", cursor: resendOtpLoading || loading ? "default" : "pointer" }}
                      >
                        {resendOtpLoading ? (
                          <>
                            {translate("resend_verification_code")} <Loader />
                          </>
                        ) : (
                          translate("resend_verification_code")
                        )}
                      </Link>
                    ) : (
                      <p className="text-center mt-3  ">
                        {counter > 0
                          ? `${translate("resend_access_code_in")} ${counter} ${translate("seconds")}`
                          : `${translate("resend_access_code_in")}`}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Verify;
