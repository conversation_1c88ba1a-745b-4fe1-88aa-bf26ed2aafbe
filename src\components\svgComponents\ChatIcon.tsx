import React from "react";

type EditIconProps = {
  className?: string;
};

function ChatIcon({ className }: EditIconProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 32 29" className={className} fill="none">
      <path d="M9.41406 13.5625H11.2891V15.4375H9.41406V13.5625Z" />
      <path d="M10.3516 6.03125C8.79644 6.03125 7.53125 7.29644 7.53125 8.85156H9.40625C9.40625 8.33031 9.83031 7.90625 10.3516 7.90625C10.8728 7.90625 11.2969 8.33031 11.2969 8.85156C11.2969 9.37281 10.8728 9.79688 10.3516 9.79688H9.41406V12.6172H11.2891V11.5117C12.3847 11.1244 13.1719 10.0782 13.1719 8.85156C13.1719 7.29644 11.9067 6.03125 10.3516 6.03125Z" />
      <path d="M32 18.2656C32 12.5578 27.3563 7.91406 21.6484 7.91406C21.2087 7.91406 20.7698 7.94263 20.3353 7.99769C19.1321 3.61369 15.1124 0.382812 10.3516 0.382812C4.64369 0.382812 0 5.0265 0 10.7344C0 12.2305 0.311375 13.6701 0.925875 15.018L0.0029375 21.084L6.062 20.1573C7.41162 20.7736 8.85325 21.0859 10.3516 21.0859C10.7955 21.0859 11.2329 21.0578 11.6623 21.0033C12.8771 25.4667 16.9382 28.6172 21.6484 28.6172C23.1468 28.6172 24.5884 28.3049 25.938 27.6886L31.9971 28.6152L31.0741 22.5492C31.6886 21.2013 32 19.7617 32 18.2656ZM6.35325 18.2159L2.24012 18.845L2.86675 14.7266C2.23913 13.5404 1.86888 12.1187 1.875 10.7344C1.875 6.06038 5.67756 2.25781 10.3516 2.25781C15.0256 2.25781 18.8281 6.06038 18.8281 10.7344C18.8281 15.4084 15.0256 19.2109 10.3516 19.2109C8.96394 19.217 7.54469 18.8471 6.35325 18.2159ZM29.7598 26.3763L25.6467 25.7472C24.4553 26.3784 23.0361 26.7483 21.6484 26.7423C17.8223 26.7423 14.5198 24.2042 13.4961 20.5971C15.4084 19.9861 17.0809 18.8337 18.332 17.3204H26.3516V15.4454H19.5677C20.2934 14.0315 20.7031 12.4299 20.7031 10.7344C20.7031 10.4352 20.6896 10.1391 20.6646 9.84625C20.9904 9.80862 21.3191 9.78913 21.6484 9.78913C26.3224 9.78906 30.125 13.5916 30.125 18.2656C30.1364 19.6178 29.7707 21.0426 29.1332 22.2579L29.7598 26.3763Z" />
      <path d="M16.9453 19.2109H26.3516V21.0859H16.9453V19.2109Z" />
    </svg>
  );
}

export default ChatIcon;
