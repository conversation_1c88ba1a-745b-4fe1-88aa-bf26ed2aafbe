const RefreshIcon = (props: { className?: string }) => {
  const { className } = props;
  return (
    <div>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="25"
        height="25"
        viewBox="0 0 24 24"
        fill="none"
        className={className}
        stroke="#000000"
        style={{ fill: "none", marginRight: "5px" }}
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4" />
        <path d="M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4" />
      </svg>
    </div>
  );
};

export default RefreshIcon;
