@use "./abstracts" as *;
.auth_main {
  background: url("../../public/assets/images/main-banner.png") no-repeat center;
  background-size: cover;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  // padding: 40px;
  .signup_link {
    color: $primary;
    font-weight: $medium;
    cursor: pointer;
  }
  .user_auth_main {
    border-radius: 3.2rem;
    overflow: hidden;
    background: $white;
    padding: 0;
    .hero_image {
      background: url(../../public/assets/images/client-fade.png) no-repeat top;
      background-size: cover;
      object-fit: cover;
      padding: 2rem;
      display: flex;
      // align-items: center;
      flex-direction: column;
      justify-content: flex-end;
      min-height: 620px;
      background-position: center;
      .client_signature_box {
        background: rgba($white, 0.13);
        border-radius: 30px;
        padding: 2.5rem;
        backdrop-filter: blur(80px);
        p {
          color: $white;
          font-weight: $light;
          font-size: 1.4rem;
          line-height: 2.2rem;
          margin-bottom: 3rem;
        }
      }

      @media only screen and (max-width: 767px) {
        display: none;
      }
    }
    .form_main {
      padding: 5rem 5rem;
      position: relative;
      // height: 815px;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .sign_up_scrollbar {
        max-height: 50vh;
        overflow-y: auto;
        overflow-x: hidden;
        padding-right: 15px;
      }
      .logo {
        width: 210px;
        min-width: 210px;
        height: 60px;
        object-fit: contain;
        margin-bottom: 30px;
        cursor: pointer;
      }
      h1 {
        font-size: $heading-md;
        font-weight: $extraBold;
        color: $primary;
        margin-bottom: 40px;

        span {
          color: $dark;
        }
      }
      .form_sub_heading {
        margin-bottom: 3rem;
        font-size: 1.6rem;
      }
      .bottom_link {
        font-size: 1.6rem;
        font-weight: $regular;
        color: $primary;
      }
      .signup-link {
        color: $primary;
      }
      @media only screen and (max-width: 767px) {
        padding: 5rem 2rem;
        .sign_up_scrollbar {
          max-height: 100% !important;
          overflow-y: auto;
          overflow-x: hidden;
          padding-right: 0px;
        }
      }
    }
    .bottom_link {
      font-size: $text-sm;
      font-weight: $regular;
      color: $dark;
      text-align: center;
      margin-top: 20px;
      a {
        color: $primary;
        font-weight: $bold;
        padding: 0 5px;
        text-decoration: underline !important;
      }
    }

    .back_icon {
      svg {
        position: absolute;
        left: 2rem;
        top: 2.5rem;
        cursor: pointer;
        width: 2rem;
        height: 2rem;
        object-fit: contain;
      }
    }
  }
  // mobile screen css
  @media (max-width: 767px) {
    padding: 15px;
    .user_auth_main {
      border-radius: 2.8rem;
      .form_main {
        padding: 30px 15px;
        form {
          max-width: auto !important;
        }

        .logo {
          width: 150px;
          min-width: 150px;
          height: 40px;
          margin-bottom: 20px;
          cursor: pointer;
        }
        h1 {
          font-size: $text-xxl;
          margin-bottom: 20px;
        }
        .form_sub_heading {
          margin-bottom: 20px;
          font-size: $text-sm;
        }
        .bottom_link {
          font-size: $text-sm;
        }
      }
      .bottom_link {
        margin-top: 15px;
      }

      // .back_icon {
      //   svg {
      //     position: absolute;
      //     left: 2rem;
      //     top: 2.5rem;
      //     cursor: pointer;
      //     width: 2rem;
      //     height: 2rem;
      //     object-fit: contain;
      //   }
      // }
    }
  }
  @media (min-width: 767px) and (max-width: 991px) {
    padding: 0;
    .user_auth_main {
      .hero_image {
        padding: 0;
        min-height: 480px;
      }
      .form_main {
        padding: 20px 15px;
        .logo {
          width: 150px;
          min-width: 150px;
          height: 40px;
          margin-bottom: 20px;
          cursor: pointer;
        }
        h1 {
          font-size: $text-xxl;
        }
        .form_sub_heading {
          font-size: $text-sm;
        }
        .bottom_link {
          font-size: $text-sm;
        }
      }
    }
  }
}
