"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import Button from "../formElements/Button";
import Image from "next/image";
import Logo from "../../../public/assets/images/logo.svg";
import styles from "@/styles/header.module.scss";
import { usePathname, useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import ROUTES from "@/constants/routes";
import { STRATUM9_MAIN_WEB_URL } from "@/constants/commonConstants";

const HomeHeader = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { isAuthenticated, isLoading, hasLoadedOnce } = useAuth();

  const [scrolling, setScrolling] = useState(false);

  const pathname = usePathname();
  const navigate = useRouter();

  console.log(">>>>>>>>>>>>>>>>>>>>>>pathname", pathname);
  console.log("Authentication status:", { isAuthenticated, isLoading });

  // for handling the color of the header on homepage
  useEffect(() => {
    const handleScroll = (event: Event) => {
      const target = event.target as HTMLElement;
      const scrollTop = target.scrollTop;

      console.log("🚀 SCROLL EVENT! scrollTop:", scrollTop);

      if (scrollTop > 15) {
        setScrolling(true);
      } else {
        setScrolling(false);
      }
    };

    // Find the scroll-content element
    const scrollContainer = document.querySelector(".scroll-content");

    if (scrollContainer) {
      console.log("✅ Found scroll-content container, attaching listener");
      scrollContainer.addEventListener("scroll", handleScroll, { passive: true });

      return () => {
        scrollContainer.removeEventListener("scroll", handleScroll);
      };
    } else {
      console.log("❌ scroll-content container not found, falling back to window");
      // Fallback to window scroll for other pages
      const windowScrollHandler = () => {
        if (window.scrollY > 15) {
          setScrolling(true);
        } else {
          setScrolling(false);
        }
      };

      window.addEventListener("scroll", windowScrollHandler, { passive: true });

      return () => {
        window.removeEventListener("scroll", windowScrollHandler);
      };
    }
  }, []);

  console.log("isLoading====>", isLoading, "isAuthenticated====>", isAuthenticated);

  return (
    <header className={`${styles.home_header} ${scrolling ? "scrolling" : ""}`}>
      {/* add class "scrolling" when scroll the page */}
      <nav className="navbar">
        <div className="container">
          {/* Logo */}
          <Link href={ROUTES.HOME} className="navbar-brand">
            <Image src={Logo} alt="logo" width={640} height={320} className={styles.logo} />
          </Link>

          {/* Desktop Buttons */}
          {pathname === ROUTES.HOME && (hasLoadedOnce || !isLoading) ? (
            <div className={styles.desktopButtons}>
              <div className="logout-btns">
                {!isAuthenticated ? (
                  <>
                    <Button className="login-btn" onClick={() => navigate.push(ROUTES.SIGNUP)}>
                      Sign Up
                    </Button>
                    <Button className="login-btn outline-btn" onClick={() => navigate.push(ROUTES.LOGIN)}>
                      Log In
                    </Button>
                  </>
                ) : (
                  <Button className="login-btn outline-btn" onClick={() => navigate.push(ROUTES.DASHBOARD)}>
                    Back to Dashboard
                  </Button>
                )}
                <Button className="login-btn outline-btn" onClick={() => window.open(STRATUM9_MAIN_WEB_URL, "_blank")}>
                  Back to Stratum 9
                </Button>
              </div>
            </div>
          ) : null}

          {/* Hamburger Button (mobile only) */}
          {/* <button className={`${styles.hamburgerSM} ${isOpen ? styles.active : ""}`} onClick={() => setIsOpen(!isOpen)}>
            <span />
            <span />
            <span />
          </button> */}
        </div>

        {/* Overlay */}
        <div className={`${styles.overlay} ${isOpen ? styles.show : ""}`} onClick={() => setIsOpen(false)} />

        {/* Right Side Menu (mobile only) */}
        {pathname === ROUTES.HOME ? (
          <div className={`${styles.sideMenu} ${isOpen ? styles.open : ""}`}>
            <div className="logout-btns secondary-logout-btns">
              {!isLoading ? (
                !isAuthenticated ? (
                  <>
                    <Button className="login-btn" onClick={() => navigate.push(ROUTES.LOGIN)}>
                      Sign Up
                    </Button>
                    <Button className="login-btn outline-btn" onClick={() => navigate.push(ROUTES.LOGIN)}>
                      Log In
                    </Button>
                  </>
                ) : (
                  <Button className="login-btn outline-btn" onClick={() => navigate.push(ROUTES.DASHBOARD)}>
                    Back to Dashboard
                  </Button>
                )
              ) : null}
              <Button className="login-btn outline-btn" onClick={() => window.open(STRATUM9_MAIN_WEB_URL, "_blank")}>
                Back to Stratum 9
              </Button>
            </div>
          </div>
        ) : null}
      </nav>
    </header>
  );
};

export default HomeHeader;
