"use client";

import { useEffect, useRef } from "react";
import { useRouter } from "next/navigation";

export function useLeaveGuard(isGuardActive = true, message = "Your interview is not finished yet. Are you sure you want to leave?") {
  const router = useRouter();
  // Store the guard state in a ref inside the hook to track changes
  const guardActiveRef = useRef(isGuardActive);

  // Update the ref when the prop changes
  useEffect(() => {
    guardActiveRef.current = isGuardActive;
  }, [isGuardActive]);

  console.log("isGuardActive in hook =======>>>>>>>@@@@@@", isGuardActive);

  useEffect(() => {
    // 1) The default behavior: prevent leaving if message is set.
    const onBeforeUnload = (e: BeforeUnloadEvent) => {
      if (!guardActiveRef.current) return; // Check current ref value
      e.preventDefault();
      e.returnValue = ""; // Some browsers require returnValue to be set
    };
    window.addEventListener("beforeunload", onBeforeUnload);

    // 2) Handle clicks on in-app links and programmatic navigation (router.push)
    const onDocumentClick = (e: MouseEvent) => {
      if (!guardActiveRef.current) return; // Check current ref value

      const target = e.target as HTMLElement | null;
      if (!target) return;
      const anchor = target.closest("a") as HTMLAnchorElement | null;
      if (!anchor) return;

      const sameOrigin = anchor.origin === window.location.origin;
      const newTabIntent =
        anchor.target === "_blank" || e.metaKey || e.ctrlKey || e.shiftKey || e.altKey || (anchor.getAttribute("rel") || "").includes("external");
      if (!sameOrigin || newTabIntent) return;

      const href = anchor.getAttribute("href");
      if (!href) return;

      e.preventDefault();
      const ok = window.confirm(message);
      if (ok) router.push(href); // Proceed with navigation
    };
    document.addEventListener("click", onDocumentClick, true);

    // 3) Handle programmatic navigation (router.push, router.replace)
    const originalPush = router.push;
    const originalReplace = router.replace;

    router.push = (url: string) => {
      if (!guardActiveRef.current) {
        // If guard is disabled, navigate without confirmation
        originalPush(url);
        return;
      }
      const ok = window.confirm(message);
      if (ok) originalPush(url); // Proceed with navigation
    };

    router.replace = (url: string) => {
      if (!guardActiveRef.current) {
        // If guard is disabled, navigate without confirmation
        originalReplace(url);
        return;
      }
      const ok = window.confirm(message);
      if (ok) originalReplace(url);
    };

    // Cleanup on component unmount
    return () => {
      window.removeEventListener("beforeunload", onBeforeUnload);
      document.removeEventListener("click", onDocumentClick, true);
      router.push = originalPush;
      router.replace = originalReplace;
    };
  }, [message, router]); // Only re-run if message changes
}
