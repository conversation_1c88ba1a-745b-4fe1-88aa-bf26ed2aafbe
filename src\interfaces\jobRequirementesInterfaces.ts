// Import the type from our validation schema
import { GenerateJobSchema } from "@/validations/jobRequirementsValidations";

// Use the same type as our validation schema
export type JobFormData = GenerateJobSchema;

export interface GeneratedJobResponse {
  id: string;
  jobTitle: string;
  description: string;
  requirements: string;
  responsibilities: string;
  createdAt: string;
}

export interface JobSelectOption {
  label: string;
  value: string;
}

export interface ISkillData {
  id?: string;
  name: string;
  description: string;
}
export interface EditSkillsProps {
  initialSkillType?: string;
}

// Define a type for the slice state
export interface JobSkillsState {
  careerSkills: ISkillData[];
  roleSpecificSkills: ISkillData[];
  cultureSpecificSkills: ISkillData[];
}

export interface JobRequirementState {
  content: string;
  isGenerated: boolean;
  generatedAt: string | null;
}

// Define the skill item interface
export interface ISkillItem {
  id: number;
  title: string;
  description: string;
  short_description: string;
  value_points: string;
}

// Define a skill category interface
export interface ISkillCategory {
  type: string;
  items: ISkillItem[];
}

// Define the slice state type
export interface AllSkillsState {
  categories: ISkillCategory[];
  loading: boolean;
  error: string | null;
}

export interface JobApplication {
  application_id: number;
  job_id: number;
  hiring_manager_id: number;
  candidate_id: number;
  candidate_name: string;
  ai_decision: string;
  ai_reason: string;
  status?: string;
  created_ts: string;
  hidden?: boolean;
}

export interface IDepartment {
  value: number;
  label: string;
}
