/**
 * Translation utility functions for handling dynamic backend keys
 */
import { useTranslations } from "next-intl";

/**
 * Creates a function that safely translates dynamic keys from backend
 * This is a hook that must be used inside a component
 * @returns A function that can safely translate any string key
 */
export const useTranslate = () => {
  const t = useTranslations();

  /**
   * Safely translates dynamic keys from backend responses
   * @param key The dynamic key to translate
   * @returns Translated string
   */
  const translate = (key: string): string => {
    if (!key) return "";

    try {
      // Type that t function expects
      type MessageKey = Parameters<typeof t>[0];
      // Cast only at this point, keeping type checking everywhere else
      return t(key as unknown as MessageK<PERSON>);
    } catch (error) {
      // Fallback to the key itself if translation fails
      console.warn(`Translation failed for key: ${key}`, error);
      return key;
    }
  };

  return translate;
};

/**
 * Legacy function name for backward compatibility
 * @deprecated Use useTranslate instead
 */
export const useTranslateBackendKey = useTranslate;
