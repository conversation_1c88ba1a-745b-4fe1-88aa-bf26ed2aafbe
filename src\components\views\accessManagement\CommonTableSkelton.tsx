import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import React from "react";

const CommonTableSkelton = ({
  Rowcount,
  ColumnCount,
  ColumnWidth,
  center,
}: {
  Rowcount?: number;
  ColumnCount?: number;
  ColumnWidth?: string;
  center?: boolean;
}) => {
  return (
    <>
      {Array(Rowcount)
        .fill(null)
        .map((_, rowIndex) => (
          <tr key={rowIndex}>
            {Array(ColumnCount)
              .fill(null)
              .map((_, colIndex) => (
                <td key={colIndex} width={ColumnWidth} className={center ? "text-center" : ""}>
                  <Skeleton height={16} width="60%" borderRadius={4} />
                </td>
              ))}
          </tr>
        ))}
    </>
  );
};

export default CommonTableSkelton;
