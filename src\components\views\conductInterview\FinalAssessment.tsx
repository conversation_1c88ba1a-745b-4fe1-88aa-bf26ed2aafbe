"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";

import { AddQuestionModal } from "@/components/commonModals/AddQuestionModal";
import FinalAssessmentModal from "@/components/commonModals/FinalAssessmentModal";
import Button from "@/components/formElements/Button";
import ArrowDownIcon from "@/components/svgComponents/ArrowDownIcon";
import ShareIcon from "@/components/svgComponents/ShareIcon";
import { AssessmentData, Question } from "@/interfaces/finalAssessment";
import { getFinalAssessmentQuestions } from "@/services/assessmentService";
import { toastMessageError, toastMessageSuccess, getDecryptedData } from "@/utils/helper";

import style from "../../../styles/conductInterview.module.scss";
import { commonConstants } from "@/constants/commonConstants";
import { useTranslations } from "next-intl";
import Loader from "@/components/loader/Loader";
import ROUTES from "@/constants/routes";

export default function FinalAssessment() {
  console.log("🚀 FinalAssessment component initialized");

  const t = useTranslations();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [assessmentData, setAssessmentData] = useState<AssessmentData | null>(null);
  const [currentGroupIndex, setCurrentGroupIndex] = useState(0);
  const [expandedQuestions, setExpandedQuestions] = useState<{ [key: number]: boolean }>({});
  const [finalAssessmentId, setFinalAssessmentId] = useState<number | null>(null);
  const [jobId, setJobId] = useState<number | null>(null);
  const [jobApplicationId, setJobApplicationId] = useState<number | null>(null);
  const [isShared, setIsShared] = useState<boolean>(false);
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false);

  // State for AddQuestionModal
  const [showAddQuestionModal, setShowAddQuestionModal] = useState(false);
  const [selectedSkill, setSelectedSkill] = useState<{ id: number; title: string } | null>(null);
  const [showShareModal, setShowShareModal] = useState(false);
  // Ref for the top of the content section
  const contentTopRef = useRef<HTMLDivElement>(null);

  // Debug state changes
  console.log("📊 Current State:", {
    loading,
    currentGroupIndex,
    finalAssessmentId,
    jobId,
    jobApplicationId,
    isShared,
    isSubmitted,
    assessmentDataExists: !!assessmentData,
    assessmentDataGroups: assessmentData?.questionGroups?.length || 0,
    expandedQuestionsCount: Object.keys(expandedQuestions).length,
    showAddQuestionModal,
    showShareModal,
    selectedSkill,
  });

  // Get parameters from URL on component mount
  useEffect(() => {
    console.log("🔗 URL Parameters Effect - Starting to parse URL parameters");
    const urlParams = new URLSearchParams(window.location.search);

    // Check if all required parameters exist
    const encryptedFinalAssessmentId = urlParams.get(commonConstants.finalAssessmentId);
    const encryptedJobId = urlParams.get(commonConstants.jobId);
    const encryptedJobApplicationId = urlParams.get(commonConstants.jobApplicationId);

    if (!encryptedFinalAssessmentId || !encryptedJobId || !encryptedJobApplicationId) {
      console.error("Missing required URL parameters");
      toastMessageError(t("invalid_or_malformed_url_parameters"));
      router.back();
      return;
    }

    try {
      // Process finalAssessmentId
      const finalAssessmentData = getDecryptedData(encryptedFinalAssessmentId);
      setFinalAssessmentId(
        finalAssessmentData?.finalAssessmentId ? Number(finalAssessmentData.finalAssessmentId) : Number(encryptedFinalAssessmentId)
      );

      // Process jobId
      const jobIdData = getDecryptedData(encryptedJobId);
      setJobId(jobIdData?.jobId ? Number(jobIdData.jobId) : Number(encryptedJobId));

      // Process jobApplicationId
      const jobApplicationData = getDecryptedData(encryptedJobApplicationId);
      setJobApplicationId(jobApplicationData?.jobApplicationId ? Number(jobApplicationData.jobApplicationId) : Number(encryptedJobApplicationId));

      console.log("✅ All parameters processed and state updated");
    } catch (error) {
      console.error("Error processing URL parameters:", error);
      toastMessageError(t("something_went_wrong"));
      router.back();
    }
  }, [t, router]);

  // Function to fetch assessment questions - extracted as a reusable function
  const fetchAssessmentQuestions = useCallback(async () => {
    console.log("📡 fetchAssessmentQuestions - Starting API call");
    console.log("🔍 Parameters check:", {
      finalAssessmentId,
      jobId,
      jobApplicationId,
      allParametersValid: !!(finalAssessmentId && jobId && jobApplicationId),
    });

    if (!finalAssessmentId || !jobId || !jobApplicationId) {
      console.error("❌ Missing required parameters for API call:", {
        finalAssessmentId,
        jobId,
        jobApplicationId,
      });
      toastMessageError(t("invalid_or_missing_final_assessment_id"));
      return;
    }

    try {
      console.log("⏳ Setting loading to true and making API call...");
      setLoading(true);

      const response = await getFinalAssessmentQuestions(finalAssessmentId, jobId, jobApplicationId);

      console.log("📥 API Response received:", {
        responseExists: !!response,
        dataExists: !!response?.data,
        success: response?.data?.success,
        dataContent: response?.data?.data ? "Data present" : "No data",
        fullResponse: response,
      });

      if (response.data && response.data.success) {
        console.log("✅ Successful API response, processing data...");
        console.log("📊 Assessment Data Details:", {
          questionGroups: response.data.data.questionGroups?.length || 0,
          isAssessmentShared: response.data.data.isAssessmentShared,
          isAssessmentSubmitted: response.data.data.isAssessmentSubmitted,
          jobApplicationId: response.data.data.jobApplicationId,
          firstGroupQuestions: response.data.data.questionGroups?.[0]?.questions?.length || 0,
        });

        setAssessmentData(response.data.data);

        // Set jobApplicationId from response data
        if (response.data.data.jobApplicationId) {
          console.log("🔄 Updating jobApplicationId from response:", response.data.data.jobApplicationId);
          setJobApplicationId(response.data.data.jobApplicationId);
        }

        // Initialize all questions as expanded
        const questions = response.data.data.questionGroups?.[0]?.questions || [];
        console.log("🔧 Initializing expanded questions state:", {
          questionsCount: questions.length,
          questionIds: questions.map((q: Question) => q.id),
        });

        if (questions.length > 0) {
          const initialExpandState = Object.fromEntries(questions.map((question: Question) => [question.id, true]));
          console.log("📝 Setting expanded questions:", initialExpandState);
          setExpandedQuestions(initialExpandState);
        }

        // Update isShared state based on the latest data
        if (response.data.data.isAssessmentShared !== undefined && response.data.data.isAssessmentSubmitted !== undefined) {
          console.log("🔄 Updating shared/submitted states:", {
            isShared: response.data.data.isAssessmentShared,
            isSubmitted: response.data.data.isAssessmentSubmitted,
          });
          setIsShared(response.data.data.isAssessmentShared);
          setIsSubmitted(response.data.data.isAssessmentSubmitted);
        }
      } else {
        console.error("❌ API call failed or returned unsuccessful response:", {
          success: response?.data?.success,
          message: response?.data?.message,
          fullResponse: response,
        });
        toastMessageError(t(response.data?.message || "failed_to_fetch_assessment_questions"));
      }
    } catch (error) {
      console.error("💥 Exception in fetchAssessmentQuestions:", error);
      console.error("Error details:", {
        message: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : "No stack trace",
      });
      toastMessageError(t("failed_to_fetch_assessment_questions"));
    } finally {
      console.log("🏁 fetchAssessmentQuestions completed, setting loading to false");
      setLoading(false);
    }
  }, [finalAssessmentId, jobId, jobApplicationId, t]);

  const handleShareSuccess = useCallback(() => {
    console.log("🎉 handleShareSuccess - Assessment shared successfully");

    // Update isShared state
    console.log("🔄 Setting isShared to true");
    setIsShared(true);

    // Update URL parameter to reflect the change
    console.log("🔄 Refreshing assessment data after share");

    // Refresh the assessment data
    fetchAssessmentQuestions();

    // Close the share modal
    console.log("❌ Closing share modal");
    setShowShareModal(false);
  }, [fetchAssessmentQuestions, setIsShared]);

  useEffect(() => {
    console.log("🔄 Final Assessment ID Effect triggered:", {
      finalAssessmentId,
      shouldFetch: !!finalAssessmentId,
    });

    if (finalAssessmentId) {
      console.log("✅ Final Assessment ID is valid, calling fetchAssessmentQuestions");
      fetchAssessmentQuestions();
    } else {
      console.log("⏳ Final Assessment ID not yet available, waiting...");
    }
  }, [finalAssessmentId, fetchAssessmentQuestions]);
  useEffect(() => {
    const handleTabKey = (e: KeyboardEvent) => {
      if (showAddQuestionModal && e.key === "Tab") {
        e.preventDefault();
      }
    };
    window.addEventListener("keydown", handleTabKey);
    return () => {
      window.removeEventListener("keydown", handleTabKey);
    };
  }, [showAddQuestionModal]);

  // Toggle question expansion
  const handleToggleQuestion = (questionId: number) => {
    console.log("🔄 Toggling question expansion:", {
      questionId,
      currentState: expandedQuestions[questionId],
      newState: !expandedQuestions[questionId],
    });

    setExpandedQuestions((prev) => ({
      ...prev,
      [questionId]: !prev[questionId],
    }));
  };

  // Handle navigation to next question group
  // Function to scroll to the top of content section
  const scrollToTop = () => {
    setTimeout(() => {
      if (contentTopRef.current) {
        contentTopRef.current.scrollIntoView({ behavior: "smooth" });
      }
    }, 100);
  };

  const handleNextGroup = () => {
    console.log("➡️ handleNextGroup - Navigating to next group");
    console.log("📊 Navigation check:", {
      hasAssessmentData: !!assessmentData,
      currentGroupIndex,
      totalGroups: assessmentData?.questionGroups.length || 0,
      canNavigateNext: assessmentData && currentGroupIndex < assessmentData.questionGroups.length - 1,
    });

    if (assessmentData && currentGroupIndex < assessmentData.questionGroups.length - 1) {
      const nextIndex = currentGroupIndex + 1;
      console.log("✅ Moving to next group:", {
        fromIndex: currentGroupIndex,
        toIndex: nextIndex,
      });

      setCurrentGroupIndex(nextIndex);

      // Initialize expanded state for questions in the next group
      const nextGroupQuestions = assessmentData.questionGroups[nextIndex].questions;
      console.log("🔧 Initializing expanded state for next group:", {
        groupIndex: nextIndex,
        questionsCount: nextGroupQuestions.length,
        questionIds: nextGroupQuestions.map((q) => q.id),
      });

      const initialExpandState = nextGroupQuestions.reduce(
        (acc, question) => {
          acc[question.id] = true;
          return acc;
        },
        {} as { [key: number]: boolean }
      );

      console.log("📝 Setting expanded questions for next group:", initialExpandState);
      setExpandedQuestions(initialExpandState);
      // Scroll to top of content section
      scrollToTop();
    } else {
      console.log("❌ Cannot navigate to next group - at last group or no data");
    }
  };

  // Handle navigation to previous question group
  const handlePreviousGroup = () => {
    console.log("⬅️ handlePreviousGroup - Navigating to previous group");
    console.log("📊 Navigation check:", {
      hasAssessmentData: !!assessmentData,
      currentGroupIndex,
      canNavigatePrevious: assessmentData && currentGroupIndex > 0,
    });

    if (assessmentData && currentGroupIndex > 0) {
      const prevIndex = currentGroupIndex - 1;
      console.log("✅ Moving to previous group:", {
        fromIndex: currentGroupIndex,
        toIndex: prevIndex,
      });

      setCurrentGroupIndex(prevIndex);

      // Initialize expanded state for questions in the previous group
      const prevGroupQuestions = assessmentData.questionGroups[prevIndex].questions;
      console.log("🔧 Initializing expanded state for previous group:", {
        groupIndex: prevIndex,
        questionsCount: prevGroupQuestions.length,
        questionIds: prevGroupQuestions.map((q) => q.id),
      });

      const initialExpandState = prevGroupQuestions.reduce(
        (acc, question) => {
          acc[question.id] = true;
          return acc;
        },
        {} as { [key: number]: boolean }
      );

      console.log("📝 Setting expanded questions for previous group:", initialExpandState);
      setExpandedQuestions(initialExpandState);
      // Scroll to top of content section
      scrollToTop();
    } else {
      console.log("❌ Cannot navigate to previous group - at first group or no data");
    }
  };

  // Get current question group
  const currentGroup = assessmentData?.questionGroups[currentGroupIndex];
  console.log("📋 Current Group Details:", {
    currentGroupIndex,
    hasCurrentGroup: !!currentGroup,
    currentGroupType: currentGroup?.type,
    currentGroupQuestionsCount: currentGroup?.questions?.length || 0,
  });

  // Format group type for display
  const formatGroupType = (type: string) => {
    const formatted = type
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");

    console.log("🎨 Formatting group type:", { original: type, formatted });
    return formatted;
  };

  // Determine if this is the last group
  const isLastGroup = assessmentData ? currentGroupIndex === assessmentData.questionGroups.length - 1 : false;
  console.log("🏁 Last Group Check:", {
    isLastGroup,
    currentGroupIndex,
    totalGroups: assessmentData?.questionGroups.length || 0,
  });

  // Get total number of questions across all groups
  const getTotalQuestions = () => {
    if (!assessmentData || !assessmentData.questionGroups) return 0;

    let totalQuestions = 0;
    assessmentData.questionGroups.forEach((group) => {
      if (group.questions) {
        totalQuestions += group.questions.length;
      }
    });

    console.log("📊 Total questions count:", totalQuestions);
    return totalQuestions;
  };

  // Get total number of questions that have been answered by the applicant
  const getTotalAnsweredQuestions = () => {
    if (!assessmentData || !assessmentData.questionGroups) return 0;

    let answeredQuestions = 0;
    assessmentData.questionGroups.forEach((group) => {
      if (group.questions) {
        group.questions.forEach((question) => {
          if (question.applicantAnswer !== null && question.applicantAnswer !== undefined) {
            answeredQuestions++;
          }
        });
      }
    });

    console.log("📊 Total answered questions count:", answeredQuestions);
    return answeredQuestions;
  };

  // Group questions by skillId
  const getQuestionsGroupedBySkill = () => {
    console.log("🔧 Grouping questions by skill for current group");

    if (!currentGroup) {
      console.log("❌ No current group available for skill grouping");
      return [];
    }

    // Group questions by skillId
    const groupedQuestions: { [key: number]: { skillTitle: string; questions: Question[] } } = {};

    currentGroup.questions.forEach((question) => {
      if (!groupedQuestions[question.skillId]) {
        groupedQuestions[question.skillId] = {
          skillTitle: question.skillTitle,
          questions: [],
        };
      }

      groupedQuestions[question.skillId].questions.push(question);
    });

    // Convert to array for rendering
    const result = Object.values(groupedQuestions);
    console.log("📊 Skill Groups Created:", {
      totalSkillGroups: result.length,
      skillGroups: result.map((group) => ({
        skillTitle: group.skillTitle,
        questionsCount: group.questions.length,
      })),
    });

    return result;
  };

  const skillGroups = getQuestionsGroupedBySkill();

  // Render loading state
  if (loading) {
    console.log("⏳ Rendering loading state");
    return (
      <div className={style.conduct_interview_page}>
        <div className="container">
          <div className="text-center py-5">
            <Loader />
            <h1 className="mt-3">{t("loading_assessment_questions")}</h1>
          </div>
        </div>
      </div>
    );
  }

  // Render error state if no assessment data
  if (!loading && !assessmentData) {
    console.log("❌ Rendering error state - no assessment data found");
    return (
      <div className={style.conduct_interview_page}>
        <div className="container">
          <div className="text-center py-5">{t("no_assessment_data_found")}</div>
        </div>
      </div>
    );
  }

  // Handle modal actions
  const handleCloseAddQuestionModal = () => {
    console.log("❌ Closing Add Question Modal");
    setShowAddQuestionModal(false);
    setSelectedSkill(null);
  };

  const handleAddQuestionSuccess = () => {
    console.log("🎉 Question added successfully, refreshing data");
    toastMessageSuccess(t("question_added_successfully"));
    setShowAddQuestionModal(false);
    setSelectedSkill(null);

    // Re-fetch assessment questions
    fetchAssessmentQuestions();
  };

  console.log("🎨 Rendering main component with data:", {
    hasAssessmentData: !!assessmentData,
    currentGroupIndex,
    skillGroupsCount: skillGroups.length,
    isShared,
    isSubmitted,
    isLastGroup,
    showAddQuestionModal,
    showShareModal,
  });

  console.log("🎨 skillGroups====", skillGroups);

  return (
    <div className={style.conduct_interview_page}>
      <div ref={contentTopRef} className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                Final <span>Assessment</span>
              </h2>
            </div>
          </div>
        </div>
        <div className="inner-section">
          <div className="section-heading d-flex justify-content-between mb-4">
            <h2 className="m-0">
              {currentGroup && (
                <>
                  {t("group")} {currentGroupIndex + 1} of {assessmentData?.questionGroups.length}: <span>{formatGroupType(currentGroup.type)}</span>
                </>
              )}
            </h2>
            {isSubmitted ? (
              <div
                style={{
                  backgroundColor: "#f0f0f0",
                  padding: "8px 15px",
                  borderRadius: "20px",
                  fontSize: "14px",
                  fontWeight: "bold",
                  color: "#436eb6",
                }}
              >
                {getTotalAnsweredQuestions()}/{getTotalQuestions()} {t("questions_answered")}
              </div>
            ) : null}
            {!isShared && !isSubmitted ? (
              <Button
                className="clear-btn text-btn primary p-0 m-0"
                onClick={() => {
                  console.log("📤 Share button clicked - opening share modal");
                  setShowShareModal(true);
                }}
              >
                <ShareIcon className="me-2" />
                {t("share_assessment_link_to_candidate")}
              </Button>
            ) : (
              <div className="d-flex align-items-center">
                <div className="color-legend d-flex align-items-center">
                  <div className="d-flex align-items-center me-3">
                    <div style={{ width: "15px", height: "15px", backgroundColor: "#cb993299", borderRadius: "3px", marginRight: "5px" }}></div>
                    <span style={{ fontSize: "12px" }}>{t("candidate_answer")}</span>
                  </div>
                  <div className="d-flex align-items-center">
                    <div style={{ width: "15px", height: "15px", backgroundColor: "#00773399", borderRadius: "3px", marginRight: "5px" }}></div>
                    <span style={{ fontSize: "12px" }}>{t("correct_answer")}</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="row">
            <div className="col-md-12">
              {skillGroups.map((skillGroup, skillGroupIndex) => (
                <div key={`skill-${skillGroupIndex}`} className="skill-group mb-5">
                  <div className="skill-group-question">
                    <h3
                      className="skill-title mb-3"
                      style={{
                        color: "#131313",
                        paddingBottom: "10px",
                        fontSize: "20px",
                        fontWeight: "600",
                      }}
                    >
                      {t("skill")}: <span style={{ color: "#cb9932" }}>{skillGroup.skillTitle}</span>
                    </h3>

                    {skillGroup.questions.map((question, index) => (
                      <div key={question.id} className="interview-question-card with-border" onClick={() => handleToggleQuestion(question.id)}>
                        <p className="tittle">
                          {t("question")} {index + 1} <ArrowDownIcon className={!expandedQuestions[question.id] ? "rotate" : ""} />
                        </p>
                        <h5>{question.question}</h5>
                        {expandedQuestions[question.id] && (
                          <div className="question-body" onClick={(e) => e.stopPropagation()}>
                            {question.options.options.map((option) => {
                              console.log("🎯 Processing option for question:", {
                                questionId: question.id,
                                optionId: option.id,
                                optionText: option.text,
                                correctAnswer: question.correctAnswer,
                                applicantAnswer: question.applicantAnswer,
                              });

                              const isCorrectAnswer = option.id === question.correctAnswer;
                              const isCandidateAnswer = question?.applicantAnswer ? question?.applicantAnswer === option.id : false;

                              const isBothCorrectAndCandidate = isCandidateAnswer && isCorrectAnswer;

                              console.log(index, "✅ Option analysis:", {
                                optionId: option.id,
                                isCorrectAnswer,
                                isCandidateAnswer,
                                isBothCorrectAndCandidate,
                                willBeChecked: isCandidateAnswer,
                              });
                              return (
                                <div
                                  key={option.id}
                                  className={`answer-strap ${
                                    isCandidateAnswer && isCorrectAnswer
                                      ? "selected-correct"
                                      : isCandidateAnswer
                                        ? "candidate-answer"
                                        : isCorrectAnswer
                                          ? "correct-answer"
                                          : ""
                                  }`}
                                >
                                  <div className="radio-wrapper">
                                    <input
                                      className="radio-input form-check-input"
                                      type="radio"
                                      name={`question_${question.id}`}
                                      id={`question_${question.id}_option_${option.id}`}
                                      value={option.id}
                                      checked={isCandidateAnswer}
                                      readOnly
                                    />
                                    <label className="radio-label" htmlFor={`question_${question.id}_option_${option.id}`}>
                                      {option.text}
                                    </label>
                                  </div>
                                </div>
                              );
                            })}
                            {/* <div className="answer-strap p-0 border-0">
                            <p className="note-text">Note: The candidate's answer is not visible to you.</p>
                          </div> */}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Add New Question button for each skill group - only show if not shared */}
                  {!isShared && !isSubmitted && (
                    <Button
                      className="clear-btn text-btn secondary p-0 mb-3 mt-2"
                      style={{ color: "#f5b759", fontWeight: 500 }}
                      onClick={() => {
                        setSelectedSkill({
                          id: skillGroup.questions[0].skillId, // Get skillId from the first question in the group
                          title: skillGroup.skillTitle,
                        });
                        setShowAddQuestionModal(true);
                      }}
                    >
                      {t("add_new_question")}
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="button-align" style={{ display: "flex", justifyContent: "space-between" }}>
          {/* Show Previous button if not on the first group */}
          <div>
            {currentGroupIndex > 0 && (
              <Button className="secondary-btn rounded-md" onClick={handlePreviousGroup}>
                {t("previous_skill_assessment")}
              </Button>
            )}
          </div>

          {/* Show Next button if not on the last group, positioned on the right */}
          <div>
            {!isLastGroup ? (
              <Button className="primary-btn rounded-md" onClick={handleNextGroup}>
                {t("next_skill_assessment")}
              </Button>
            ) : null}
          </div>
          {/* <div>
            {!isLastGroup ? (
              <Button className="primary-btn rounded-md" onClick={handleNextGroup}>
                {t("next_skill_assessment")}
              </Button>
            ) : !isSubmitted ? (
              <Button
                className="primary-btn rounded-md"
                onClick={() => {
                  router.push(`${ROUTES.JOBS.CANDIDATE_PROFILE}/${jobApplicationId}`); // Navigate to candidate profile using App Router
                  // We'll navigate after sharing or if user cancels
                }}
              >
                {t("complete_assessment")}
              </Button>
            ) : null}
          </div> */}
        </div>
      </div>

      {/* Add Question Modal */}
      {showAddQuestionModal && selectedSkill && finalAssessmentId && (
        <AddQuestionModal
          onClickCancel={handleCloseAddQuestionModal}
          onSubmitSuccess={handleAddQuestionSuccess}
          skillId={selectedSkill.id}
          skillTitle={selectedSkill.title}
          finalAssessmentId={Number(finalAssessmentId)}
        />
      )}

      {/* Share Assessment Modal */}
      {showShareModal && finalAssessmentId && (
        <FinalAssessmentModal
          onClickCancel={() => setShowShareModal(false)}
          onSubmitSuccess={handleShareSuccess}
          finalAssessmentId={Number(finalAssessmentId)}
          jobApplicationId={Number(jobApplicationId)}
          isFromCompleteButton={isLastGroup}
        />
      )}
    </div>
  );
}
