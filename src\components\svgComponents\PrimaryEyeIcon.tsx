import React from "react";

function PrimaryEyeIcon({ className }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} width="24" height="24" viewBox="0 0 32 32" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.9998 12.8555C14.2265 12.8555 12.7852 14.2981 12.7852 16.0715C12.7852 17.8435 14.2265 19.2848 15.9998 19.2848C17.7732 19.2848 19.2158 17.8435 19.2158 16.0715C19.2158 14.2981 17.7732 12.8555 15.9998 12.8555M15.9998 21.2848C13.1238 21.2848 10.7852 18.9461 10.7852 16.0715C10.7852 13.1955 13.1238 10.8555 15.9998 10.8555C18.8758 10.8555 21.2158 13.1955 21.2158 16.0715C21.2158 18.9461 18.8758 21.2848 15.9998 21.2848"
      />
      <mask id="mask0_11415_1842" style={{ maskType: "luminance" }} maskUnits="userSpaceOnUse" x="2" y="5" width="28" height="22">
        <path fillRule="evenodd" clipRule="evenodd" d="M2.66602 5.33398H29.3325V26.8073H2.66602V5.33398Z" fill="white" />
      </mask>
      <g mask="url(#mask0_11415_1842)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.76172 16.068C7.24172 21.5467 11.4191 24.804 16.0017 24.8054C20.5844 24.804 24.7617 21.5467 27.2417 16.068C24.7617 10.5907 20.5844 7.33336 16.0017 7.33203C11.4204 7.33336 7.24172 10.5907 4.76172 16.068V16.068ZM16.0036 26.8064H15.9983H15.997C10.4823 26.8024 5.5303 22.937 2.74897 16.4637C2.64097 16.2117 2.64097 15.9264 2.74897 15.6744C5.5303 9.20236 10.4836 5.33703 15.997 5.33303C15.9996 5.3317 15.9996 5.3317 16.001 5.33303C16.0036 5.3317 16.0036 5.3317 16.005 5.33303C21.5196 5.33703 26.4716 9.20236 29.253 15.6744C29.3623 15.9264 29.3623 16.2117 29.253 16.4637C26.473 22.937 21.5196 26.8024 16.005 26.8064H16.0036Z"
        />
      </g>
    </svg>
  );
}

export default PrimaryEyeIcon;
