/**
 * Interface definitions for subscription-related components
 */

/**
 * Interface for a single plan feature
 */
export interface IPlanFeature {
  slug: string; // Identifier used in code (e.g., "job_postings")
  feature: string; // Display name (e.g., "Job Postings")
  text: string; // Value description (e.g., "Unlimited")
  value: number | string | boolean; // Actual value
  is_active: boolean; // Whether feature is available
  description: string; // Detailed description for tooltips/info
}

export interface PlanData {
  subscriptionPlanId: number;
  subscriptionPlanName: string;
  subscriptionPlanDescription: string;
  subscriptionPlanBenefits: IPlanFeature[];
  subscriptionPlanPaymentType: string;
  pricingId: number | null;
  price: string;
}

export interface PaymentIntentData {
  id: string;
  client_secret: string;
  payment_method_id?: string;
  subscription_id?: string;
}

export interface PaymentIntentResponse {
  status: string;
  message: string;
  data: PaymentIntentData;
}

// Payment form related interfaces
export interface PaymentFormProps {
  planId: string;
  pricingId: string;
  planName: string;
  subscriptionType: string;
  amount: number;
  onPaymentSuccess: () => void;
  onPaymentError: (error: string) => void;
  onCancel: () => void;
  paymentIntentId?: string; // Optional payment intent ID passed from parent
  clientSecret?: string; // Optional client secret passed from parent
}

export interface PaymentIntent {
  id: string;
  client_secret: string;
  payment_method_id?: string;
  subscription_id?: string;
}

/**
 * Interface for the checkout session request with all required properties
 */
export interface CreateCheckoutSessionRequest {
  plan_id: string;
  pricing_id: string;
}

export interface ValidateSubscriptionDetailsRequest {
  plan_id: string;
  pricing_id: string;
}

export interface CancelSubscriptionRequest {
  reason?: string;
}

/**
 * Individual transaction item interface
 */
export interface TransactionItem {
  id: number;
  payment_status: string;
  amount: string;
  transaction_type: string;
  transaction_method: string;
  transaction_date: string;
  invoice_id: string;
  invoice_url: string | null;
  plan_name: string;
}

/**
 * Pagination information interface
 */
export interface PaginationInfo {
  limit: number;
  offset: number;
  total: number;
  hasMore: boolean;
}

/**
 * Transaction response interface with pagination and transactions array
 */
export interface TransactionResponse {
  transactions: TransactionItem[];
  pagination: PaginationInfo;
}

export interface TransactionsResponse {
  success: boolean;
  message: string;
  data: TransactionResponse;
  code?: number;
}

/**
 * Interface for buy subscription response data
 */
export interface BuySubscriptionData {
  sessionId: string;
  checkoutUrl: string;
  planName: string;
  price: string;
  stripeCustomerId: string;
}

/**
 * Interface for buy subscription response
 */
export interface BuySubscriptionResponse {
  success: boolean;
  message: string;
  data: BuySubscriptionData;
  code: number;
}
