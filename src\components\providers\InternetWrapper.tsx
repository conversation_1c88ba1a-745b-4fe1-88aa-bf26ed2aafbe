"use client";

import { ReactNode, useEffect } from "react";
import { useInternetConnection } from "@/hooks/useInternetConnection";
import { usePathname, useRouter } from "next/navigation";
import NoInternetPage from "@/app/[locale]/no-internet/page";
import ROUTES from "@/constants/routes";

export default function InternetWrapper({ children }: { children: ReactNode }) {
  const { isOnline } = useInternetConnection();
  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    // If user is online but trying to access no-internet page, redirect them away
    if (isOnline && pathname.includes(ROUTES.NO_INTERNET)) {
      console.log("User has internet but is on no-internet page, redirecting to dashboard");
      try {
        router.replace(ROUTES.DASHBOARD);
      } catch (error) {
        // Fallback to home page
        router.replace(ROUTES.HOME);
      }
    }
  }, [isOnline, pathname, router]);

  // Show no-internet page only when actually offline
  if (!isOnline) {
    return <NoInternetPage />;
  }

  return <>{children}</>;
}
