import endpoint from "@/constants/endpoint";
import * as http from "@/utils/http";
import { IApiResponseCommonInterface } from "@/interfaces/commonInterfaces";

export interface Logs {
  auditId: number;
  comments: string;
  entityId: number;
  entityType: string;
  logType: string;
  newValue: string;
  oldValue: string;
  orgId: number;
  timestamp: string;
  actionByName: string;
}

/**
 * Fetch paginated activity logs from the API
 * @param data Object with pagination and filter params
 * @returns Promise resolving to ActivityLogsApiResponse
 */
export const fetchActivityLogs = (data: { offset?: number; limit?: number; logType?: string }): Promise<IApiResponseCommonInterface<Logs[]>> => {
  // Use 'params' to send query parameters in axios GET
  return http.get(endpoint.activityLogs.GET_ACTIVITY_LOGS, { ...data });
};
