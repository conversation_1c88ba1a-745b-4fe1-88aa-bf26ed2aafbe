import endpoint from "@/constants/endpoint";
import * as http from "@/utils/http";

import { IApiResponseCommonInterface } from "@/interfaces/commonInterfaces";
import {
  AdditionalInfoPayload,
  CandidateApplication,
  CandidateProfileResponse,
  FetchCandidatesParams,
  ICandidateInterviewHistory,
  IFinalAssessment,
  IHiredCandidatesResponse,
  ISkillSpecificAssessment,
  PromoteDemotePayload,
  topCandidateApplication,
} from "@/interfaces/candidatesInterface";

// ============================================================================
// CANDIDATE APPLICATION MANAGEMENT
// ============================================================================

/**
 * Fetches candidates with their job applications using advanced filtering and pagination
 *
 * This function retrieves a paginated list of candidates along with their application details.
 * It supports comprehensive filtering options including job-specific filtering, name search,
 * and active/archived status filtering.
 *
 * @async
 * @function fetchCandidatesApplications
 * @param {FetchCandidatesParams} data - Query parameters for filtering and pagination
 * @param {number} [data.page] - Page offset for pagination (0-based)
 * @param {number} [data.limit] - Maximum number of candidates to return per page
 * @param {string} [data.searchStr] - Search string to filter candidates by name
 * @param {boolean} [data.isActive] - Filter by status: true for active, false for archived
 * @param {number} [data.jobId] - Optional job ID to filter candidates for specific position
 * @returns {Promise<IApiResponseCommonInterface<CandidateApplication[]>>} Promise resolving to candidate applications list
 *
 */
export const fetchCandidatesApplications = (data: FetchCandidatesParams): Promise<IApiResponseCommonInterface<CandidateApplication[]>> => {
  return http.get(endpoint.candidatesApplication.GET_CANDIDATES_WITH_APPLICATIONS, { ...data });
};

/**
 * Fetches top-ranked candidates with their applications for a specific job
 *
 * This function retrieves candidates who have been identified as top performers
 * based on AI scoring, ATS evaluation, and other ranking criteria. The results
 * are pre-filtered and sorted by the backend ranking algorithm.
 *
 * @async
 * @function fetchTopCandidatesApplications
 * @param {number} [jobId] - Optional job ID to filter top candidates for specific position
 * @returns {Promise<IApiResponseCommonInterface<topCandidateApplication[]>>} Promise resolving to top candidates list
 */
export const fetchTopCandidatesApplications = (jobId?: number): Promise<IApiResponseCommonInterface<topCandidateApplication[]>> => {
  return http.get(endpoint.candidatesApplication.GET_TOP_CANDIDATES_WITH_APPLICATIONS, {
    jobId,
  });
};

/**
 * Promotes or demotes a candidate in the application ranking
 *
 * This function allows hiring managers to manually adjust candidate rankings
 * by promoting high-potential candidates or demoting those who don't meet
 * expectations. The action affects the candidate's visibility and priority.
 *
 * @async
 * @function promoteDemoteCandidate
 * @param {PromoteDemotePayload} payload - The promotion/demotion request data
 * @param {number} payload.candidateId - ID of the candidate to promote/demote
 * @param {number} payload.applicationId - ID of the specific application
 * @param {"Promoted" | "Demoted"} payload.action - Action to perform
 * @returns {Promise<IApiResponseCommonInterface<null>>} Promise resolving to success confirmation
 */
export const promoteDemoteCandidate = async (payload: PromoteDemotePayload): Promise<IApiResponseCommonInterface<null>> => {
  return await http.put(endpoint.candidatesApplication.PROMOTE_DEMOTE_CANDIDATE, payload);
};

/**
 * Adds additional information to a candidate's application
 *
 * This function allows candidates or hiring managers to submit supplementary
 * information, documents, or clarifications to an existing application.
 * Commonly used for portfolio submissions, additional certifications, or
 * responses to specific questions.
 *
 * @async
 * @function addApplicantAdditionalInfo
 * @param {AdditionalInfoPayload} payload - The additional information data
 * @param {string} payload.applicationId - ID of the application to update
 * @param {string} payload.description - Description or text content
 * @param {string} payload.images - Image URLs or file references
 * @returns {Promise<IApiResponseCommonInterface<null>>} Promise resolving to success confirmation
 */
export const addApplicantAdditionalInfo = async (payload: AdditionalInfoPayload): Promise<IApiResponseCommonInterface<null>> => {
  return await http.post(endpoint.candidatesApplication.ADDITIONAL_INFO, payload);
};

// ============================================================================
// CANDIDATE PROFILE MANAGEMENT
// ============================================================================

/**
 * Fetches comprehensive candidate profile details by candidate ID
 *
 * This function retrieves detailed information about a specific candidate including
 * personal details, job application status, assigned interviewer information,
 * resume links, and current round information. Essential for candidate profile views.
 *
 * @async
 * @function fetchCandidateProfile
 * @param {number | string} candidateId - The unique identifier of the candidate
 * @returns {Promise<IApiResponseCommonInterface<CandidateProfileResponse>>} Promise resolving to candidate profile data
 */
export const fetchCandidateProfile = (jobApplicationId: number | string): Promise<IApiResponseCommonInterface<CandidateProfileResponse>> => {
  return http.get(endpoint.candidatesApplication.GET_CANDIDATE_DETAILS, { jobApplicationId });
};

/**
 * Updates the job application status for hire/reject decisions
 *
 * This function allows hiring managers to make final decisions on candidate applications
 * by updating the status to either "Hired" or "Final-Reject". This action typically
 * triggers workflow notifications and updates the candidate's status across the system.
 *
 * @async
 * @function updateJobApplicationStatus
 * @param {number} jobApplicationId - The unique identifier of the job application
 * @param {string} status - The new status ("Hired" or "Final-Reject")
 * @returns {Promise<IApiResponseCommonInterface<null>>} Promise resolving to success confirmation
 * ```
 */
export const updateJobApplicationStatus = async (jobApplicationId: number, status: string): Promise<IApiResponseCommonInterface<null>> => {
  return await http.put(endpoint.candidatesApplication.UPDATE_JOB_APPLICATION_STATUS.replace(":jobApplicationId", jobApplicationId.toString()), {
    status,
  });
};

/**
 * Retrieves comprehensive interview history for a specific candidate
 *
 * This function fetches detailed interview records across all rounds including
 * interviewer information, skill scores, hard skill marks, interview summaries,
 * and AI-powered performance analysis. Essential for tracking candidate progress.
 *
 * @async
 * @function getCandidateInterviewHistory
 * @param {string} applicationId - The unique identifier of the application
 * @returns {Promise<IApiResponseCommonInterface<ICandidateInterviewHistory[]>>} Promise resolving to interview history array
 */
export const getCandidateInterviewHistory = async (applicationId: string): Promise<IApiResponseCommonInterface<ICandidateInterviewHistory[]>> => {
  return await http.get(endpoint.candidatesApplication.GET_CANDIDATE_INTERVIEW_HISTORY.replace(":applicationId", applicationId));
};

/**
 * Retrieves comprehensive final assessment summary for a specific candidate application
 *
 * This function fetches the complete final assessment analysis generated after all
 * interview rounds are completed. It includes AI-powered insights, success probability
 * calculations, skill summaries, and personalized development recommendations.
 *
 * @async
 * @function getApplicationFinalSummary
 * @param {string} candidateId - The unique identifier of the candidate
 * @returns {Promise<IApiResponseCommonInterface<IFinalAssessment>>} Promise resolving to final assessment data
 *
 * Assessment Data Includes:
 * - Overall success probability percentage (0-100)
 * - Comprehensive skill summary with AI analysis
 * - Personalized development recommendations by category
 * - Job application reference details
 * ```
 */
export const getApplicationFinalSummary = async (
  jobApplicationId: string
): Promise<
  IApiResponseCommonInterface<{
    formattedFinalSummary: IFinalAssessment;
    candidateProfileSkillScoreData: ISkillSpecificAssessment;
  }>
> => {
  return await http.get(endpoint.candidatesApplication.GET_APPLICATION_FINAL_SUMMARY.replace(":jobApplicationId", jobApplicationId));
};

/**
 * Retrieves detailed skill-specific assessment data for a candidate
 *
 * This function fetches comprehensive skill evaluation data aggregated from all
 * completed interview rounds. The data is flattened and optimized for frontend
 * consumption, providing detailed insights into each skill area evaluated.
 *
 * @async
 * @function getApplicationSkillScoreData
 * @param {string} candidateId - The unique identifier of the candidate
 * @returns {Promise<IApiResponseCommonInterface<ISkillSpecificAssessment>>} Promise resolving to skill assessment data
 *
 * Skill Data Includes:
 * - Individual skill scores and marks (0-10 scale)
 * - Skill-specific strengths and achievements
 * - Identified potential gaps and improvement areas
 * - Success probability for each skill area
 * - Overall career-based skills score
 * - Interviewer-specific evaluations and feedback
 */
// export const getApplicationSkillScoreData = async (jobApplicationId: string): Promise<IApiResponseCommonInterface<ISkillSpecificAssessment>> => {
//   return await http.get(endpoint.candidatesApplication.GET_APPLICATION_SKILL_SCORE_DATA.replace(":jobApplicationId", jobApplicationId));
// };

/**
 * Generates final summary for a candidate application
 *
 * This function triggers the generation of a comprehensive final summary for a candidate
 * based on their interview performance, skill assessments, and overall evaluation data.
 * The summary includes AI-powered insights and recommendations for hiring decisions.
 *
 * @async
 * @function generateFinalSummary
 * @param {number | string} candidateId - The unique identifier of the candidate
 * @param {number | string} jobApplicationId - The unique identifier of the job application
 * @returns {Promise<IApiResponseCommonInterface<null>>} Promise resolving to success confirmation
 *
 * Summary Generation Includes:
 * - Comprehensive analysis of interview performance across all rounds
 * - Skill-specific evaluation and scoring aggregation
 * - AI-powered insights and recommendations
 * - Overall success probability calculation
 * - Personalized development recommendations
 */
export const generateFinalSummary = async (jobApplicationId: number | string): Promise<IApiResponseCommonInterface<null>> => {
  return await http.get(endpoint.candidatesApplication.GENERATE_FINAL_SUMMARY, {
    jobApplicationId: jobApplicationId.toString(),
  });
};

/**
 * Fetches all hired candidates with their interviewer details
 *
 * This function retrieves a list of all candidates who have been successfully hired,
 * along with comprehensive information about the interviewers who conducted their interviews.
 * The data includes candidate names, job titles, and detailed interviewer information.
 *
 * @async
 * @function fetchAllHiredCandidates
 * @returns {Promise<IHiredCandidatesResponse>} Promise resolving to hired candidates data
 *
 * Response Data Includes:
 * - Candidate name and job title
 * - List of interviewers with names and profile images
 * - Success status and response message
 * - Structured data for easy frontend consumption
 */
export const fetchAllHiredCandidates = async (): Promise<IHiredCandidatesResponse> => {
  return await http.get(endpoint.candidatesApplication.GET_ALL_HIRED_CANDIDATES);
};
