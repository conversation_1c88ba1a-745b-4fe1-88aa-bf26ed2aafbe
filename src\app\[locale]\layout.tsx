import type { Metadata } from "next";
import { notFound } from "next/navigation";

import "../../../node_modules/bootstrap/dist/css/bootstrap.css";
import "../../styles/style.scss";
import { Locale, NextIntlClientProvider, hasLocale } from "next-intl";
import { getMessages } from "next-intl/server";
import { ReactNode } from "react";
import { routing } from "@/i18n/routing";
import ReduxProvider from "@/redux/ReduxProvider";
import { Toaster } from "react-hot-toast";
import RouteAwareWrapper from "@/components/layout/RouteAwareWrapper";
import InternetConnectionProvider from "@/components/providers/InternetConnectionProvider";
import InternetWrapper from "@/components/providers/InternetWrapper";

type Props = {
  children: ReactNode;
  params: Promise<{ locale: Locale }>;
};

export const metadata: Metadata = {
  title: "S9 InnerView ",
  description:
    "Stratum9 Hiring Web lets teams post jobs, screen candidates, run structured interviews and hire faster with data-driven insights—all in one intuitive platform.",
};

export default async function LocaleLayout({ children, params }: Props) {
  const { locale } = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  const messages = await getMessages();

  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta charSet="UTF-8" />
        <meta
          name="description"
          content="Stratum9 Hiring Web lets teams post jobs, screen candidates, run structured interviews and hire faster with data-driven insights—all in one intuitive platform."
        />
        <meta name="keywords" content="Stratum9 Hiring Web, Hiring Platform, Job Board, Recruitment Software" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" />
        <link rel="icon" href="/favicon.ico?v=1" type="image/x-icon" sizes="any" />
        <link rel="shortcut icon" href="/favicon.ico?v=1" type="image/x-icon" />
        <link rel="apple-touch-icon" href="/favicon.ico?v=1" />
        <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap" rel="stylesheet" />
      </head>
      <body>
        <ReduxProvider>
          <NextIntlClientProvider locale={locale} messages={messages}>
            <InternetConnectionProvider>
              <InternetWrapper>
                <RouteAwareWrapper>{children}</RouteAwareWrapper>
              </InternetWrapper>
              <Toaster position="top-right" />
            </InternetConnectionProvider>
          </NextIntlClientProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}
