@use "./abstracts" as *;

.dashboard__stats_header {
  background: #fff;
  border-radius: 25px;
  padding: 2rem 2.5rem;
  margin-bottom: 28px;
  box-shadow: 0 2px 9px #00000017;
  display: flex;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
  .dashboard__stats {
    display: flex;
    justify-content: space-between;
    overflow: hidden;
    // gap: 10px;
    // width: calc(100% - 180px);
    z-index: 2;

    .dashboard__stat {
      display: flex;
      flex-direction: column;
      position: relative;
      padding: 0 1.5rem;

      &::after {
        content: "";
        height: 100%;
        position: absolute;
        right: 0;
        top: 0;
        width: 1px;
        background-color: rgba($dark, 0.1);
        z-index: 1;
        margin: auto 0;
      }

      &:first-child {
        padding-left: 0;
      }

      &.border_none {
        padding-right: 0;

        &::after {
          display: none;
        }
      }

      .dashboard__stat_label {
        color: $dark;
        font-size: $text-md;
        font-weight: $bold;
        padding-bottom: 1.2rem;
        @media screen and (min-width: 1200px) and (max-width: 1399px) {
          font-size: 1.5rem;
        }
        @media screen and (min-width: 992px) and (max-width: 1199px) {
          font-size: 1.5rem;
          max-width: 90px;
          min-height: 60px;
        }
      }

      .dashboard__stat_value {
        font-size: $text-xl;
        color: $primary;
        font-weight: $bold;
      }
    }

    .dashboard__stat_design {
      position: relative;
      min-width: 135px;
    }
  }
  .dashboard__stat_image {
    width: 190px;
    height: 125px;
    position: absolute;
    bottom: 0;
    right: -25px;
    object-position: right;
  }
  // mobile screen css
  @media (max-width: 991px) {
    flex-direction: column;
    padding: 2rem;
    .dashboard__stats {
      flex-direction: column;
      .dashboard__stat {
        padding: 0;
        flex-direction: row;
        justify-content: space-between;
        border-bottom: 1px solid rgba($dark, 0.1);
        margin-bottom: 10px;
        &:last-child {
          border-bottom: none;
          margin-bottom: 0;
        }

        &::after {
          display: none;
        }
      }
    }
    .dashboard__stat_image {
      display: none;
    }
  }
}

// job page style
.job_page {
  .job_info {
    border-radius: 8px;
    background: rgba(67, 110, 182, 0.1);
    padding: 5px 15px;
    font-size: $text-sm;
    font-weight: $medium;
    color: $dark;
    &.text_xs {
      font-size: 1.3rem;
    }
    a {
      color: $primary;
      text-decoration: underline !important;
      font-size: $text-sm;
    }
  }
  .section_heading {
    font-size: $text-md;
    font-weight: $bold;
    color: $dark;
    margin-bottom: 16px;
    span {
      color: $primary;
    }
  }
  .interview_form_icon {
    width: 100%;
    height: 260px;
    margin-top: -20px;
    padding: 0px 20px;
  }

  .inner_heading {
    font-size: $text-xxl;
    font-weight: $bold;
    color: $dark;
    margin-bottom: 20px;

    span {
      color: $secondary;
    }
  }
  .skills_info_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    ul {
      display: flex;
      gap: 4rem;
      align-items: center;
      @extend %listSpacing;
      margin-bottom: 0;
      li {
        font-size: $text-sm;
        font-weight: $medium;
        color: $dark;
        display: flex;
        align-items: center;
        gap: 1rem;
        span {
          width: 20px;
          height: 20px;
          border-radius: 100%;
          display: block;
          &.selecting {
            background: $secondary;
          }
          &.selected {
            background: $primary;
          }
          &.selection {
            background: rgba($primary, 0.1);
          }
        }
      }
    }
    .skills_tab {
      @extend %listSpacing;
      display: flex;
      align-items: center;
      border: 1px solid $dark;
      border-radius: 14px;
      display: inline-flex;
      overflow: hidden;

      li {
        font-size: $text-md;
        font-weight: $medium;
        color: $dark;
        cursor: pointer;
        padding: 8px 30px;
        position: relative;
        margin: 0;
        text-align: center;
        min-width: 155px;
        &.active {
          position: relative;
          color: $white;
          background: $primary;
        }
      }
    }
  }
  .career-skill-card {
    min-height: 280px;
  }
}

// dashboard page style
.dashboard_page {
  .dashboard_inner_head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 30px;
    margin-bottom: 20px;
    .header_tab {
      @extend %listSpacing;
      display: flex;
      align-items: center;
      border: 1px solid $dark;
      border-radius: 14px;
      display: inline-flex;
      overflow: hidden;
      li {
        font-size: $text-md;
        font-weight: $medium;
        color: $dark;
        cursor: pointer;
        padding: 8px 30px;
        position: relative;
        margin: 0;
        text-align: center;
        min-width: 155px;
        &.active {
          position: relative;
          color: $white;
          background: $primary;
        }
      }
    }
    .search_box {
      width: 35%;
    }
    @media (max-width: 767px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
      overflow: auto;

      .header_tab {
        border: none;
        border-radius: 14px;
        overflow: auto;
        width: 100%;
        border-radius: 0px;
        white-space: nowrap;
        gap: 15px;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none;
        padding: 0px 5px;
        &::-webkit-scrollbar {
          display: none;
        }
        li {
          font-size: $text-md;
          padding: 5px 0;
          padding-bottom: 10px;
          min-width: max-content;
          font-weight: $bold;
          border-bottom: 2px solid transparent;
          &.active {
            position: relative;
            color: $primary;
            background: transparent;
            border-bottom: 2px solid $primary;
          }
        }
      }
      .search_box {
        width: 100%;
      }
    }
  }
}

//resume_page
.resume_page {
  //upload_resume_page
  &.upload_resume_page {
    .inner_page {
      .operation_admins_img {
        width: 100%;
        height: auto;
        object-fit: contain;
      }
    }
  }

  //manual_upload_resume
  &.manual_upload_resume {
    .inner_page {
      //input_type_file
      .input_type_file {
        input {
          border: 1px solid rgba($white, 0.6);
          padding: 11px 15px;
          font-size: $text-sm;
          border-radius: 12px;
          background: rgba(51, 51, 51, 0.05);
          color: $dark;
          width: 100%;
        }
      }

      //candidate_card
      .candidate_card {
        border-radius: 30px;
        border: 2px solid rgba(0, 0, 0, 0.2);
        background: rgba(255, 255, 255, 0.05);
        padding: 20px;
        margin-bottom: 20px;

        .candidate_card_header {
          margin-bottom: 15px;

          h3 {
            color: $dark;
            font-size: 18px;
            font-style: normal;
            font-weight: 700;
            line-height: normal;
            margin: 0px;
          }
        }
      }

      //add_another_candidate_link
      .add_another_candidate_link {
        text-align: left;
      }
    }
  }

  //candidate_qualification
  &.candidate_qualification_page {
    .inner_page {
      .approved_status_indicator {
        display: flex;
        gap: 30px;
        margin-top: 10px;
        @media (max-width: 576px) {
          flex-wrap: wrap;
          gap: 10px;
        }

        p {
          font-size: 1.3rem;
          font-weight: $medium;
          color: $dark;
          display: flex;
          align-items: center;
          gap: 5px;
          white-space: nowrap;
          margin-bottom: 0;

          span {
            background: rgba(#007733, 0.1);
            border: 1px solid #007733;
            width: 16px;
            min-width: 16px;
            height: 16px;
            // background: rgba(#007733, 0.05);
            // border: 1px solid #007733;
            // width: 24px;
            // min-width: 24px;
            // height: 24px;
            border-radius: 50%;
          }
          &:nth-child(2) {
            span {
              background: rgba(#d00000, 0.05);
              border: 1px solid rgba(#d00000, 0.1);
            }
          }
        }
      }
    }
  }

  //candidates_list_page
  &.candidates_list_page {
    .candidates_list_section {
      margin-bottom: 20px;
      .section_name {
        margin-bottom: 20px;
        h3 {
          font-size: 22px;
          font-weight: 700;
          color: $dark;
          margin-bottom: 6px;
        }
        p {
          font-size: 16px;
          font-weight: 500;
          color: $dark;
          margin: 0px;
        }
      }
    }
  }
}
