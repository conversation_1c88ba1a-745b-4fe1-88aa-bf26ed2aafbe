@use "./abstracts" as *;

.home_page {
  section {
    padding: 60px 0;
  }
  .page_heading {
    margin-bottom: 20px;
    h2 {
      font-size: 3.2rem;
      font-weight: $bold;
      color: $dark;
      margin-bottom: 15px;
      span {
        color: $primary;
      }
    }
    p {
      font-size: 1.6rem;
      font-weight: $medium;
      color: $dark;
      margin: 0;
    }
  }
  @media (max-width: 767px) {
    .page_heading {
      padding-bottom: 10px;
      h2 {
        font-size: 2.4rem;
        margin-bottom: 10px;
      }
      p {
        font-size: 1.4rem;
      }
    }
    section {
      padding: 40px 0;
    }
  }

  .hero_section {
    padding: 100px 0;
    background-image: url("../../public/assets/images/home-bg.png");
    background-size: cover;
    background-position: right;
    background-repeat: no-repeat;
    height: 100vh;
    display: flex;
    align-items: center;

    .banner_content {
      .logo_head_img {
        width: 100px;
        height: 100px;
        object-fit: contain;
        margin-bottom: 20px;
      }
      h1 {
        font-family: "Plus Jakarta Sans";
        font-size: 5.6rem;
        font-weight: 800;
        color: $white;
        margin-bottom: 30px;
        span {
          background: linear-gradient(63deg, #74a8ff 31.35%, #aacaff 42.53%, #5d86cc 54.92%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          display: block;
          &::before {
            content: attr(data-first-letters);
            position: absolute;
            background: #e5be53;
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
      p {
        font-size: $text-lg;
        font-weight: $medium;
        color: $white;
        margin-bottom: 50px;
      }
      button {
        position: relative;
      }
    }
    .banner_image {
      position: relative;
      // padding-left: 40px;
      img {
        width: 100%;
        height: auto;
        z-index: 2;
        position: relative;
      }
    }
    @media (max-width: 767px) {
      padding-bottom: 10px;
      .banner_content {
        .logo_head_img {
          width: 55px;
          height: 55px;
        }
        h1 {
          font-size: 2.6rem;
          margin-bottom: 30px;
        }
        p {
          font-size: 1.4rem;
          margin-bottom: 30px;
        }
      }
      .banner_image {
        padding: 30px;
      }
    }
  }

  .top_brand_section {
    padding-top: 90px;
    padding-bottom: 0px;
    .top_brand_image {
      position: relative;
      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 633px;
        opacity: 0.8;
        z-index: 1;
        background: linear-gradient(54deg, #74a8ff 20.92%, #aacaff 52.91%, #5d86cc 88.37%);
        filter: blur(148.4499969482422px);
      }

      img {
        width: 100%;
        height: auto;
        z-index: 2;
        position: relative;
      }
    }
    @media (max-width: 767px) {
      padding: 40px 0 0;
    }
  }

  .advantage_section {
    .advantage_card {
      border-radius: 32px;
      border: 1px solid #d9d9d9;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      position: relative;
      min-height: 570px;
      height: 570px;
      justify-content: space-between;
      &::after {
        content: "";
        position: absolute;
        bottom: -270px;
        left: -210px;
        width: 400px;
        height: 400px;
        border-radius: 100%;
        background: radial-gradient(50% 50%, #aacaff 0%, #436eb6 100%);
        filter: blur(256.05px);
        z-index: -1;
      }

      .card_body {
        padding: 30px;
        h3 {
          font-size: $text-xxl;
          font-weight: $bold;
          color: $dark;
          margin-bottom: 25px;
        }
        p {
          font-size: $text-md;
          font-weight: $medium;
          color: $dark;
        }
      }

      .card_image {
        padding-left: 30px;
        width: 100%;
        object-fit: cover;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;
        margin-top: 10px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          margin-bottom: -1px;
          margin-right: -1px;
          object-position: left top;
        }
      }
    }
    @media (max-width: 767px) {
      .advantage_card {
        border-radius: 24px;
        height: auto;
        min-height: auto;
        .card_body {
          padding: 15px;
          h3 {
            font-size: 1.8rem;
            margin-bottom: 15px;
          }
          p {
            font-size: 1.4rem;
          }
        }
      }
    }
  }

  // Growth_section start
  .growth_section {
    padding-top: 10px;
    .page_heading {
      margin-bottom: 25px;
    }
    .team_img {
      border-radius: 25px;
      width: 100%;
      height: 100%;
      overflow: hidden;
      box-shadow: 0 4px 12px #0000001a;
      position: relative;

      .img {
        width: 100%;
        height: 100%;
      }

      .team_content {
        padding: 20px;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: end;
        justify-content: end;

        .team_info {
          min-height: 115px;

          h4 {
            font-size: 2rem;
            font-weight: $bold;
            color: $white;
            margin-bottom: 10px;
          }

          p {
            font-size: 1.4rem;
            font-weight: $medium;
            color: rgba($white, 0.6);
            min-height: 80px;
            margin-bottom: 0;
          }
        }
      }
    }
    @media (max-width: 767px) {
      padding-top: 10px;
      .page_heading {
        padding-bottom: 10px;
      }
      .team_img {
        border-radius: 24px;
      }
    }
  }

  // Growth_section end

  // Expeience_section start
  .expeience_section {
    padding-top: 10px;
    padding-bottom: 60px;
    .star_content {
      padding: 25px;
      margin-top: 30px;
      border-radius: 32px;
      background: $white;
      box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.09);

      .expeience_text {
        font-size: $text-md;
        font-weight: $medium;
        color: $dark;
        padding: 15px 0;
      }

      .star_icon {
        display: flex;
        align-items: center;
        gap: 10px;
        border-bottom: solid 1px #ccc;
        padding-bottom: 20px;

        img {
          width: 20px;
          height: 20px;
          object-fit: contain;
        }
      }

      .star_founder {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .quotes {
          width: 60px;
          height: 60px;
        }
        .star_founder_img {
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 15px;
          .star_img {
            width: 50px;
            height: 50px;
          }

          .star_founder_info {
            h4 {
              font-weight: $bold;
              margin-bottom: 3px;
              font-size: 1.8rem;
            }

            p {
              font-size: 14px;
              margin: 0;
              span {
                font-weight: $bold;
              }
            }
          }
        }
      }
      @media (max-width: 767px) {
        .star_founder {
          .star_founder_info {
            h4 {
              font-weight: $bold;
              margin-bottom: 3px;
              font-size: 1.8rem;
            }

            p {
              font-size: 14px;
              margin: 0;
            }
          }
        }
      }
    }

    .expeience_bg_img {
      background-image: url("../../public/assets/images/path.png");
      background-size: contain;
      background-position: top center;
      background-repeat: no-repeat;
      @media (max-width: 767px) {
        display: none;
      }

      .frame_img {
        height: 460px;
        width: 100%;

        display: flex;
        justify-content: center;
        align-items: flex-end;

        img {
          width: 330px;
          height: 422px;
          object-fit: contain;
          display: block;
        }
      }
    }
  }
  // subscription_plans-----
  .subscription_plans {
    background: linear-gradient(to top, rga($primary, 0.1), $white);
    .wrapper {
      display: flex;
      justify-content: flex-start;
      padding: 2rem;
      gap: 1rem;

      .card {
        background: #f5f5f5;
        border-radius: 10px;
        padding: 1.5rem;
        width: 240px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

        &.cardPro {
          background: linear-gradient(135deg, #e0f2fe, #bae6fd);
        }

        &.cardEnterprise {
          background: linear-gradient(135deg, #ede9fe, #ddd6fe);
        }

        .planTitle {
          font-weight: 600;
          font-size: 1.2rem;
          margin-bottom: 1rem;
          text-align: center;
        }

        .benefitList {
          list-style: none;
          padding: 0;
          margin: 0;

          .benefitRow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.4rem 0;
            border-bottom: 1px solid #ddd;
            font-size: 0.95rem;

            .iconImg {
              width: 18px;
              height: 18px;
            }
          }
        }
      }
    }
  }
  .difference_section {
    .inner_section {
      padding-top: 80px;
      .path_svg {
        svg {
          width: 100%;
          max-width: 100%;
          height: auto;
        }
      }
    }
  }
}
