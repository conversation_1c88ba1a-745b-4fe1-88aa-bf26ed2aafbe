"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import style from "../../../styles/conductInterview.module.scss";
import Image from "next/image";
import user from "../../../../public/assets/images/user.png";
import Button from "@/components/formElements/Button";
import FinalAssessmentIcon from "@/components/svgComponents/FinalAssessmentIcon";
import CandidateResumetIcon from "@/components/svgComponents/CandidateResumetIcon";
import { createFinalAssessment, getAssessmentStatus } from "@/services/assessmentService";
import { toastMessageSuccess, toastMessageError, getEncryptedData } from "@/utils/helper";
import { useTranslations } from "next-intl";
import routes from "@/constants/routes";
import FinalAssessmentConfirmModal from "@/components/commonModals/FinalAssessmentConfirmModal";
import QuestionGeneratorLoader from "@/components/loader/QuestionGeneratorLoader";
import { useTranslate } from "@/utils/translationUtils";

export interface IAssessmentStatus {
  exists: boolean;
  isAssessmentShared: boolean;
  isAssessmentSubmitted: boolean;
  assessmentId: number | null;
}
const BUTTON_STATES = {
  IDLE: "idle",
  LOADING: "loading",
  GENERATING: "generating",
};
const InterviewSummary = (params: Promise<{ jobId: string; jobApplicationId: string }>) => {
  const router = useRouter();
  const t = useTranslations();
  const translate = useTranslate();
  const searchParamsPromise = React.use(params);
  type ButtonState = (typeof BUTTON_STATES)[keyof typeof BUTTON_STATES];
  // const searchParams = useSearchParams() || new URLSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [buttonState, setButtonState] = useState<ButtonState>(BUTTON_STATES.IDLE);
  const [assessmentStatus, setAssessmentStatus] = useState<IAssessmentStatus>();
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  //  dummy data
  const jobId = Number(searchParamsPromise.jobId);
  const jobApplicationId = Number(searchParamsPromise.jobApplicationId);
  /**
   * Show the confirmation modal for final assessment
   */
  const showFinalAssessmentConfirmation = () => {
    if (!jobId || !jobApplicationId) {
      toastMessageError(t("job_id_and_job_application_id_are_required"));
      return;
    }
    setShowConfirmModal(true);
  };

  /**
   * Handle creating final assessment
   */
  const handleCreateFinalAssessment = async () => {
    if (!jobId || !jobApplicationId) {
      toastMessageError(t("job_id_and_job_application_id_are_required"));
      return;
    }

    try {
      setIsLoading(true);
      setButtonState(BUTTON_STATES.GENERATING);
      setShowConfirmModal(false);
      const response = await createFinalAssessment({ jobId, jobApplicationId });
      if (response && response.data && response.data.success) {
        toastMessageSuccess(translate(response?.data?.message || "final_assessment_created_successfully"));

        // Get the finalAssessmentId from the response data
        const finalAssessmentId = response.data.data?.assessmentId;

        if (finalAssessmentId) {
          // Redirect to the final assessment page with the finalAssessmentId and default status values
          // For a newly created assessment, both isShared and isSubmitted will be false
          router.push(
            `${routes.FINAL_ASSESSMENT.FINAL_ASSESSMENT}?finalAssessmentId=${getEncryptedData({ finalAssessmentId })}&jobId=${getEncryptedData({ jobId })}&jobApplicationId=${getEncryptedData({ jobApplicationId })}`
          );
        }
      } else {
        toastMessageError(translate(response?.data?.message || "failed_to_create_final_assessment"));
      }
    } catch (error) {
      console.error("Error creating final assessment:", error);
      toastMessageError(t("an_error_occurred_while_creating_the_final_assessment"));
    } finally {
      setIsLoading(false);
      setButtonState(BUTTON_STATES.IDLE);
    }
  };

  /**
   * Close the confirmation modal
   */
  const handleCancelConfirmModal = () => {
    setShowConfirmModal(false);
  };

  // Effect to check assessment status when component mounts
  useEffect(() => {
    // Automatically call getAssessmentStatus when the component mounts
    const checkStatus = async () => {
      if (!jobId || !jobApplicationId) {
        return;
      }

      try {
        setIsLoading(true);
        const response = await getAssessmentStatus(jobApplicationId.toString());

        if (response?.data) {
          const assessmentData = response?.data?.data;

          // Update assessment status state
          setAssessmentStatus({
            exists: !!assessmentData.assessmentId, //exists: assessmentData.id ? true : false
            isAssessmentShared: assessmentData.isAssessmentShared || false,
            isAssessmentSubmitted: assessmentData.isAssessmentSubmitted || false,
            assessmentId: assessmentData.assessmentId || null,
          });
        } else {
          toastMessageError(translate(response?.data?.message || "failed_to_get_assessment_status"));
        }
      } catch (error) {
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    };

    checkStatus();
  }, [t]);
  return (
    <>
      <QuestionGeneratorLoader show={buttonState === BUTTON_STATES.GENERATING} />
      {showConfirmModal && (
        <FinalAssessmentConfirmModal onClickCancel={handleCancelConfirmModal} onClickGenerate={handleCreateFinalAssessment} disabled={isLoading} />
      )}
      <div className={style.conduct_interview_page}>
        <div className="container">
          <div className="common-page-header">
            <div className="common-page-head-section">
              <div className="main-heading">
                <h2>
                  {t("interview_summary")}
                  <span>{t("summary")}</span>
                </h2>
              </div>
            </div>
          </div>

          <div className="inner-section">
            <div className="user-summary">
              <div className="info">
                <h4 className="user-name">{t("benedict_cumberbatch")}</h4>
                <h5 className="user-role">{t("operations_admin")}</h5>
              </div>
              <div className="actions">
                <Button
                  className="clear-btn m-0 p-0"
                  onClick={() => {
                    if (!isLoading) {
                      if (!assessmentStatus?.exists) {
                        showFinalAssessmentConfirmation();
                      } else if (assessmentStatus?.assessmentId) {
                        // Redirect to the final assessment page with the existing assessment ID
                        router.push(
                          `${routes.FINAL_ASSESSMENT.FINAL_ASSESSMENT}?finalAssessmentId=${getEncryptedData({ finalAssessmentId: assessmentStatus.assessmentId })}&jobId=${getEncryptedData({ jobId })}&jobApplicationId=${getEncryptedData({ jobApplicationId })}`
                        );
                      }
                    }
                  }}
                  disabled={isLoading}
                >
                  <FinalAssessmentIcon />
                  {buttonState === "loading" || (isLoading && buttonState !== "generating")
                    ? t("loading")
                    : buttonState === "generating"
                      ? t("generating_questions")
                      : !assessmentStatus?.exists
                        ? t("create_final_assessment")
                        : assessmentStatus.isAssessmentSubmitted
                          ? t("view_final_assessment_result")
                          : t("view_final_assessment")}
                </Button>
                <Button className="clear-btn m-0 p-0">
                  <CandidateResumetIcon />
                  {t("preview_candidate_resume")}
                </Button>
              </div>
            </div>
            <div className="interview-summary">
              <div className="summary-header">
                <h1 className="summary-heading">{t("round_1_summary")}</h1>
              </div>
              <div className="interviewer">
                <h2 className="summary-title">Interview By</h2>

                <div className="interviewer-info">
                  <Image src={user} alt="Interviewer avatar" className="interviewer-avatar" />
                  <span className="interviewer-name">{t("aaron_salko")}</span>
                </div>
              </div>
              <div className="summary-scores">
                <h2 className="summary-title">Scores</h2>
                <div className="score-btns">
                  <Button className="secondary-btn rounded-md px-3 py-3">Hard Skills : 07</Button>
                  <Button className="secondary-btn rounded-md px-3 py-3">Work Ethic : 08</Button>
                  <Button className="secondary-btn rounded-md px-3 py-3">Humility : 06</Button>
                  <Button className="secondary-btn rounded-md px-3 py-3">Drive : 09</Button>
                  <Button className="secondary-btn rounded-md px-3 py-3">Confidence : Extreme</Button>
                </div>
              </div>
              <div className="summary-highlights">
                <h2 className="summary-title">Highlights</h2>
                <ul className="highlight-list">
                  <li className="highlight-item">Proficient in managing schedules and using tools like [specific tools].</li>
                  <li className="highlight-item">Adaptable under high workload and challenging scenarios.</li>
                  <li className="highlight-item">Ensures compliance with safety and organizational standards.</li>
                  <li className="highlight-item">Proactive in taking ownership beyond assigned tasks.</li>
                </ul>
              </div>
            </div>
            <div className="interview-summary">
              <div className="summary-header">
                <h1 className="summary-heading">Your Performance Feedback</h1>
              </div>
              <div className="interviewer">
                <div className="interviewer-info large">
                  <Image src={user} alt="Interviewer avatar" className="interviewer-avatar" />
                  <span className="interviewer-name">Aaron Salko</span>
                </div>
              </div>
              <div className="summary-highlights">
                <h2 className="summary-title">Highlights</h2>
                <ul className="highlight-list">
                  <li className="highlight-item">Proficient in managing schedules and using tools like [specific tools].</li>
                  <li className="highlight-item">Adaptable under high workload and challenging scenarios.</li>
                  <li className="highlight-item">Ensures compliance with safety and organizational standards.</li>
                  <li className="highlight-item">Proactive in taking ownership beyond assigned tasks.</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default InterviewSummary;
