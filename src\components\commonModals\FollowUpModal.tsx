"use client";
import React from "react";
import AiMarkIcon from "../svgComponents/AiMarkIcon";
import Button from "../formElements/Button";
import { FOLLOW_UP_TYPE } from "@/constants/commonConstants";
import { useTranslations } from "next-intl";

const FollowUpModal = ({
  followupQuestion,
  skillName,
  type,
  onProceed,
  onCancel,
  loading,
  skillQuestions,
}: {
  type: string;
  loading: boolean;
  onProceed: (selectedQuestions?: string[]) => void;
  onCancel: () => void;
  followupQuestion?: string;
  skillName?: string;
  skillQuestions?: string[];
}) => {
  const t = useTranslations();

  const [selectedQuestions, setSelectedQuestions] = React.useState<number[]>([]);

  const handleCheckboxChange = (index: number) => {
    setSelectedQuestions((prev) => (prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]));
  };

  const handleProceed = () => {
    if (type === FOLLOW_UP_TYPE.SKILL && skillQuestions) {
      const selected = selectedQuestions.map((index) => skillQuestions[index]);
      onProceed(selected);
    } else {
      onProceed();
    }
  };

  return (
    <div className="follow-up-modal">
      <div className="follow-up-container">
        <p className="sub-title">
          <AiMarkIcon className="p-1 ps-0" />
          {t("follow_up_generated", {
            type: type === FOLLOW_UP_TYPE.SKILL ? t("skill") : t("question"),
          })}
        </p>
        <h4>{followupQuestion ? followupQuestion : t("generated_follow_up_skill_name", { skillName: skillName ?? "" })}</h4>
        {/* this is follow up question start */}
        {type === FOLLOW_UP_TYPE.SKILL ? (
          <div className="question-group">
            <p className="question-label">{t("pick_a_question")}</p>
            {skillQuestions?.map((question, index) => (
              <label className="radio-item" key={index}>
                <input type="checkbox" name="boldness" onChange={() => handleCheckboxChange(index)} />
                <span className="radio-text">{question}</span>
              </label>
            ))}
          </div>
        ) : null}
        {/* this is follow up question end */}
        <div className="action-btn">
          <Button className="primary-btn w-100" onClick={handleProceed} disabled={loading} loading={loading}>
            {t("proceed")}
          </Button>
          <Button className="dark-outline-btn w-100" onClick={onCancel} disabled={loading}>
            {t("cancel")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FollowUpModal;
