"use client";
import React, { FC, useState } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import InputWrapper from "../formElements/InputWrapper";
import Textarea from "../formElements/Textarea";
import { useForm } from "react-hook-form";
import GreenCheckIcon from "../svgComponents/GreenCheckIcon";
import RoundCrossIcon from "../svgComponents/RoundCrossIcon";
import { changeApplicationStatus } from "@/services/screenResumeServices";
import { useSelector } from "react-redux";
import { AuthState } from "@/redux/slices/authSlice";
import { JobApplication } from "@/interfaces/jobRequirementesInterfaces";
import { APPLICATION_STATUS } from "@/constants/jobRequirementConstant";
import Lottie from "lottie-react";
import rejected from "../../../public/assets/images/rejected.json";
import hurray from "../../../public/assets/images/hurray.json";
import { toastMessageSuccess, toTitleCase } from "@/utils/helper";

interface IProps {
  onClickCancel: () => void;
  disabled?: boolean;
  candidate?: JobApplication;
  aiDecision?: string;
  actionType: (typeof APPLICATION_STATUS)[keyof typeof APPLICATION_STATUS];
  onSuccess?: () => void;
}

const CandidateApprovalModal: FC<IProps> = ({ onClickCancel, candidate, actionType = APPLICATION_STATUS.APPROVED, onSuccess, aiDecision }) => {
  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<{ reason: string }>({
    defaultValues: {
      reason: "",
    },
    // Add validation rules here for the component
    mode: "onSubmit",
    criteriaMode: "firstError",
    shouldFocusError: true,
    reValidateMode: "onChange",
    resolver: (values) => {
      const errors: Record<string, { type: string; message: string }> = {};

      // Required validation for reason field
      if (!values.reason || values.reason.trim() === "") {
        errors.reason = {
          type: "required",
          message: "Please provide a reason",
        };
      } else if (values.reason.trim().length < 5) {
        errors.reason = {
          type: "minLength",
          message: "Reason should be at least 5 characters long",
        };
      } else if (values.reason.trim().length > 50) {
        errors.reason = {
          type: "maxLength",
          message: "Reason should not exceed 50 characters",
        };
      }

      return {
        values,
        errors,
      };
    },
  });

  const onSubmit = async (formData: { reason: string }) => {
    if (!candidate || !authData) return;

    try {
      setIsSubmitting(true);
      setError("");

      const data = {
        hiring_manager_reason: formData.reason,
        job_id: candidate.job_id,
        candidate_id: candidate.candidate_id,
        hiring_manager_id: authData.id,
        status: actionType,
      };

      const response = await changeApplicationStatus(data);

      if (response.data && response.data.success) {
        toastMessageSuccess(`Candidate successfully marked as ${actionType.toLowerCase()}.`);
        setSuccess(true);
        // Call the onSuccess callback if provided
        if (onSuccess) {
          setTimeout(() => {
            onClickCancel();
            onSuccess();
          }, 1500);
        }
      } else {
        setError(response.data?.message || "Failed to update candidate status");
      }
    } catch (err) {
      console.error("Error updating candidate status:", err);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header justify-content-center">
            {aiDecision === APPLICATION_STATUS.APPROVED && (
              <>
                <h2 className="d-flex align-items-center justify-content-center">
                  Hurray! <Lottie animationData={hurray} style={{ width: "100px", height: "100px" }} />
                </h2>
                <p>Candidate marked as a good fit, shortlisted for the next step.</p>
              </>
            )}
            {aiDecision === APPLICATION_STATUS.REJECTED && (
              <>
                <h2 className="d-flex align-items-center justify-content-center">
                  Uh-Oh! <Lottie animationData={rejected} style={{ width: "100px", height: "100px" }} />
                </h2>
                <p>The candidate is unfit for the job.</p>
              </>
            )}
            {!isSubmitting && (
              <Button className="modal-close-btn" onClick={onClickCancel}>
                <ModalCloseIcon />
              </Button>
            )}
          </div>
          <div className="modal-body">
            {/* qualification-card */}
            <div className="qualification-card">
              <div className="qualification-card-top">
                <div className="name">
                  <h3>{toTitleCase(candidate?.candidate_name || "Candidate")}</h3>
                  <p>{candidate?.ai_decision || "Pending"} by S9 InnerView</p>
                </div>
                <div className="top-right">
                  <div className={`${actionType.toLowerCase()}-status`}>
                    <p>
                      {actionType === APPLICATION_STATUS.APPROVED && <GreenCheckIcon />}
                      {actionType === APPLICATION_STATUS.REJECTED && <RoundCrossIcon />}
                      {actionType} By You
                    </p>
                  </div>
                </div>
              </div>
              <div className="qualification-card-mid">
                <p>
                  <b>
                    {aiDecision === APPLICATION_STATUS.REJECTED
                      ? "Why the candidate is unfit for the role"
                      : "Why the candidate is a good fit for the role"}
                  </b>
                </p>
                <p>{candidate?.ai_reason || "No reason provided by AI evaluation."}</p>
              </div>
              {/* <div className="button-align">
                <Button className="secondary-btn rounded-md">Approve Candidate</Button>
                <Button className="dark-outline-btn rounded-md">Reject Candidate</Button>
              </div> */}
            </div>
            {!success && (
              <form onSubmit={handleSubmit(onSubmit)}>
                <InputWrapper>
                  <InputWrapper.Label htmlFor="reason" required>
                    {actionType === APPLICATION_STATUS.APPROVED ? "Reason for approval" : "Reason for rejection"}
                  </InputWrapper.Label>
                  <Textarea
                    rows={4}
                    name="reason"
                    control={control}
                    placeholder={
                      actionType === APPLICATION_STATUS.APPROVED
                        ? "Please enter the reason for approving this candidate"
                        : "Please enter the reason for rejecting this candidate"
                    }
                    className="form-control"
                  />
                  {errors.reason && <p className="text-danger mt-1">{errors.reason.message as string}</p>}
                </InputWrapper>

                {error && <div className="error-message alert alert-danger my-3">{error}</div>}

                <Button type="submit" className="primary-btn rounded-md w-100" disabled={isSubmitting}>
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
export default CandidateApprovalModal;
