import { Step } from "react-joyride-react-19";

/**
 * Tour step configurations for different pages
 *
 * Each page has its own array of Step objects
 * - target: CSS selector or ID of the element to highlight
 * - content: Tooltip content describing the feature
 * - placement: Where the tooltip appears relative to the element
 * - disableBeacon: Whether to disable the pulsing beacon (first step only)
 */

export const dashboardSteps: Step[] = [
  {
    target: "#create-job-card",
    content: "Allows you to post a new job opening.",
    disableBeacon: true,
    placement: "bottom",
  },
  {
    target: "#screen-resumes-card",
    content: "Lets you review resumes submitted by applicants.",
    placement: "bottom",
  },
  {
    target: "#conduct-interviews-card",
    content: "Provides access to scheduled interviews.",
    placement: "bottom",
  },
  {
    target: "#interviews-section",
    content: "Displays interviews scheduled for the future and records of past interviews.",
    placement: "bottom",
  },
];

/**
 * Interview Feedback page walkthrough steps
 */
export const interviewFeedbackSteps: Step[] = [
  {
    target: "#career-feedback-section .summary-heading",
    content: "Allows evaluators to provide structured feedback on general career-related skills.",
    disableBeacon: true,
    placement: "bottom",
  },
  {
    target: "#behavioral-performance-section",
    content: "Summarizes key behavioral performance observations.",
    placement: "right",
  },
  {
    target: "#role-culture-skills-section",
    content: "Focuses on the skills directly tied to the role and cultural fit. Includes scoring and written evaluation for each skill.",
    placement: "bottom",
  },
  {
    target: "#interview-verdict-buttons",
    content: "Allows you to finalize the candidate's evaluation outcome",
    placement: "bottom",
  },
];

/**
 * Calendar page walkthrough steps
 */
export const calendarSteps: Step[] = [
  {
    target: "#calendar-nav-buttons", // Left part with navigation ar    target: ".fc-toolbar-chunk:nth-child(1)", // Left part with navigation arrows
    content: "Lets you move between months.",
    disableBeacon: true,
    placement: "bottom",
  },
  {
    target: ".fc-toolbar-chunk:nth-child(3)", // Right part of the toolbar with view toggles
    content: "Allows switching between a month-wise or week-wise calendar view.",
    disableBeacon: true,
    placement: "bottom",
  },
  {
    target: ".calendar-container", // The calendar itself for event creation
    content: "Allows adding new interviews or editing details for existing interviews on current and future dates.",
    placement: "bottom",
  },
  {
    target: ".fc-daygrid-day, .fc-timegrid-slot", // Calendar events area
    content: "Displays all interviews, meetings, and tasks on their respective dates.",
    placement: "top",
  },
  {
    target: ".fc-daygrid-more-link", // +More links
    content: "Shows additional events on a date when there are more than can be displayed.",
    placement: "left",
  },
];

/**
 * Top 10 Candidates section walkthrough steps
 */
export const top10CandidatesSteps: Step[] = [
  {
    target: "#top-candidates-section", // Top Candidates List section
    content: "Displays candidates who have performed best based on interviews to date, ranked by ATS score and approval status.",
    disableBeacon: true,
    placement: "top",
  },
  {
    target: "#ats-score-column", // ATS Score column
    content: "Shows the percentage score from the Applicant Tracking System based on resume and interview evaluation.",
    placement: "bottom",
  },
  {
    target: "#candidate-status-column", // Candidates Analysis Status
    content: "Indicates the evaluation result (e.g., Hired, Approved, Final-Reject, Pending).",
    placement: "bottom",
  },
  {
    target: "#other-candidates-heading", // Other Candidates Heading
    content: "Lists candidates ranked by resume/job description analysis but not in the top category.",
    placement: "top",
  },
];

/**
 * Candidate Profile page walkthrough steps
 */
/**
 * Online Interview page walkthrough steps
 */
export const interviewSteps: Step[] = [
  {
    target: "#video-controls", // Video Controls section
    content: "Displays the live video feed of the interviewer and candidate with controls for camera, mic, and leaving the interview.",
    disableBeacon: true,
    placement: "right",
  },
  {
    target: "#progress-tracker", // Interview Progress Bar
    content: "Shows the current interview stage (e.g., Career-Based Interview, Skill & Culture Based Interview, Candidate Queries).",
    placement: "bottom",
  },
  {
    target: "#interview-question-cards-height", // Question & Notes Section
    content: "Lists predefined interview questions for the current stage with note-taking fields.",
    placement: "right",
  },
  {
    target: "#behavioral-performance-section", // Behavioral Performance Panel
    content: "Dedicated space to document candidate's behavioral traits and other observations.",
    placement: "left",
  },
];

/**
 * Interview Question page walkthrough steps
 */
export const interviewQuestionSteps: Step[] = [
  {
    target: "#common-page-header", // Header section with progress tracker
    content: "Shows your progress through the interview process with recording controls for capturing candidate responses.",
    disableBeacon: true,
    placement: "bottom",
  },
  {
    target: "#behavioral-performance-section", // Behavioral Performance Panel
    content: "Space to document candidate’s behavioral observations during the interview.",
    placement: "right",
  },
  {
    target: "#container", // Interview Questions & Notes
    content: "Lists structured questions for the selected skill with space for interviewer notes.",
    placement: "left",
  },
  {
    target: "#number-task", // Scoring System
    content: "Provides a rating scale from 1-9 plus an 'Extreme' option to evaluate the candidate's proficiency for the skill.",
    placement: "bottom",
  },
];

/**
 * Permissions section walkthrough steps
 */
export const permissionsSteps: Step[] = [
  {
    target: "#permissions-count-display", // Permission Counts column
    content: "Shows the number of permissions assigned to each user role.",
    disableBeacon: true,
    placement: "bottom",
  },
  {
    target: "#edit-permissions-button", // Edit Permissions Action
    content: "Allows modification of permissions assigned to a specific role.",
    placement: "bottom",
  },
];

/**
 * User Roles section walkthrough steps
 */
export const userRolesSteps: Step[] = [
  {
    target: "#user-roles-list", // User Roles List section
    content: "Displays the available roles in the system such as Administrator, Human Resources, and Interviewer.",
    disableBeacon: true,
    placement: "right",
  },
  {
    target: "#add-new-role-button", // Add New Role button
    content: "Allows creation of a new user role.",
    placement: "bottom",
  },
  {
    target: "#user-role-button", // Edit/Delete buttons
    content: "Provides options to modify or remove an existing role.",
    placement: "right",
  },
];

export const candidateProfileSteps: Step[] = [
  {
    target: "#ai-summary-section", // AI Summary section
    content: "Provides AI-generated insights on candidate strengths, areas of concern, and cultural fit.",
    disableBeacon: true,
    placement: "top",
  },
  {
    target: "#common-tab", // Tabs section
    content: "Allows switching between Skill-Specific Assessment and Interview History views.",
    placement: "bottom",
  },
  {
    target: "#improvement-areas", // Improvement areas
    content: "Highlights specific skills or competencies where the candidate needs improvement.",
    placement: "top",
  },
];

/**
 * Performance Based Skills page walkthrough steps
 */
export const performanceBasedSkillsSteps: Step[] = [
  {
    target: "#available-skills-section", // Available Skills section
    content: "Displays a list of role-specific performance-based skills you can choose from.",
    disableBeacon: true,
    placement: "auto",
  },
  {
    target: "#skills-drag-drop-icon", // Drag & Drop selection
    content: "Allows moving skills from the Available Skills list to the Selected Skills section.",
    placement: "auto",
  },
  {
    target: "#selected-role-skills", // Selected Skills section
    content: "Shows the 10 skills you have chosen for the role, along with descriptions.",
    placement: "auto",
  },
  {
    target: "#ai-suggestions-section", // AI-Powered Suggestions (culture skills section)
    content: "Provides additional skills based on the role description and cultural fit.",
    placement: "auto",
  },
];
