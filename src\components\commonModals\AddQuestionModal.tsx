"use client";
import React, { FC, useState, useEffect, useRef } from "react";
import Button from "../formElements/Button";
// import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import InputWrapper from "../formElements/InputWrapper";
import Textbox from "../formElements/Textbox";
import { useForm, useFieldArray } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import Loader from "../loader/Loader";
import Select from "../formElements/Select";
import { createAssessmentQuestion } from "@/services/assessmentService";
import { toastMessageError } from "@/utils/helper";
import { AddQuestionFormData, AddQuestionModalProps, ICreateAssessmentQuestionRequest, QuestionOption } from "@/interfaces/finalAssessment";
import { addQuestionValidationSchema } from "@/validations/finalAssessmentValidations";
import { DEFAULT_MCQ_OPTIONS, DEFAULT_TRUE_FALSE_OPTIONS, OPTION_ID, QUESTION_TYPE } from "@/constants/commonConstants";
import { useTranslate } from "@/utils/translationUtils";
import InfoIcon from "../svgComponents/InfoIcon";

export const AddQuestionModal: FC<AddQuestionModalProps> = ({ onClickCancel, onSubmitSuccess, skillId, skillTitle, finalAssessmentId }) => {
  const t = useTranslate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [formDisabled, setFormDisabled] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);

  // Initialize form with react-hook-form
  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<AddQuestionFormData>({
    defaultValues: {
      question: "",
      questionType: QUESTION_TYPE.MCQ,
      options: DEFAULT_MCQ_OPTIONS,
      correctAnswer: "",
    },
    resolver: yupResolver(addQuestionValidationSchema(t)),
    mode: "onChange",
  });

  // Use field array for dynamic options
  const { fields, replace } = useFieldArray({
    control,
    name: "options",
  });

  // Watch for changes in the question type
  const questionType = watch("questionType");

  // Helper function to ensure we're using the right option IDs
  const getOptionId = (field: QuestionOption, index: number): string => {
    // If the field has our custom id property with expected values
    if (
      typeof field.id === "string" &&
      (field.id === OPTION_ID.A ||
        field.id === OPTION_ID.B ||
        field.id === OPTION_ID.C ||
        field.id === OPTION_ID.D ||
        field.id === OPTION_ID.TRUE ||
        field.id === OPTION_ID.FALSE)
    ) {
      return field.id;
    }

    // Otherwise, determine the ID based on question type
    if (questionType === QUESTION_TYPE.TRUE_FALSE) {
      return index === 0 ? OPTION_ID.TRUE : OPTION_ID.FALSE;
    } else {
      // For MCQ, return A, B, C, D based on index
      return String.fromCharCode(65 + index); // 65 is ASCII for 'A'
    }
  };

  // Update options when question type changes
  useEffect(() => {
    if (questionType === QUESTION_TYPE.TRUE_FALSE) {
      replace(DEFAULT_TRUE_FALSE_OPTIONS);
      // Set a default correct answer for true/false questions
      // setValue("correctAnswer", OPTION_ID.TRUE);
    } else if (questionType === QUESTION_TYPE.MCQ) {
      replace(DEFAULT_MCQ_OPTIONS);
      setValue("correctAnswer", "");
    }
  }, [questionType, replace, setValue]);

  // Prevent body scrolling when modal is open
  useEffect(() => {
    // Save the original overflow style
    const originalStyle = window.getComputedStyle(document.body).overflow;

    // Disable scrolling on body
    document.body.style.overflow = "hidden";

    // Re-enable scrolling when component unmounts
    return () => {
      document.body.style.overflow = originalStyle;
    };
  }, []);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (isSubmitting) return;
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClickCancel();
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClickCancel, isSubmitting]);
  const onSubmit = async (data: AddQuestionFormData) => {
    try {
      setIsSubmitting(true);
      setFormDisabled(true);
      setSubmitError(null);

      // Ensure we're using the correct option IDs
      const formattedOptions = data.options.map((option, index) => ({
        id: getOptionId(option, index),
        text: option.text,
      }));

      // Prepare the request data
      const requestData: ICreateAssessmentQuestionRequest = {
        finalAssessmentId,
        question: data.question,
        questionType: data.questionType,
        skillId,
        options: {
          options: formattedOptions,
        },
        correctAnswer: data.correctAnswer,
      };

      // Make the API call to add the question
      try {
        const response = await createAssessmentQuestion(requestData);

        if (response.data && response.data.success) {
          onSubmitSuccess();
        } else {
          const errorMessage = t(response.data?.message || "failed_to_add_question");
          toastMessageError(errorMessage);
          setSubmitError(errorMessage);
        }
      } catch (apiError: unknown) {
        const error = apiError as { response?: { data?: { message?: string } } };
        const errorMessage = error.response?.data?.message || "failed_to_add_question";
        toastMessageError(t(errorMessage));
        setSubmitError(t(errorMessage));
      }
    } catch (error) {
      console.error(error);
      toastMessageError(t("an_unexpected_error_occurred_while_adding_the_question"));
      setSubmitError(t("an_unexpected_error_occurred_while_adding_the_question"));
    } finally {
      setIsSubmitting(false);
      setFormDisabled(false);
    }
  };

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered modal-lg">
        <div className="modal-content" ref={modalRef}>
          <div className="modal-header pb-0">
            <h2 className="text-center m-0">
              Add New Question <br /> <span className="color-primary">{skillTitle}</span>
            </h2>
          </div>
          <div className="modal-body">
            <form onSubmit={handleSubmit(onSubmit)}>
              {/* Question Input */}
              <div className="mb-4">
                <InputWrapper>
                  <InputWrapper.Label required>{t("question")}</InputWrapper.Label>
                  <Textbox
                    name="question"
                    control={control}
                    placeholder={t("enter_your_question")}
                    className="form-control"
                    disabled={formDisabled}
                  />
                  {errors.question?.message && <InputWrapper.Error message={errors.question?.message} />}
                </InputWrapper>
              </div>

              {/* Question Type */}
              <div className="mb-4">
                <InputWrapper>
                  <InputWrapper.Label required>{t("question_type")}</InputWrapper.Label>
                  <div className="d-flex gap-4 mt-2 flex-wrap">
                    <div
                      className={`question-type-radio ${questionType === QUESTION_TYPE.MCQ ? "selected" : ""}`}
                      onClick={() => !formDisabled && setValue("questionType", QUESTION_TYPE.MCQ)}
                      style={{
                        padding: "12px 15px",
                        border: `1px solid ${questionType === QUESTION_TYPE.MCQ ? "#f0f7ff" : "#3333330D"}`,
                        borderRadius: "14px",
                        cursor: "pointer",
                        display: "flex",
                        alignItems: "center",
                        backgroundColor: questionType === QUESTION_TYPE.MCQ ? "#f0f7ff" : "white",
                        transition: "all 0.2s ease",
                        flex: 1,
                      }}
                    >
                      <div
                        style={{
                          width: "24px",
                          minWidth: "24px",
                          height: "24px",
                          borderRadius: "50%",
                          border: `1px solid ${questionType === QUESTION_TYPE.MCQ ? "#436EB6" : "rgba(51, 51, 51, 0.6)"}`,
                          marginRight: "12px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        {questionType === QUESTION_TYPE.MCQ && (
                          <div
                            style={{
                              width: "12px",
                              height: "12px",
                              borderRadius: "50%",
                              backgroundColor: "#436EB6",
                            }}
                          />
                        )}
                      </div>
                      <div>
                        <div style={{ fontWeight: 500, color: "#333", fontSize: "15px", whiteSpace: "nowrap" }}>{t("multiple_choice_question")}</div>
                        {/* <div style={{ fontSize: "12px", color: "#95aac9" }}>(A, B, C, D)</div> */}
                      </div>
                    </div>

                    <div
                      className={`question-type-radio ${questionType === QUESTION_TYPE.TRUE_FALSE ? "selected" : ""}`}
                      onClick={() => !formDisabled && setValue("questionType", QUESTION_TYPE.TRUE_FALSE)}
                      style={{
                        padding: "12px 15px",
                        border: `1px solid ${questionType === QUESTION_TYPE.TRUE_FALSE ? "#f0f7ff" : "rgba(51, 51, 51, 0.06)"}`,
                        borderRadius: "14px",
                        cursor: "pointer",
                        display: "flex",
                        alignItems: "center",
                        backgroundColor: questionType === QUESTION_TYPE.TRUE_FALSE ? "#f0f7ff" : "white",
                        transition: "all 0.2s ease",
                        flex: 1,
                      }}
                    >
                      <div
                        style={{
                          width: "24px",
                          minWidth: "24px",
                          height: "24px",
                          borderRadius: "50%",
                          border: `1px solid ${questionType === QUESTION_TYPE.TRUE_FALSE ? "#436EB6" : "rgba(51, 51, 51, 0.6)"}`,
                          marginRight: "12px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        {questionType === QUESTION_TYPE.TRUE_FALSE && (
                          <div
                            style={{
                              width: "12px",
                              height: "12px",
                              borderRadius: "50%",
                              backgroundColor: "#436EB6",
                            }}
                          />
                        )}
                      </div>
                      <div>
                        <div style={{ fontWeight: 500, color: "#333", fontSize: "15px" }}>{t("true_false")}</div>
                        {/* <div style={{ fontSize: "12px", color: "#95aac9" }}>True / False</div> */}
                      </div>
                    </div>
                  </div>
                  {errors.questionType?.message && <InputWrapper.Error message={errors.questionType?.message} />}
                </InputWrapper>
              </div>

              {/* Options */}
              <div className="mb-4">
                <div className="d-flex justify-content-between align-items-center mb-2">
                  <InputWrapper className="mb-0">
                    <InputWrapper.Label>
                      {t("options")} <span className="text-danger">*</span> <InfoIcon tooltip={t("options_tooltip")} place="right" />
                    </InputWrapper.Label>
                  </InputWrapper>
                </div>
                {errors.correctAnswer && (
                  <div
                    className="mb-3 p-2"
                    style={{
                      background: "#fff0f0",
                      border: "1px solid #ffcece",
                      borderRadius: "4px",
                      display: "flex",
                      alignItems: "center",
                      color: "#e53e3e",
                      fontSize: "14px",
                    }}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      fill="currentColor"
                      className="bi bi-exclamation-circle me-2"
                      viewBox="0 0 16 16"
                    >
                      <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
                      <path d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z" />
                    </svg>
                    {t("correct_answer_is_required")}
                  </div>
                )}
                {fields.map((field, index) => {
                  const optionId = getOptionId(field, index);
                  return (
                    <div key={field.id} className="mb-4">
                      <div
                        className="option-wrapper"
                        style={{
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <div
                          style={{
                            marginRight: "10px",
                            cursor: "pointer",
                            position: "relative",
                          }}
                          onClick={() => !formDisabled && setValue("correctAnswer", optionId)}
                        >
                          <div
                            style={{
                              width: "24px",
                              minWidth: "24px",
                              height: "24px",
                              border: `1px solid ${optionId === watch("correctAnswer") ? "#436EB6" : "rgba(51, 51, 51, 0.6)"}`,
                              borderRadius: "50%",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              position: "relative",
                            }}
                          >
                            {optionId === watch("correctAnswer") && (
                              <div
                                style={{
                                  width: "12px",
                                  height: "12px",
                                  backgroundColor: "#436EB6",
                                  borderRadius: "50%",
                                }}
                              ></div>
                            )}
                          </div>
                        </div>
                        <div style={{ flexGrow: 1 }}>
                          <InputWrapper className="mb-0">
                            <Textbox
                              name={`options.${index}.text`}
                              control={control}
                              placeholder={`Enter option ${optionId}`}
                              disabled={questionType === "true_false" || formDisabled}
                              className="form-control"
                            />
                            {/* Only show validation errors for MCQ options, not for True/False */}
                            {questionType !== "true_false" && errors.options?.[index]?.text?.message && (
                              <InputWrapper.Error message={errors.options?.[index]?.text?.message} />
                            )}
                          </InputWrapper>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Correct Answer - Hidden but functional */}
              <div style={{ display: "none" }}>
                <InputWrapper>
                  <InputWrapper.Label required>{t("correct_answer")}</InputWrapper.Label>
                  <Select
                    name="correctAnswer"
                    control={control}
                    options={fields.map((option, index) => ({
                      value: getOptionId(option, index),
                      label: `${getOptionId(option, index)}: ${option.text || ""}`,
                    }))}
                    placeholder={t("select_correct_answer")}
                    className="form-control"
                  />
                </InputWrapper>
              </div>

              {submitError && <div className="alert alert-danger mb-3">{submitError}</div>}

              <div className="d-flex justify-content-between gap-4 mt-5">
                <Button type="submit" className="primary-btn rounded-md w-100" disabled={isSubmitting || formDisabled}>
                  <div className="d-flex align-items-center justify-content-center">
                    {isSubmitting && <Loader />}
                    <span className={isSubmitting ? "ms-2" : ""}>{isSubmitting ? t("adding_question") : t("add_question")}</span>
                  </div>
                </Button>
                <Button type="button" className="dark-outline-btn rounded-md w-100" onClick={onClickCancel} disabled={formDisabled}>
                  {t("cancel")}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};
