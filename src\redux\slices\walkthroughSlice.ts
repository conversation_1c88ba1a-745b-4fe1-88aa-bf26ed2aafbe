import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface IWalkthroughState {
  visitedWalkthroughs: string[];
}

const initialState: IWalkthroughState = {
  visitedWalkthroughs: [],
};

export const walkthroughSlice = createSlice({
  name: "walkthrough",
  initialState,
  reducers: {
    // set walkthrough status
    setWalkthroughStatus: (state, action: PayloadAction<string[]>) => {
      state.visitedWalkthroughs = action.payload;
    },

    // update walkthrough status
    updateWalkthrough: (state, action: PayloadAction<string>) => {
      console.log("action.payload", action.payload);
      // Check if the walkthrough name already exists in the array
      if (!state.visitedWalkthroughs.includes(action.payload)) {
        // Only append if it doesn't exist
        state.visitedWalkthroughs = [...state.visitedWalkthroughs, action.payload];
      }
    },

    // reset walkthrough status (only in Redux, not in DB)
    resetWalkthrough: (state) => {
      state.visitedWalkthroughs = [];
    },
  },
});

export const { setWalkthroughStatus, updateWalkthrough, resetWalkthrough } = walkthroughSlice.actions;

export default walkthroughSlice.reducer;
