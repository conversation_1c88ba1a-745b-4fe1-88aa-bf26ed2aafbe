import { FIRST_LAST_NAME_REGEX } from "@/constants/commonConstants";
import * as yup from "yup";

export const updateInterviewOrderValidationSchema = (translations: (key: string) => string) => {
  return yup.object().shape({
    interviewOrder: yup
      .number()
      .transform((value) => (isNaN(value) ? undefined : value))
      .required(translations("interview_order_required"))
      .min(1, translations("interview_must_be_select")),
  });
};

export const profileValidationSchema = (translations: (key: string) => string) => {
  return yup.object().shape({
    firstName: yup
      .string()
      .trim()
      .required(translations("first_name_required"))
      .min(1, translations("min_first_name"))
      .max(25, translations("first_name_max"))
      .matches(FIRST_LAST_NAME_REGEX, translations("first_last_regex")),
    lastName: yup
      .string()
      .trim()
      .required(translations("last_name_required"))
      .min(1, translations("min_last_name"))
      .max(25, translations("last_name_max"))
      .matches(FIRST_LAST_NAME_REGEX, translations("first_last_regex")),
  });
};
