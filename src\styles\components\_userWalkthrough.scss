/* User Walkthrough Styles */
.walkthrough-button {
  padding: 6px 12px;
  border-radius: 4px;
  background-color: #546af7;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #3a4db8;
  }
}

.walkthrough-button {
  margin-left: 10px;
}

/* React Joyride Overrides */
/* No animation initially as we'll apply them in JS */
.react-joyride__tooltip {
  border-radius: 8px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
  animation: none !important;
  transition: none !important;
}

/* Keep the beacon animation */
.react-joyride__beacon {
  animation: beacon-pulse 1.2s infinite;
}

/* Better backdrop */
.react-joyride__overlay {
  transition: none !important;
}

/* Fix spotlight animations */
.react-joyride__spotlight {
  transition: none !important;
}

/* Smooth button hover transitions */
.react-joyride__button {
  transition:
    background-color 0.2s ease,
    color 0.2s ease !important;
}

/* Beacon pulse animation */
@keyframes beacon-pulse {
  0% {
    transform: scale(1);
    opacity: 0.9;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: scale(1.8);
    opacity: 0;
  }
}

/* Override popper.js animations with our own */
/* Remove all animations */
.react-joyride .react-floater__floater {
  transform: none !important;
  transition: none !important;
}

/* Force fixed positioning to stop automatic animations */
.react-joyride .react-floater__container {
  position: fixed !important;
}

/* Ensure floater applies transitions */
.react-joyride .react-floater__floater:not([data-floater-on-first-render]) {
  transition: none !important;
}

/* This forces immediate redraw and prevents default animations */
.react-joyride .react-floater__floater[data-react-floater-position] {
  animation: none !important;
}

/* Remove all tooltip animations */
.react-joyride__tooltip--entering,
.react-joyride__tooltip--updating {
  animation: none !important;
  transition: none !important;
}

/* Direct appear animation (for first step) */
@keyframes direct-appear {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Helper class for Floater to force immediate position */
.force-position {
  transition: none !important;
}

@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-down {
  0% {
    opacity: 0;
    transform: translateY(-30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-left {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-right {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Customize tooltip buttons */
.react-joyride__button {
  &.react-joyride__back {
    color: #546af7 !important;
  }

  &.react-joyride__next {
    background-color: #546af7 !important;

    &:hover {
      background-color: #3a4db8 !important;
    }
  }

  &.react-joyride__skip {
    color: #666 !important;
  }
}
