import React from "react";

function SecondaryChatIcon({ className }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path
        d="M15.0224 10.5637C15.0224 10.1909 14.7202 9.88867 14.3474 9.88867H6.92244C6.54969 9.88867 6.24744 10.1909 6.24744 10.5637C6.24744 10.9364 6.54969 11.2387 6.92244 11.2387H14.3474C14.7202 11.2387 15.0224 10.9364 15.0224 10.5637Z"
        fill="#CB9932"
      />
      <path
        d="M6.92097 13.2969C6.54822 13.2969 6.24597 13.5991 6.24597 13.9719C6.24597 14.3446 6.54822 14.6469 6.92097 14.6469H10.8577C11.2305 14.6469 11.5327 14.3446 11.5327 13.9719C11.5327 13.5991 11.2305 13.2969 10.8577 13.2969H6.92097Z"
        fill="#CB9932"
      />
      <path
        d="M19.4699 10.6008C19.0972 10.6008 18.795 10.9031 18.795 11.2758V16.7351C18.795 17.7566 17.964 18.5876 16.9425 18.5876H12.4275C12.2812 18.5876 12.1387 18.6348 12.0217 18.7233L8.1232 21.6551C8.0242 21.7293 7.9312 21.6986 7.8847 21.6731C7.83745 21.6491 7.75945 21.5898 7.76245 21.4691L7.83745 19.2866C7.84345 19.1036 7.7752 18.9258 7.64845 18.7946C7.5217 18.6626 7.3462 18.5883 7.1632 18.5883H4.02745C3.00595 18.5883 2.17495 17.7581 2.17495 16.7358V7.81081C2.17495 6.78556 3.00595 5.95156 4.02745 5.95156H12.3449C12.7177 5.95156 13.02 5.64931 13.02 5.27656C13.02 4.90381 12.7177 4.60156 12.3449 4.60156H4.02745C2.26195 4.60156 0.824951 6.04156 0.824951 7.81156V16.7358C0.824951 18.5013 2.26195 19.9391 4.02745 19.9391H6.4642L6.4132 21.4256C6.3952 22.0361 6.71995 22.5911 7.2622 22.8723C7.49395 22.9931 7.7437 23.0523 7.9912 23.0523C8.32345 23.0523 8.6527 22.9451 8.9332 22.7358L12.6525 19.9391H16.9417C18.7072 19.9391 20.1442 18.5021 20.1442 16.7358V11.2766C20.145 10.9031 19.8427 10.6008 19.4699 10.6008Z"
        fill="#CB9932"
      />
      <path
        d="M23.1188 4.17101C22.9965 3.79601 22.6958 3.51551 22.314 3.42101L20.589 2.99126L19.647 1.48376C19.2293 0.813258 18.1388 0.814758 17.7218 1.48376L16.7798 2.99126L15.0548 3.42101C14.673 3.51551 14.3715 3.79601 14.25 4.17101C14.1285 4.54601 14.2065 4.95026 14.46 5.25251L15.603 6.61301L15.4785 8.38601C15.4508 8.77901 15.6248 9.15176 15.9435 9.38351C16.1415 9.52751 16.374 9.60176 16.6095 9.60176C16.7528 9.60176 16.8975 9.57401 17.0363 9.51776L18.6848 8.85176L20.3325 9.51776C20.6978 9.66626 21.1058 9.61526 21.4253 9.38426C21.744 9.15251 21.918 8.77976 21.891 8.38601L21.7665 6.61301L22.9095 5.25176C23.1623 4.95026 23.241 4.54601 23.1188 4.17101ZM20.5568 5.95376C20.4443 6.08801 20.388 6.26051 20.4008 6.43601L20.5208 8.13851L18.9383 7.49876C18.8573 7.46576 18.771 7.44926 18.6855 7.44926C18.6 7.44926 18.5138 7.46576 18.4328 7.49876L16.851 8.13851L16.9703 6.43601C16.9823 6.26126 16.926 6.08801 16.8143 5.95376L15.717 4.64726L17.373 4.23401C17.5425 4.19126 17.6895 4.08551 17.7818 3.93701L18.6863 2.49026L19.5908 3.93701C19.6838 4.08551 19.83 4.19201 19.9995 4.23401L21.6555 4.64726L20.5568 5.95376Z"
        fill="#CB9932"
      />
    </svg>
  );
}

export default SecondaryChatIcon;
