"use client";
import Button from "@/components/formElements/Button";
import Svg404 from "@/components/svgComponents/Svg404";
import Link from "next/link";
import ROUTES from "@/constants/routes";
import Image from "next/image";
import Logo from "../../../../public/assets/images/logo.svg";
import { useAuth } from "@/hooks/useAuth";

const PageNotFound = () => {
  const { isAuthenticated } = useAuth();
  return (
    <>
      <div className="logo-header text-center">
        <Link className="navbar-brand" href={isAuthenticated ? ROUTES.DASHBOARD : ROUTES.HOME}>
          <Image src={Logo} alt="logo" width={640} height={320} />
        </Link>
      </div>
      <section className="static-page">
        <div className="container">
          <div className="error-page-container text-center py-5">
            <div className="svg-container mb-4">
              <Svg404 />
            </div>
            <h1 className="color-primary mb-3">Page Not Found</h1>
            <p className="mb-5">
              Oops! The page you are looking for might have been removed, <br /> had its name changed, or is temporarily unavailable.
            </p>
            <Button className="primary-outline-btn rounded-md m-auto">Return to Home</Button>
          </div>
        </div>
      </section>
    </>
  );
};

export default PageNotFound;
