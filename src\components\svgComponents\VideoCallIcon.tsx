const VideoCallIcon = (props: { className?: string; isVideoMute?: boolean }) => {
  const { className, isVideoMute } = props;
  return (
    <div className={className}>
      {isVideoMute ? (
        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28" fill="none">
          <path
            d="M21.4697 2.46967C21.7626 2.17678 22.2374 2.17678 22.5303 2.46967C22.8232 2.76256 22.8232 3.23732 22.5303 3.53022L1.53027 24.5302C1.23737 24.8229 0.762563 24.8231 0.469727 24.5302C0.176891 24.2374 0.177006 23.7626 0.469727 23.4697L1.85449 22.0839C1.3278 21.6371 0.889511 21.0902 0.572266 20.4677C0.252114 19.8393 0.121104 19.1639 0.0595706 18.4111C8.21795e-05 17.6826 -1.23469e-05 16.7848 3.11838e-07 15.6835V12.3183C-1.23704e-05 11.217 7.84809e-05 10.3192 0.0595706 9.59076C0.121098 8.83789 0.252088 8.16261 0.572266 7.53412C1.07556 6.5465 1.87859 5.74347 2.86621 5.24018C3.49476 4.91991 4.16989 4.78902 4.92285 4.72748C5.65135 4.66797 6.54899 4.6679 7.65039 4.66791H13.3496C14.451 4.6679 15.3487 4.66796 16.0771 4.72748C16.83 4.78902 17.5053 4.91995 18.1338 5.24018C18.2573 5.30313 18.3777 5.37108 18.4951 5.4433L21.4697 2.46967ZM26.1553 5.98529C26.9273 5.43402 27.9997 5.98591 28 6.93451V21.0673C27.9997 22.0159 26.9273 22.5676 26.1553 22.0165L20.9492 18.2978C20.9465 18.3357 20.9435 18.374 20.9404 18.4111C20.8789 19.1639 20.7479 19.8393 20.4277 20.4677C19.9244 21.4554 19.1215 22.2593 18.1338 22.7626C17.5053 23.0829 16.8301 23.2138 16.0771 23.2753C15.3487 23.3349 14.451 23.3349 13.3496 23.3349H7.65039C6.8324 23.3349 6.12682 23.3339 5.51953 23.3095L7.8291 21.0009H13.2998C14.4623 21.0009 15.2644 21 15.8867 20.9492C16.4954 20.8994 16.8294 20.8083 17.0742 20.6835C17.6228 20.404 18.069 19.9576 18.3486 19.4091C18.4734 19.1643 18.5655 18.8301 18.6152 18.2216C18.6661 17.5993 18.667 16.7971 18.667 15.6347V12.3681C18.667 11.4453 18.6643 10.7493 18.6387 10.1904L20.6787 8.15033C20.823 8.60091 20.8985 9.0777 20.9404 9.59076C20.9435 9.62788 20.9465 9.66605 20.9492 9.70404L26.1553 5.98529ZM7.7002 7.00092C6.53775 7.00092 5.7356 7.00186 5.11328 7.05268C4.50453 7.10242 4.17064 7.19452 3.92578 7.31928C3.37707 7.59889 2.93098 8.04498 2.65137 8.59369C2.52667 8.83853 2.43449 9.17263 2.38477 9.78119C2.33396 10.4035 2.33301 11.2058 2.33301 12.3681V15.6347C2.33301 16.7971 2.33392 17.5993 2.38477 18.2216C2.43451 18.8301 2.52664 19.1643 2.65137 19.4091C2.85685 19.8121 3.15286 20.1588 3.5127 20.4257L16.7441 7.19428C16.5339 7.13334 16.2634 7.08346 15.8867 7.05268C15.2644 7.00185 14.4622 7.00092 13.2998 7.00092H7.7002Z"
            fill="white"
          />
        </svg>
      ) : (
        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28" fill="none">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M13.3492 4.66602H7.65083H7.65081C6.54913 4.666 5.65169 4.66599 4.92307 4.72552C4.17002 4.78705 3.49517 4.91794 2.86655 5.23823C1.8787 5.74157 1.07555 6.54472 0.572217 7.53257C0.251921 8.16118 0.121035 8.83604 0.0595077 9.58909C-2.26523e-05 10.3177 -1.23186e-05 11.2151 3.68922e-07 12.3168V12.3168V15.6819V15.6819C-1.23186e-05 16.7836 -2.26523e-05 17.681 0.0595077 18.4096C0.121035 19.1627 0.251921 19.8375 0.572217 20.4661C1.07555 21.454 1.8787 22.2571 2.86655 22.7605C3.49517 23.0808 4.17002 23.2117 4.92307 23.2732C5.65167 23.3327 6.54908 23.3327 7.65072 23.3327H7.65086H13.3491H13.3493C14.4509 23.3327 15.3483 23.3327 16.0769 23.2732C16.83 23.2117 17.5048 23.0808 18.1335 22.7605C19.1213 22.2571 19.9244 21.454 20.4278 20.4661C20.7481 19.8375 20.879 19.1627 20.9405 18.4096C20.9435 18.3723 20.9464 18.3346 20.9492 18.2964L26.1552 22.015C26.9274 22.5665 28 22.0145 28 21.0656V6.93305C28 5.98411 26.9274 5.43213 26.1552 5.98369L20.9492 9.7023C20.9464 9.66412 20.9435 9.62638 20.9405 9.58909C20.879 8.83604 20.7481 8.16118 20.4278 7.53257C19.9244 6.54472 19.1213 5.74157 18.1335 5.23823C17.5048 4.91794 16.83 4.78705 16.0769 4.72552C15.3483 4.66599 14.4509 4.666 13.3492 4.66602H13.3492ZM3.92586 7.31725C4.17072 7.19249 4.50432 7.10084 5.11308 7.05111C5.73546 7.00026 6.53731 6.99935 7.7 6.99935H13.3C14.4627 6.99935 15.2645 7.00026 15.8869 7.05111C16.4957 7.10084 16.8293 7.19249 17.0741 7.31725C17.6229 7.59688 18.0691 8.04307 18.3488 8.59188C18.4735 8.83673 18.5652 9.17034 18.6149 9.7791C18.6658 10.4015 18.6667 11.2033 18.6667 12.366V15.6327C18.6667 16.7954 18.6658 17.5972 18.6149 18.2196C18.5652 18.8284 18.4735 19.162 18.3488 19.4068C18.0691 19.9556 17.6229 20.4018 17.0741 20.6815C16.8293 20.8062 16.4957 20.8979 15.8869 20.9476C15.2645 20.9984 14.4627 20.9994 13.3 20.9994H7.7C6.53731 20.9994 5.73546 20.9984 5.11308 20.9476C4.50432 20.8979 4.17072 20.8062 3.92586 20.6815C3.37706 20.4018 2.93086 19.9556 2.65123 19.4068C2.52647 19.162 2.43483 18.8284 2.38509 18.2196C2.33424 17.5972 2.33333 16.7954 2.33333 15.6327V12.366C2.33333 11.2033 2.33424 10.4015 2.38509 9.7791C2.43483 9.17034 2.52647 8.83673 2.65123 8.59188C2.93086 8.04307 3.37706 7.59688 3.92586 7.31725Z"
            fill="white"
          />
        </svg>
      )}
    </div>
  );
};

export default VideoCallIcon;
