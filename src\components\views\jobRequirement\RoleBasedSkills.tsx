"use client";

// Internal libraries
import { useEffect } from "react";

import { redirect, useRouter } from "next/navigation";
import { useSelector } from "react-redux";

// Components
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
import EditSkillIcon from "@/components/svgComponents/EditSkillIcon";
import Button from "@/components/formElements/Button";

// Redux, constants, interfaces
import { selectJobDetails } from "@/redux/slices/jobDetailsSlice";
import { selectRoleSpecificSkills } from "@/redux/slices/jobSkillsSlice";
import { ISkillData } from "@/interfaces/jobRequirementesInterfaces";
import ROUTES from "@/constants/routes";

// CSS
import style from "@/styles/commonPage.module.scss";
import { SKILL_TYPE } from "@/constants/jobRequirementConstant";
import NoDataFoundIcon from "@/components/svgComponents/NoDataFoundIcon";
import { useTranslations } from "next-intl";

function RoleBasedSkills() {
  const router = useRouter();
  const roleSpecificSkills = useSelector(selectRoleSpecificSkills) || [];
  const jobDetails = useSelector(selectJobDetails);
  const t = useTranslations();
  const tCommon = useTranslations("jobRequirement");
  /**
   *
   *
   * @effect
   * @description Checks if the skill type is valid and if necessary Redux data is available
   *              Redirects to the career-based skills page if any validation fails
   *              Runs only once on component mount due to empty dependency array
   */

  useEffect(() => {
    if (!roleSpecificSkills || roleSpecificSkills.length === 0) redirect(`${ROUTES.JOBS.GENERATE_JOB}`);
  }, []);

  /**
   *
   *
   * @effect
   * @description Checks if the skill type is valid and if necessary Redux data is available
   *              Redirects to the career-based skills page if any validation fails
   *              Runs only once on component mount due to empty dependency array
   */

  useEffect(() => {
    if (!roleSpecificSkills || roleSpecificSkills.length === 0) redirect(`${ROUTES.JOBS.GENERATE_JOB}`);
  }, []);

  return (
    <div className={style.job_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                <BackArrowIcon
                  onClick={() => {
                    router.push(`${ROUTES.JOBS.CAREER_BASED_SKILLS}`);
                  }}
                />
                {tCommon("top_performance_based_skills")} <span>{jobDetails?.title || ""}</span>
              </h2>
            </div>
            <p className="description">
              Based on the job details you provided and the finalized description, we’ve identified the <b>Performance-based Skills</b> most critical
              to success. These Performance-based skills are essential for driving high performance and ensuring alignment with your team and culture.
              <strong className="color-primary"> You can edit or adjust the selected skills at any time.</strong>
            </p>
          </div>
        </div>
        <div className="inner-section role-based-skills">
          <div className="d-flex justify-content-between align-items-center mb-4">
            <h3 className={style.inner_heading}>
              {t("top")} <span> {t("role_specific_performance_based_skills")} </span>
            </h3>
            <Button
              className="clear-btn p-0 primary"
              onClick={() => {
                router.push(`${ROUTES.JOBS.PERFORMANCE_BASED_SKILLS}?skillType=${SKILL_TYPE.ROLE}`);
              }}
            >
              <EditSkillIcon className="me-3" />
              {t("edit_skills")}
            </Button>
          </div>
          <div className="row g-4">
            {roleSpecificSkills.length > 0 ? (
              roleSpecificSkills.map((skill: ISkillData, index: number) => (
                <div className="col-md-4" key={index}>
                  <div className="career-skill-card">
                    <div className="head">
                      <h3>{skill.name || `Skill ${index + 1}`}</h3>
                    </div>
                    <div className="skill-content">
                      <p>{skill.description || t("no_desc_available")}</p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-12">
                <NoDataFoundIcon width={300} height={300} />
                <p>{t("no_role_specific_skills_found")}</p>
              </div>
            )}
          </div>
        </div>

        <div className="button-align py-5">
          <Button
            className="primary-btn rounded-md"
            onClick={() => {
              router.push(`${ROUTES.JOBS.CULTURE_BASED_SKILLS}`);
            }}
          >
            {t("save_and_next")}
          </Button>
        </div>
      </div>
    </div>
  );
}

export default RoleBasedSkills;
