import React from "react";

function RightGreenIcon({ className }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} width="20" height="20" viewBox="0 0 32 32" fill="none">
      <g clipPath="url(#clip0_9922_1550)">
        <path
          d="M4.16016 20.4807L5.12016 11.8407L10.2402 10.5607L18.2402 6.7207L26.5602 7.0407L26.8802 16.6407L15.3602 24.6407L4.16016 20.4807Z"
          fill="white"
        />
        <path
          d="M16 0C7.17794 0 0 7.17794 0 16C0 24.8221 7.17794 32 16 32C24.8221 32 32 24.8221 32 16C32 7.17794 24.8221 0 16 0ZM24.9424 11.7895L14.7168 21.9348C14.1153 22.5363 13.1529 22.5764 12.5113 21.9749L7.09774 17.0426C6.45614 16.4411 6.41604 15.4386 6.97744 14.797C7.57895 14.1554 8.58145 14.1153 9.22306 14.7168L13.5138 18.6466L22.6566 9.50376C23.2982 8.86215 24.3008 8.86215 24.9424 9.50376C25.584 10.1454 25.584 11.1479 24.9424 11.7895Z"
          fill="#1A9222"
        />
      </g>
      <defs>
        <clipPath id="clip0_9922_1550">
          <rect width="32" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default RightGreenIcon;
