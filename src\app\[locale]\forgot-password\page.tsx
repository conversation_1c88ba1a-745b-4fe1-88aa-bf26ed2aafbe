"use client";
import React, { useState } from "react";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import Image from "next/image";
import { useRouter } from "next/navigation";

import logo from "../../../../public/assets/images/logo.svg";
import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import styles from "@/styles/auth.module.scss";
import { forgotPasswordValidation } from "@/validations/authValidations";
import { forgotPassword } from "@/services/authServices";
import { IForgotPassword } from "@/interfaces/authInterfaces";
import routes from "@/constants/routes";
import { toastMessageSuccess, toastMessageError, encryptInfo } from "@/utils/helper";
import { OTP_TYPE } from "@/constants/commonConstants";
import { useTranslate } from "@/utils/translationUtils";
import Link from "next/link";
import ROUTES from "@/constants/routes";

const ForgotPassword = () => {
  const [loading, setLoading] = useState(false);
  const translate = useTranslate();
  const router = useRouter();
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(forgotPasswordValidation(translate)),
    mode: "onChange",
  });

  const onSubmit = async (data: IForgotPassword) => {
    try {
      setLoading(true);
      const result = await forgotPassword(data);

      if (result?.data?.success) {
        toastMessageSuccess(translate(result?.data?.message));
        const info = encryptInfo(
          JSON.stringify({
            ...data,
            type: OTP_TYPE.FORGOT_PASSWORD,
          })
        );
        const encodedInfo = encodeURIComponent(info);
        router.push(`${routes.VERIFY}?info=${encodedInfo}`);
        setLoading(false);
      } else {
        setLoading(false);
        toastMessageError(translate(result?.data?.message) ?? "something_went_wrong");
      }
    } catch (error) {
      console.error(error);
      toastMessageError(translate("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.auth_main}>
      <div className="container">
        <div className="row">
          <div className={styles.user_auth_main}>
            <div className="container">
              <div className="row row-center">
                <div className={`${styles.hero_image} col-md-6`}>
                  {/* <div className={styles.client_signature_box}>
                    <p>
                      The challenge is great, the effort is extraordinary, the achievement is life changing, and the impact will become your legacy.
                      Where are you now and what are you willing to change to get to where you want to be?
                    </p>
                    <Image src={clientSignature} alt="client" />
                  </div> */}
                </div>
                <div className="col-md-6">
                  <div className={styles.form_main}>
                    <div className="text-center">
                      <Image src={logo} alt="logo" className={styles.logo} width={200} height={80} onClick={() => router.push(ROUTES.HOME)} />
                      <h1>
                        {translate("forgot")}
                        <span> {translate("password")}</span>
                      </h1>
                    </div>
                    <form onSubmit={handleSubmit(onSubmit)}>
                      <InputWrapper>
                        <InputWrapper.Label htmlFor="email" required>
                          {translate("email")}
                        </InputWrapper.Label>
                        <Textbox className="form-control" control={control} name="email" type="email" placeholder={translate("enter_your_email")} />
                        <InputWrapper.Error message={errors?.email?.message || ""} />
                      </InputWrapper>
                      <Button disabled={loading} loading={loading} className="primary-btn rounded-md w-100 mt-5">
                        {translate("send_verification_code")}
                      </Button>
                    </form>
                    <p className="text-center mt-4">
                      {translate("back_to")}{" "}
                      <Link href={routes.LOGIN} className={styles.signup_link}>
                        {translate("login")}
                      </Link>
                    </p>
                    {/* <p className={styles.bottom_link}>
                      {translate("back_to")}{" "}
                      <a
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          router.push(routes.LOGIN);
                        }}
                        className={styles.login_link}
                      >
                        {translate("login")}
                      </a>
                    </p> */}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
