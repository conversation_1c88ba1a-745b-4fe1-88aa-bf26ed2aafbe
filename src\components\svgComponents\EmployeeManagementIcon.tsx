"use client";
import React from "react";

const EmployeeManagementIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      style={{ minWidth: "20px" }}
      width="20"
      height="20"
      id="Layer_1"
      viewBox="0 0 512 512"
      data-name="Layer 1"
    >
      <path d="m491.181 367.517c-.803 1.939-2.082 3.646-3.718 4.96-7.396 5.943-23.179 14.709-35.54 20.76-.106.052-.213.103-.32.151l-161.875 73.625c-27.64 12.427-47.71 12.013-63.835 11.684-3.166-.065-6.157-.125-8.992-.083-6.33.098-12.055-.025-17.592-.146-26.562-.575-44.114-.953-78.805 27.231-2.317 1.882-5.105 2.799-7.875 2.799-3.635 0-7.238-1.577-9.709-4.618-4.353-5.358-3.539-13.231 1.819-17.584 41.816-33.973 66.512-33.438 95.109-32.822 5.31.115 10.799.234 16.666.143 3.287-.05 6.495.016 9.893.085 15.026.31 30.566.628 53.02-9.468l161.664-73.529c10.754-5.272 21.866-11.54 27.952-15.698.547-2.366.594-5.116-1.231-6.975-3.802-3.872-18.242-7.278-48.243 4.517-31.946 12.559-74.343 26.34-98.621 32.056-.07.016-.139.023-.209.038-1.226 1.811-2.617 3.538-4.191 5.16-20.602 21.24-65.264 21.16-105.511 19.42-3.906-.169-7.28-.315-9.934-.372-6.902-.146-12.378-5.861-12.231-12.763.146-6.902 5.858-12.404 12.763-12.231 2.929.062 6.43.213 10.483.389 21.537.931 71.973 3.113 86.486-11.85 2.658-2.74 3.7-5.898 3.38-10.241-.549-7.426-8.471-11.263-23.547-11.404-37.27-.349-70.54-3.121-112.094-7.483-52.693-3.541-95.592 43.946-126.936 78.622-2.467 2.73-5.866 4.118-9.277 4.118-2.988 0-5.986-1.065-8.378-3.227-5.122-4.629-5.521-12.534-.891-17.655 19.78-21.883 39.447-42.893 62.529-59.403 28.537-20.413 56.311-29.37 84.9-27.38.146.01.292.023.438.038 40.929 4.299 73.597 7.031 109.943 7.371 37.698.353 46.145 20.333 47.903 31.611 23.775-6.478 57.421-17.692 83.85-28.081 46.461-18.266 67.074-7.067 75.226 1.233 9.187 9.354 11.255 23.187 5.533 37zm-385.432-70.062c1.953-21.677 11.885-41.753 27.966-56.53 6.8-6.249 14.463-11.3 22.685-15.078-7.292-8.453-11.717-19.445-11.717-31.457 0-26.596 21.637-48.233 48.233-48.233s48.233 21.637 48.233 48.233c0 12.013-4.425 23.005-11.717 31.457 8.222 3.777 15.885 8.829 22.685 15.078 16.081 14.776 26.013 34.853 27.966 56.53.619 6.875-4.452 12.952-11.328 13.571-.381.034-.761.051-1.136.051-6.397 0-11.85-4.884-12.435-11.379-2.919-32.401-29.688-56.834-62.269-56.834s-59.349 24.433-62.268 56.834c-.62 6.876-6.706 11.941-13.571 11.328-6.875-.62-11.947-6.696-11.328-13.571zm63.935-103.065c0 12.811 10.422 23.232 23.233 23.232s23.233-10.422 23.233-23.232-10.422-23.233-23.233-23.233-23.233 10.422-23.233 23.233zm204.759-48.233c26.596 0 48.233 21.637 48.233 48.233 0 12.013-4.425 23.005-11.716 31.457 8.222 3.777 15.885 8.829 22.685 15.078 16.082 14.776 26.013 34.853 27.966 56.53.619 6.875-4.453 12.952-11.328 13.571-6.867.619-12.952-4.453-13.571-11.328-2.919-32.401-29.688-56.834-62.269-56.834s-59.349 24.434-62.269 56.834c-.585 6.494-6.039 11.379-12.435 11.379-.376 0-.754-.017-1.136-.051-6.876-.62-11.947-6.696-11.328-13.571 1.954-21.677 11.885-41.753 27.966-56.53 6.8-6.249 14.463-11.301 22.685-15.078-7.292-8.453-11.716-19.445-11.716-31.457 0-26.596 21.637-48.233 48.233-48.233zm-23.233 48.233c0 12.811 10.422 23.232 23.233 23.232s23.233-10.422 23.233-23.232-10.422-23.233-23.233-23.233-23.233 10.422-23.233 23.233zm-146.485-69.457c8.956-18.679 24.228-33.263 42.493-41.68-7.324-8.461-11.771-19.478-11.771-31.52 0-26.596 21.637-48.233 48.233-48.233s48.232 21.637 48.232 48.233c0 12.043-4.447 23.06-11.771 31.52 18.265 8.417 33.538 23.001 42.494 41.68 2.984 6.225.358 13.691-5.867 16.676-1.743.835-3.583 1.231-5.396 1.231-4.66 0-9.131-2.617-11.28-7.099-10.35-21.586-32.493-35.535-56.413-35.535s-46.062 13.948-56.412 35.535c-2.984 6.225-10.451 8.851-16.676 5.867-6.225-2.985-8.852-10.451-5.867-16.676zm55.722-73.201c0 12.811 10.422 23.232 23.233 23.232s23.232-10.422 23.232-23.232-10.422-23.233-23.232-23.233-23.233 10.422-23.233 23.233z" />
    </svg>
  );
};

export default EmployeeManagementIcon;
