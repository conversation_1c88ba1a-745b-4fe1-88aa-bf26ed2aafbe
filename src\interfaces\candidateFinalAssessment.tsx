// Interfaces for assessment data
export interface Option {
  id: string;
  text: string;
}

export interface QuestionOptions {
  options: Option[];
}

export interface Question {
  id: number;
  question: string;
  questionType: "mcq" | "true_false";
  skillId: number;
  skillTitle: string;
  options: QuestionOptions;
  correctAnswer?: string; // Optional as candidates shouldn't see this
}

interface QuestionGroup {
  type: string;
  questions: Question[];
}

export interface IAssessmentData {
  assessmentId: number;
  jobApplicationId: number;
  questionGroups: QuestionGroup[];
}

export interface CandidateInfo {
  email: string;
}
