const ManageUsersIcon = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <mask id="mask0_17060_16145" style={{ maskType: "luminance" }} maskUnits="userSpaceOnUse" x="0" y="0" width="16" height="16">
        <path d="M0 9.53674e-07H16V16H0V9.53674e-07Z" fill="white" />
      </mask>
      <g mask="url(#mask0_17060_16145)">
        <path
          d="M8.05883 5.48526C8.05883 7.03429 6.82977 8.29004 5.31358 8.29004C3.79745 8.29004 2.56836 7.03429 2.56836 5.48526C2.56836 3.9362 3.79745 2.68045 5.31358 2.68045C6.82977 2.68045 8.05883 3.9362 8.05883 5.48526Z"
          stroke="#333333"
          strokeWidth="0.9375"
          strokeMiterlimit="10"
        />
        <path
          d="M1.22838 13.3047C0.760662 13.3047 0.400256 12.8739 0.483756 12.4037C0.899193 10.0638 2.9036 8.28928 5.31366 8.28928C7.72372 8.28928 9.7281 10.0638 10.1436 12.4037C10.227 12.8739 9.86666 13.3047 9.39891 13.3047H1.22838Z"
          stroke="#333333"
          strokeWidth="0.9375"
          strokeMiterlimit="10"
        />
        <path
          d="M13.9103 5.09441C13.9103 6.29353 12.9589 7.26562 11.7852 7.26562C10.6116 7.26562 9.66016 6.29353 9.66016 5.09441C9.66016 3.89531 10.6116 2.92322 11.7852 2.92322C12.9589 2.92322 13.9103 3.89531 13.9103 5.09441Z"
          stroke="#333333"
          strokeWidth="0.9375"
          strokeMiterlimit="10"
        />
        <path
          d="M8.39258 9.39878C9.01883 8.13353 10.3031 7.26603 11.7857 7.26603C13.6513 7.26603 15.203 8.63963 15.5245 10.451C15.5892 10.8149 15.3102 11.1484 14.9481 11.1484H9.74655"
          stroke="#333333"
          strokeWidth="0.9375"
          strokeMiterlimit="10"
        />
      </g>
    </svg>
  );
};

export default ManageUsersIcon;
