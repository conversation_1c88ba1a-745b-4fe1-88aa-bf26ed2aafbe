@use "./abstracts" as *;

.conduct_interview_page {
  padding-bottom: 40px;

  // Profile image fallback styles
  .profile_image_fallback {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100px;
    background-color: #f0f0f0;
    border-radius: 12px;
    font-size: 36px;
    font-weight: bold;
    color: #666;
    border: 2px solid #e0e0e0;
  }

  .question_info_box {
    ul {
      display: flex;
      gap: 4rem;
      align-items: center;
      @extend %listSpacing;
      margin-bottom: 30px;
      li {
        font-size: $text-sm;
        font-weight: $medium;
        color: $dark;
        display: flex;
        align-items: center;
        gap: 1rem;
        span {
          width: 20px;
          height: 20px;
          border-radius: 100%;
          display: block;
          &.current {
            background: $secondary;
          }
          &.completed {
            background: $primary;
          }
          &.additional {
            background: rgba($green, 1);
          }
        }
      }
      @media (max-width: 991px) {
        gap: 2rem;
        padding-left: 5px;
        li {
          span {
            width: 16px;
            height: 16px;
          }
        }
      }
    }
  }
}

.video_participants_grid {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 20px;
  background: #f1f2f4;
  padding: 20px;
  height: 100%;
  justify-content: flex-start;
  .video_participant_box {
    position: relative;
    border-radius: 9px;
    overflow: hidden;
    background-color: #000;
    // aspect-ratio: 16/9;
    aspect-ratio: 13/10;

    .participant_name {
      position: absolute;
      bottom: 5px;
      left: 5px;
      background-color: $primary;
      color: $white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 1rem;
      font-weight: $medium;
      z-index: 10;
      text-transform: capitalize;
    }

    .video_feed {
      width: 100%;
      height: 100%;
      object-fit: cover;

      // Hide picture-in-picture button in Firefox and other browsers
      :global(video) {
        &::-webkit-media-controls-picture-in-picture-button {
          display: none !important;
        }

        &::-moz-media-controls-picture-in-picture-button {
          display: none !important;
        }
      }
    }

    .empty_video {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background-color: rgba($primary, 0.1);

      p {
        color: rgba(255, 255, 255, 0.8);
        font-size: 12px;
        margin-bottom: 0;
      }
    }
  }
  .video_controls {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 20px;

    .control_btn {
      background-color: $primary;
      color: white;
      border: none;
      border-radius: 6px;
      padding: 10px 15px;
      font-size: 14px;
      font-weight: $medium;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 120px;

      &:hover {
        background-color: darken($primary, 10%);
      }

      &.off {
        background-color: #6c757d;

        &:hover {
          background-color: darken(#6c757d, 10%);
        }
      }
    }
  }
  @media (max-width: 991px) {
    background: transparent;
    padding: 0;
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
    .video_participant_box {
      aspect-ratio: 1.2;
      width: 47%;
      .participant_name {
        font-size: 0.9rem;
      }
      .empty_video {
        p {
          font-size: 9px;
        }
      }
    }
  }
}

// Candidate Confirmation page styles
.candidate_confirmation {
  padding: 0px 5vw;
  .candidate_interview_lottie {
    width: 100%;
    height: 100vh;
    object-fit: contain;
  }

  .confirmation {
    padding: 40px;
    border-radius: 30px;
    background: $white;
    box-shadow: 0px 4px 12px 0px rgba($dark, 0.09);
    text-align: center;
    &_logo {
      width: 16vw;
      height: 7vh;
      object-fit: contain;
    }
    &_title {
      font-size: 1.8vw;
      font-weight: $bold;
      margin-top: 1.5vw;
      color: $dark;
    }
    &_text {
      font-size: 0.8vw;
      margin-top: 1vw;
      line-height: 1.4;
      font-weight: $medium;
      color: rgba($dark, 0.8);
    }
    &_input_group {
      text-align: left;
      margin-top: 20px;
      &_icon {
        position: absolute;
        right: 10px;
        top: 50%;
        // transform: translateY(-50%);
        cursor: pointer;
        z-index: 1;
        padding: 5px 7px;
        margin-top: 1px;
        border-radius: 5px;
        svg {
          width: 15px;
          height: 15px;
        }
      }
      p {
        font-size: 1.4rem;
      }
    }
  }
  @media screen and (min-width: 768px) and (max-width: 991px) {
    padding: 0px 2vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100vh;
    .candidate_interview_lottie {
      display: none;
    }

    .confirmation {
      padding: 30px;

      &_logo {
        width: 25vw;
        height: auto;
        margin: 0 auto;
      }

      &_title {
        font-size: 3.5vw;
        margin-top: 3vw;
      }

      &_text {
        font-size: 2vw;
        margin-top: 2vw;
        line-height: 1.5;
      }
    }
  }

  @media screen and (max-width: 767px) {
    padding: 0px 2vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100vh;

    .candidate_interview_lottie {
      display: none;
    }
    .confirmation {
      padding: 20px;

      &_logo {
        width: 40vw;
        height: auto;
        margin: 0 auto;
      }

      &_title {
        font-size: 5vw;
        margin-top: 5vw;
      }

      &_text {
        font-size: 3.5vw;
        margin-top: 3vw;
        line-height: 1.6;
      }
    }
  }
}
