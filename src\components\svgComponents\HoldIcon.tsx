import React from "react";

type HoldIconProps = {
  className?: string;
  PrimaryColor?: boolean;
};

function HoldIcon({ className, PrimaryColor }: HoldIconProps) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" className={className}>
      <path
        d="M20.0746 0H14.0859C13.827 0 13.6172 0.209859 13.6172 0.46875C13.6172 0.727641 13.827 0.9375 14.0859 0.9375H20.0746C20.932 0.9375 21.6296 1.63505 21.6296 2.49239V21.5076C21.6296 22.365 20.932 23.0625 20.0746 23.0625H3.92505C3.06766 23.0625 2.37012 22.3649 2.37012 21.5075V2.49244C2.37012 1.63505 3.06766 0.9375 3.92505 0.9375H9.96129C10.2202 0.9375 10.43 0.727641 10.43 0.46875C10.43 0.209859 10.2202 0 9.96129 0H3.92505C2.55073 0 1.43262 1.11811 1.43262 2.49244V21.5076C1.43262 22.8819 2.55073 24 3.92505 24H20.0746C21.4489 24 22.5671 22.8819 22.5671 21.5076V2.49239C22.5671 1.11811 21.4489 0 20.0746 0Z"
        fill={PrimaryColor ? "#436EB6" : "#CB9932"}
      />
      <path
        d="M8.53423 4.54633V7.32878C8.07003 6.94197 7.49131 6.73145 6.88053 6.73145C6.62164 6.73145 6.41178 6.94131 6.41178 7.2002V10.7753C6.33068 13.8414 8.90553 16.5269 11.9797 16.528C15.0649 16.528 17.6343 13.8204 17.5475 10.7477V5.84359C17.5475 5.03495 16.8911 4.37702 16.0841 4.37702H16.0752C15.8895 4.37702 15.7118 4.41184 15.5483 4.47531C15.5211 3.66695 14.9015 3.06419 14.0858 3.06419C13.8903 3.06419 13.7036 3.10281 13.5329 3.1728C13.4017 2.47075 12.7994 1.95508 12.0548 1.95508C11.3246 1.95508 10.7143 2.47919 10.5789 3.17167C10.4058 3.10356 10.2146 3.06648 10.0113 3.06648C9.19686 3.06658 8.53423 3.73037 8.53423 4.54633ZM10.5508 4.54633C10.5508 4.57023 10.5507 9.1585 10.5507 9.1585C10.5507 9.41739 10.7606 9.62725 11.0195 9.62725C11.2784 9.62725 11.4882 9.41739 11.4882 9.1585L11.4883 3.46216C11.4883 3.14809 11.7424 2.89262 12.0547 2.89262C12.3834 2.89262 12.6219 3.13216 12.6219 3.46216L12.6211 9.01787C12.6211 9.27677 12.8309 9.48662 13.0898 9.48662C13.3487 9.48662 13.5586 9.27677 13.5586 9.01787C13.5586 9.01787 13.5594 4.5363 13.5594 4.53077C13.5594 4.23902 13.7955 4.00169 14.0858 4.00169C14.3954 4.00169 14.6117 4.21923 14.6117 4.53077L14.6116 9.1585C14.6116 9.41739 14.8214 9.62725 15.0803 9.62725C15.3392 9.62725 15.5491 9.41739 15.5491 9.1585C15.5491 9.1585 15.5492 5.86773 15.5492 5.84359C15.5492 5.55184 15.7851 5.31452 16.0752 5.31452H16.0841C16.3741 5.31452 16.61 5.55184 16.61 5.84359V10.7402C16.5528 13.2973 14.6555 15.5905 11.9798 15.5905C9.36546 15.5896 7.34928 13.3261 7.34928 10.7817V7.73627C7.61103 7.81352 7.85107 7.95569 8.04954 8.15462C8.30107 8.40686 8.53015 9.11251 8.53404 9.64248V10.7503C8.53404 10.9835 8.70317 11.181 8.93412 11.214C8.98432 11.2217 10.1696 11.4158 10.7034 12.5455C10.7835 12.7149 10.9519 12.8141 11.1275 12.8141C11.1946 12.8141 11.2628 12.7996 11.3275 12.769C11.5616 12.6585 11.6616 12.379 11.5511 12.145C11.0166 11.0139 10.0268 10.5572 9.47154 10.3838L9.47173 4.54637C9.47173 4.24736 9.71379 4.00408 10.0113 4.00408C10.4095 4.00408 10.5508 4.29616 10.5508 4.54633Z"
        fill={PrimaryColor ? "#436EB6" : "#CB9932"}
      />
      <path
        d="M14.0444 17.6445C13.7855 17.6445 13.5757 17.8544 13.5757 18.1133V21.1865C13.5757 21.6615 14.042 21.6582 14.3669 21.6582C14.5327 21.6582 14.7728 21.6573 15.1241 21.6553C15.3829 21.6538 15.5917 21.4428 15.5902 21.1839C15.5888 20.9259 15.3792 20.7178 15.1215 20.7178C15.1206 20.7178 15.1197 20.7178 15.1189 20.7178C14.913 20.719 14.6961 20.7198 14.5132 20.7202V18.1133C14.5132 17.8544 14.3033 17.6445 14.0444 17.6445Z"
        fill={PrimaryColor ? "#436EB6" : "#CB9932"}
      />
      <path
        d="M7.48963 21.6579C7.74852 21.6579 7.95838 21.448 7.95838 21.1891V18.1133C7.95838 17.8544 7.74852 17.6445 7.48963 17.6445C7.23074 17.6445 7.02088 17.8544 7.02088 18.1133V19.1297H5.82983V18.1133C5.82983 17.8544 5.61997 17.6445 5.36108 17.6445C5.10219 17.6445 4.89233 17.8544 4.89233 18.1133V21.1891C4.89233 21.448 5.10219 21.6579 5.36108 21.6579C5.61997 21.6579 5.82983 21.448 5.82983 21.1891V20.0672H7.02088V21.1891C7.02088 21.448 7.23074 21.6579 7.48963 21.6579Z"
        fill={PrimaryColor ? "#436EB6" : "#CB9932"}
      />
      <path
        d="M16.2369 21.1799C16.2531 21.3965 16.4816 21.6138 16.7056 21.6138C16.7312 21.6137 17.3346 21.6113 17.5761 21.6071C18.5271 21.5905 19.2173 20.7685 19.2173 19.6526C19.2173 18.4795 18.5445 17.6914 17.5431 17.6914H16.6978C16.436 17.6914 16.229 17.9032 16.229 18.1632C16.2291 18.1632 16.232 21.1528 16.2369 21.1799ZM17.5431 18.6289C18.2267 18.6289 18.2798 19.4124 18.2798 19.6526C18.2798 20.1526 18.0571 20.6611 17.5597 20.6697C17.4683 20.6713 17.3194 20.6727 17.171 20.6737C17.1699 20.3296 17.168 18.9877 17.1673 18.6289H17.5431Z"
        fill={PrimaryColor ? "#436EB6" : "#CB9932"}
      />
      <path
        d="M8.76245 19.6512C8.76245 20.7577 9.66264 21.6579 10.7691 21.6579C11.8756 21.6579 12.7758 20.7577 12.7758 19.6512C12.7758 18.5447 11.8756 17.6445 10.7691 17.6445C9.66264 17.6445 8.76245 18.5447 8.76245 19.6512ZM11.8383 19.6512C11.8383 20.2407 11.3587 20.7204 10.7691 20.7204C10.1796 20.7204 9.69995 20.2407 9.69995 19.6512C9.69995 19.0617 10.1796 18.582 10.7691 18.582C11.3587 18.582 11.8383 19.0617 11.8383 19.6512Z"
        fill={PrimaryColor ? "#436EB6" : "#CB9932"}
      />
      <path
        d="M11.6406 0.647517C11.7161 0.831736 11.9093 0.951173 12.108 0.935705C12.3077 0.920142 12.4789 0.776611 12.528 0.582267C12.6287 0.183783 12.1825 -0.141202 11.8327 0.0667358C11.6361 0.183548 11.5527 0.436392 11.6406 0.647517Z"
        fill={PrimaryColor ? "#436EB6" : "#CB9932"}
      />
    </svg>
  );
}

export default HoldIcon;
