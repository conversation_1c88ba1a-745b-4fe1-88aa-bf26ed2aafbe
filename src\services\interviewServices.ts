import * as http from "@/utils/http";
import endpoint from "@/constants/endpoint";
import { ApiResponse } from "@/interfaces/commonInterfaces";
import {
  IAddInterviewSkillQuestion,
  IGetCandidateListResponse,
  IGetInterviewersResponse,
  IGetInterviewSkillQuestions,
  IGetInterviewSkillQuestionsResponse,
  IGetInterviewsResponse,
  IGetJobListResponse,
  IInterviewQuestionResponse,
  IInterviewStaticInformation,
  IScheduleInterview,
  IUpcomingOrPastInterview,
  IUpdateInterviewAnswers,
  IUpdateInterviewSkillQuestion,
  IUpdateScheduleInterview,
} from "@/interfaces/interviewInterfaces";
import { IFeedbackResponse } from "@/components/views/interviewFeedback/InterviewFeedback";

export const updateOrScheduleInterview = (data: IScheduleInterview | IUpdateScheduleInterview): Promise<ApiResponse<null>> => {
  return http.post(endpoint.interview.UPDATE_OR_SCHEDULE_INTERVIEW, data);
};

export const getInterviewers = (searchString: string, jobId: string): Promise<ApiResponse<IGetInterviewersResponse[]>> => {
  return http.get(endpoint.interview.GET_INTERVIEWERS, { searchString, jobId });
};

export const upcomigOrPastInterview = (params: {
  isPast: boolean;
  limit?: number;
  offset?: number;
  searchStr?: string;
}): Promise<ApiResponse<IUpcomingOrPastInterview[]>> => {
  return http.get(endpoint.interview.GET_UPCOMING_OR_PAST_INTERVIEW, { ...params });
};

export const getMyInterviews = (monthYear: string): Promise<ApiResponse<IGetInterviewsResponse[]>> => {
  return http.get(endpoint.interview.GET_MY_INTERVIEWS, { monthYear });
};

export const getInterviewSkillQuestions = (data: IGetInterviewSkillQuestions): Promise<ApiResponse<IGetInterviewSkillQuestionsResponse>> => {
  return http.get(endpoint.interview.GET_INTERVIEW_SKILL_QUESTIONS, data);
};

export const updateInterviewSkillQuestion = (data: IUpdateInterviewSkillQuestion): Promise<ApiResponse<null>> => {
  return http.post(endpoint.interview.UPDATE_INTERVIEW_SKILL_QUESTION, data);
};

export const addInterviewSkillQuestion = (data: IAddInterviewSkillQuestion): Promise<ApiResponse<IInterviewQuestionResponse>> => {
  return http.post(endpoint.interview.ADD_INTERVIEW_SKILL_QUESTION, data);
};

export const getJobList = (searchString: string): Promise<ApiResponse<IGetJobListResponse[]>> => {
  return http.get(endpoint.interview.GET_JOB_LIST, { searchString });
};

export const getCandidateList = (data: { searchString: string; jobId: string }): Promise<ApiResponse<IGetCandidateListResponse[]>> => {
  return http.get(endpoint.interview.GET_CANDIDATE_LIST, data);
};

export const updateInterviewAnswers = (data: IUpdateInterviewAnswers): Promise<ApiResponse<null>> => {
  return http.post(endpoint.interview.UPDATE_INTERVIEW_ANSWERS, data);
};

export const endInterview = (data: { interviewId: number; behaviouralNotes: string }): Promise<ApiResponse<null>> => {
  return http.post(endpoint.interview.END_INTERVIEW, data);
};

export const conductInterviewStaticInformation = (): Promise<ApiResponse<IInterviewStaticInformation>> => {
  return http.get(endpoint.interview.CONDUCT_INTERVIEW_STATIC_INFORMATION);
};

export const getInterviewFeedback = (interviewId: number): Promise<ApiResponse<IFeedbackResponse>> => {
  return http.get(endpoint.interview.GET_INTERVIEW_FEEDBACK.replace(":interviewId", interviewId.toString()));
};

export const updateInterviewFeedback = (data: {
  interviewId: number;
  feedback: string;
  isAdvanced: boolean;
  behavioralNotes: string;
}): Promise<ApiResponse<null>> => {
  return http.post(endpoint.interview.UPDATE_INTERVIEW_FEEDBACK, data);
};

export interface IPendingInterview {
  interviewId: number;
  candidateName: string;
  jobTitle: string;
  roundNumber: number;
  date: string;
  startTime: string;
  endTime: string;
  interviewerId: number;
}

export const getPendingInterviews = (): Promise<ApiResponse<IPendingInterview[]>> => {
  return http.get(endpoint.interview.GET_PENDING_INTERVIEWS);
};

export const cancelInterview = (interviewId: number): Promise<ApiResponse<null>> => {
  return http.patch(endpoint.interview.CANCEL_INTERVIEW.replace(":interviewId", interviewId.toString()));
};
