import * as http from "@/utils/http";
import endpoint from "@/constants/endpoint";
import { ApiResponse, FilePath } from "@/interfaces/commonInterfaces";

export const removeAttachmentsFromS3 = (data: { fileUrlArray: string }): Promise<ApiResponse<null>> => {
  return http.post(endpoint.common.REMOVE_ATTACHMENTS_FROM_S3, data);
};

export const getSignedUrl = (data: FilePath): Promise<ApiResponse<string | null>> => {
  return http.post(endpoint.common.GENERATE_PRESIGNED_URL, data);
};
