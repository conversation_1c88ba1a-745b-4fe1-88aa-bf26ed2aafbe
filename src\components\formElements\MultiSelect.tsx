import React from "react";
import Select, { MultiValue } from "react-select";
import { Control, Controller, FieldValues, Path } from "react-hook-form";

export interface MultiSelectOption {
  label: string;
  value: string | number;
}

interface MultiSelectProps<T extends FieldValues> {
  name: Path<T>;
  placeholder?: string;
  control: Control<T>;
  options: MultiSelectOption[];
  className?: string;
}

export default function MultiSelect<T extends FieldValues>({ options, name, control, placeholder, className }: MultiSelectProps<T>) {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <Select
          isMulti
          className={className}
          options={options}
          placeholder={placeholder}
          value={options.filter((opt) => ((field.value as (string | number)[]) || []).includes(opt.value)) || ""}
          onChange={(selected: MultiValue<MultiSelectOption>) => {
            field.onChange(selected.map((opt) => opt.value));
          }}
          closeMenuOnSelect={false}
          hideSelectedOptions={false}
          isClearable
          styles={{
            menu: (provided) => ({ ...provided, zIndex: 9999 }),
          }}
          aria-label=""
        />
      )}
      defaultValue={[] as T[typeof name]}
    />
  );
}
