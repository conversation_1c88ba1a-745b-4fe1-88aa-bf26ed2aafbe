import { TextareaHTMLAttributes, forwardRef, ForwardedRef } from "react";
import { Control, Controller, FieldValues, Path } from "react-hook-form";

interface TextareaProps<T extends FieldValues> extends TextareaHTMLAttributes<HTMLTextAreaElement> {
  name: Path<T>;
  control: Control<T>;
}

const TextareaBase = <T extends FieldValues>({ control, name, ...props }: TextareaProps<T>, ref: ForwardedRef<HTMLTextAreaElement>) => {
  return (
    <Controller
      control={control}
      render={({ field }) => {
        return <textarea {...props} ref={ref} value={field.value} onChange={field.onChange} aria-label="" />;
      }}
      name={name}
      defaultValue={"" as T[typeof name]}
    />
  );
};

const Textarea = forwardRef(TextareaBase) as <T extends FieldValues>(
  props: TextareaProps<T> & { ref?: ForwardedRef<HTMLTextAreaElement> }
) => ReturnType<typeof TextareaBase>;

export default Textarea;
