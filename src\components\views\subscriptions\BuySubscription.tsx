"use client";
import React, { useEffect, useState } from "react";
import style from "@/styles/accessManagement.module.scss";
import Button from "@/components/formElements/Button";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { PlanData } from "../../../interfaces/subscriptionInterfaces";
import { buySubscription, cancelPlan, getAllPlans, getCurrentSubscription } from "@/services/subscription";
import NewJobCreationIcon from "@/components/svgComponents/subscription/NewJobCreationIcon";
import CandidateTrackingIcon from "@/components/svgComponents/subscription/CandidateTrackingIcon";
import PreScreeningIcon from "@/components/svgComponents/subscription/PreScreeningIcon";
import ResumeScreeningIcon from "@/components/svgComponents/subscription/ResumeScreeningIcon";
import AIJobDescriptionIcon from "@/components/svgComponents/subscription/AIJobDescriptionIcon";
import ApplicantTrackingSystemIcon from "@/components/svgComponents/subscription/ApplicantTrackingSystemIcon";
import AIGeneratedQuestionsIcon from "@/components/svgComponents/subscription/AIGeneratedQuestionsIcon";
import RealTimeFollowUpIcon from "@/components/svgComponents/subscription/RealTimeFollowUpIcon";
import NonVerbalCommunicationIcon from "@/components/svgComponents/subscription/NonVerbalCommunicationIcon";
import FinalAnalysisIcon from "@/components/svgComponents/subscription/FinalAnalysisIcon";
import AIPoweredInterviewSummaryIcon from "@/components/svgComponents/subscription/AIPoweredInterviewSummaryIcon";
import SkillSpecificAssessmentsIcon from "@/components/svgComponents/subscription/SkillSpecificAssessmentsIcon";
import RoleBasedAccessIcon from "@/components/svgComponents/subscription/RoleBasedAccessIcon";
import DedicatedSupportIcon from "@/components/svgComponents/subscription/DedicatedSupportIcon";
import ManualResumeUploadIcon from "@/components/svgComponents/subscription/ManualResumeUploadIcon";
import DataSecurityIcon from "@/components/svgComponents/subscription/DataSecurityIcon";
import VerifiedCheckmarkIcon from "@/components/svgComponents/VerifiedCheckmarkIcon";
import ROUTES from "@/constants/routes";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { setCurrentPlan } from "@/redux/slices/authSlice";
import { toastMessageError, toastMessageSuccess } from "@/utils/helper";
import { useTranslations } from "next-intl";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { SUBSCRIPTION_PAYMENT_TYPE, SUBSCRIPTION_STATUS } from "@/constants/subscriptionConstants";
import ConfirmationModal from "@/components/commonModals/ConfirmationModal";

const initialDataOfConfirmationModal = {
  isOpen: false,
  title: "",
  message: "",
  confirmButtonText: "",
  cancelButtonText: "",
  onclickConfirm: () => {},
  onClickCancel: () => {},
};

function BuySubscription() {
  const t = useTranslations();

  const router = useRouter();
  const dispatch = useDispatch();

  const [loading, setLoading] = useState<boolean>(false);
  const [loadingAllPlans, setLoadingAllPlans] = useState<boolean>(false);
  const [plans, setPlans] = useState<PlanData[]>([]);

  const [confirmationModalInfo, setConfirmationModalInfo] = useState(initialDataOfConfirmationModal);

  const myCurrentPlan = useSelector((state: RootState) => state.auth.currentPlan);

  console.log("Current Plan from Redux:", myCurrentPlan, loadingAllPlans);

  // Helper function to directly get the feature text value by slug
  const getBenefitValue = (plan: PlanData, benefitKey: string): React.ReactNode => {
    console.log("Plan Benefits:", plan.subscriptionPlanBenefits);

    // Find the feature with the matching slug
    const feature = plan.subscriptionPlanBenefits?.find((item) => item.slug === benefitKey);

    // Features that should show VerifiedCheckmarkIcon when value is true
    const featuresWithIcon = [
      "ai_job_description",
      "applicant_tracking_system",
      "pre_interview_assessment_upload",
      "ai_generated_questions",
      "realtime_follow_up_questions",
      "non_verbal_communication_analysis",
      "final_analysis_after_interview",
      "skill_specific_assessments",
    ];

    // Check if this feature should show icon and if the value is true
    if (featuresWithIcon.includes(benefitKey) && feature?.value === true) {
      return <VerifiedCheckmarkIcon />;
    }

    // Return the text value if found, otherwise return 'Not available'
    return feature?.text || "Not available";
  };

  useEffect(() => {
    initializeSubscriptionData();
    fetchAllPlans();
  }, []);

  // Initialize subscription data - calls current plan first, then all plans
  const initializeSubscriptionData = async () => {
    // try {
    setLoading(true);
    // First fetch the current subscription
    const response = await getCurrentSubscription();
    // let currentPlanId = response.data.data.plan_id; // Default to Free Trial if no active subscription

    if (response?.data?.success) {
      dispatch(setCurrentPlan(response.data.data));
    }
    //   interface ApiSubscriptionResponse {
    //     plan_id?: number;
    //     plan_name?: string;
    //     subscription_status?: string;
    //     pricing_type?: string;
    //     price?: string;
    //     expiry_date?: string;
    //     next_billing_date?: string;
    //     [key: string]: string | number | boolean | null | undefined; // Allow other properties with specific types
    //   }

    //   const responseData = response.data?.data || {};
    //   const apiData = responseData as unknown as ApiSubscriptionResponse;

    //   if (apiData.plan_id) {
    //     currentPlanId = apiData.plan_id;
    //   }
    // }

    // // Now fetch all plans with the current plan ID
    // await fetchPlansWithCurrentPlan(String(currentPlanId));
    // } catch (error) {
    //   console.error("Error initializing subscription data:", error);
    // }
    setLoading(false);
  };

  // Handle cancel plan click
  const handleCancelPlanClick = () => {
    setConfirmationModalInfo({
      ...confirmationModalInfo,
      isOpen: true,
      title: "Cancel Subscription",
      message: "Are you sure you want to cancel your subscription? Your plan will remain active until the end of your current billing period.",
      confirmButtonText: "Confirm Cancelation",
      onClickCancel: () => {
        setConfirmationModalInfo(initialDataOfConfirmationModal);
      },
      onclickConfirm: handleConfirmCancellation,
    });
  };

  // Handle click on already canceled plan
  const handleCanceledPlanClick = () => {
    // Format the expiry date to MM/DD/YYYY
    let formattedExpiryDate = "";
    if (myCurrentPlan?.expiryDate) {
      const expiryDate = new Date(myCurrentPlan.expiryDate);
      formattedExpiryDate = expiryDate.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    }

    setConfirmationModalInfo({
      ...confirmationModalInfo,
      isOpen: true,
      title: "Plan Canceled",
      message: `Your subscription has been successfully canceled and will remain active until the end of the current billing cycle on <strong style="font-weight: 500;">${formattedExpiryDate || "[date not available]"}</strong>. After this date, your account will automatically revert to the <strong style="font-weight: 500;">Free Plan</strong> with limited access to premium features.`,
      onClickCancel: () => {
        setConfirmationModalInfo(initialDataOfConfirmationModal);
      },
      onclickConfirm: () => {
        setConfirmationModalInfo(initialDataOfConfirmationModal);
      },
    });
  };

  // Handle confirmation of plan cancellation
  const handleConfirmCancellation = async () => {
    try {
      setLoading(true);
      const response = await cancelPlan({});
      if (response.data?.success) {
        // Fetch updated plan details
        const response = await getCurrentSubscription();
        // let currentPlanId = response.data.data.plan_id; // Default to Free Trial if no active subscription
        if (response?.data?.success) {
          dispatch(setCurrentPlan(response.data.data));
        }
        toastMessageSuccess(t("subscription_canceled_successfully"));
      } else {
        const errorMessage = response.data?.message || "failed_to_cancel_subscription";
        toastMessageError(t(errorMessage));
      }
      setConfirmationModalInfo(initialDataOfConfirmationModal);
    } catch (error) {
      console.error("Error cancelling subscription:", error);
      toastMessageError(t("error_cancelling_subscription"));
    } finally {
      setLoading(false);
    }
  };

  // Fetch all plans and set the current plan based on the user's subscription
  const fetchAllPlans = async () => {
    try {
      setLoadingAllPlans(true);
      const response = await getAllPlans();

      // Extract the plans array from the API response's nested data structure
      let plansArray: PlanData[] = [];
      if (response?.data?.success && response.data.data) {
        plansArray = response.data.data as PlanData[];
      }

      setPlans(plansArray);

      // const currentPlan = plansArray.find((plan) => String(plan.plan_id) === currentPlanId);
      // if (currentPlan) {
      //   // Set the current plan as selected
      //   setSelectedPlan(currentPlan.plan_name);
      //   // setSelectedPlanId(String(currentPlan.plan_id));

      //   // Update pricing options for this plan
      //   if (currentPlan.pricing_options && currentPlan.pricing_options.length > 0) {
      //     setSelectedPricingId(String(currentPlan.pricing_options[0].pricing_id));
      //   }
      // } else {
      //   const freeTrial = plans.find((plan) => plan.plan_name === "Free Trial");
      //   if (freeTrial) {
      //     setSelectedPlan(freeTrial.plan_name);
      //   } else {
      //     const anyPlanWithPricing = plans.find((plan) => plan.pricing_options.length > 0);
      //     if (anyPlanWithPricing) {
      //       setSelectedPlan(anyPlanWithPricing.plan_name);
      //       setSelectedPricingId(String(anyPlanWithPricing.pricing_options[0].pricing_id));
      //     }
      //   }
      // }
    } catch (error) {
      console.error("Error fetching subscription plans:", error);
      toast.error("Failed to load subscription plans. Please try again.");
    } finally {
      setLoadingAllPlans(false);
    }
  };

  // const handlePlanSelect = (plan: string) => {
  //   const selectedPlanObj = plans.find((p) => p.plan_name === plan);
  //   if (selectedPlanObj) {
  //     setSelectedPlan(plan);
  //     updatePricingForSelectedPlan(String(selectedPlanObj.plan_id));
  //   }
  // };

  // const updatePricingForSelectedPlan = (planId: string) => {
  //   const selectedPlan = plans.find((plan) => String(plan.plan_id) === planId);
  //   if (selectedPlan && selectedPlan.pricing_options.length > 0) {
  //     // Always prioritize Monthly pricing if available
  //     const monthlyPricing = selectedPlan.pricing_options.find((price) => price.type === "Monthly");
  //     if (monthlyPricing) {
  //       setSelectedPricingId(String(monthlyPricing.pricing_id));
  //     } else {
  //       // Fall back to first pricing option if Monthly is not available
  //       const defaultPricing = selectedPlan.pricing_options[0];
  //       setSelectedPricingId(String(defaultPricing.pricing_id));
  //     }
  //   }
  // };

  const handleSubscribeClick = async (selectedPlan: PlanData) => {
    try {
      setLoading(true);

      if (selectedPlan.subscriptionPlanPaymentType === SUBSCRIPTION_PAYMENT_TYPE.FREE) {
        // For free trial, just navigate to the dashboard
        router.push(ROUTES.DASHBOARD);
        return;
      }

      // Prepare data for buy subscription API
      const buySubscriptionData = {
        planId: selectedPlan.subscriptionPlanId,
        pricingId: selectedPlan.pricingId!,
      };

      console.log("Buy subscription data:", buySubscriptionData);

      // Call the simplified buy subscription API
      const buySubscriptionResponse = await buySubscription(buySubscriptionData);

      console.log("Buy subscription response:", buySubscriptionResponse);

      // Handle the response and redirect to checkout
      if (buySubscriptionResponse.data && buySubscriptionResponse.data.success) {
        const checkoutUrl = buySubscriptionResponse.data.data?.checkoutUrl;
        if (checkoutUrl) {
          console.log("Redirecting to checkout URL:", checkoutUrl);
          // Close the confirmation modal
          setConfirmationModalInfo(initialDataOfConfirmationModal);
          // Show success message
          // toastMessageSuccess("Payment URL fetched successfully! Redirecting to checkout...");
          // Open checkout URL in new tab
          // window.open(checkoutUrl, "_blank");
          window.location.href = checkoutUrl;
        } else {
          setConfirmationModalInfo(initialDataOfConfirmationModal);
          toastMessageError(t(buySubscriptionResponse.data?.message || "failed_to_fetch_checkout_url"));
          return;
        }
      } else {
        setConfirmationModalInfo(initialDataOfConfirmationModal);
        toastMessageError(t(buySubscriptionResponse.data?.message || "failed"));
      }
    } catch (error) {
      setConfirmationModalInfo(initialDataOfConfirmationModal);
      console.error("Error during subscription process:", error);
      toast.error(error instanceof Error ? error.message : "Failed to process subscription. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const onClickFreePlan = () => {
    // show confirmation modal
    setConfirmationModalInfo({
      ...confirmationModalInfo,
      isOpen: true,
      title: "Switch to Free Plan",
      message:
        "To switch to the Free Plan, cancel your current subscription.Your account will automatically downgrade at the end of the billing cycle.",
      // confirmButtonText: "Proceed",
      // onclickConfirm: () => {
      //   router.push(ROUTES.PROFILE.MY_PROFILE);
      // },
      onClickCancel: () => {
        setConfirmationModalInfo(initialDataOfConfirmationModal);
      },
    });
  };

  const onClickBuyPlan = (plan: PlanData) => {
    // show confirmation modal
    setConfirmationModalInfo({
      ...confirmationModalInfo,
      isOpen: true,
      title: "Confirm Plan Change",
      message:
        "You're about to switch to a new plan. Your current plan will be canceled, and the new plan will become active immediately.Would you like to proceed?",
      confirmButtonText: "Buy Plan",
      cancelButtonText: "Cancel",
      onClickCancel: () => {
        setConfirmationModalInfo(initialDataOfConfirmationModal);
      },
      onclickConfirm: () => {
        handleSubscribeClick(plan);
      },
    });
  };
  return (
    <div className={style.subscription_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                Subscription <span>Plan</span>
              </h2>
            </div>
          </div>
        </div>
        <div className="inner-content">
          <div className="row flex-nowrap">
            <div className="col-lg-3 col-md-4 col-6">
              <ul className={`${style.subscription_plan} ${style.side_bar} pe-4`}>
                <li className={style.subscription_benefit_text}>Benefits</li>
                <li className={style.fixed_height} title="Job Postings">
                  <NewJobCreationIcon /> <span className={style.benefit_text}>Job Postings</span>
                </li>
                <li className={style.fixed_height} title="Resume Screening">
                  <ResumeScreeningIcon />
                  <span className={style.benefit_text}>Resume Screening</span>
                </li>
                <li className={`${style.fixed_height} ${style.ai_job_description}`} title="AI Job Description">
                  <AIJobDescriptionIcon />
                  <span className={style.benefit_text}>AI Job Description</span>
                </li>
                <li className={`${style.fixed_height} ${style.max_height}`} title="Candidate Tracking">
                  <CandidateTrackingIcon />
                  <span className={style.benefit_text}>Candidate Tracking</span>
                </li>
                <li className={style.fixed_height} title="Applicant Tracking System">
                  <ApplicantTrackingSystemIcon />
                  <span className={style.benefit_text}>Applicant Tracking System</span>
                </li>
                <li className={style.fixed_height} title="Pre-Interview Assessment Upload">
                  <PreScreeningIcon />
                  <span className={style.benefit_text}>Pre-Interview Assessment Upload</span>
                </li>
                <li className={style.fixed_height} title="AI-Generated Pre-Interview Questions">
                  <AIGeneratedQuestionsIcon />
                  <span className={style.benefit_text}>AI-Generated Pre-Interview Questions</span>
                </li>
                <li className={style.fixed_height} title="Real-Time Follow-Up Questions">
                  <RealTimeFollowUpIcon />
                  <span className={style.benefit_text}>Real-Time Follow-Up Questions</span>
                </li>
                <li className={style.fixed_height} title="Non-Verbal Communication Analysis">
                  <NonVerbalCommunicationIcon />
                  <span className={style.benefit_text}>Non-Verbal Communication Analysis</span>
                </li>
                <li className={style.fixed_height} title="Final Analysis After Interview">
                  <FinalAnalysisIcon />
                  <span className={style.benefit_text}>Final Analysis After Interview</span>
                </li>
                <li className={style.fixed_height} title="AI-Powered Interview Summary">
                  <AIPoweredInterviewSummaryIcon />
                  <span className={style.benefit_text}>AI-Powered Interview Summary</span>
                </li>
                <li className={style.fixed_height} title="Skill-Specific AI Assessments">
                  <SkillSpecificAssessmentsIcon />
                  <span className={style.benefit_text}>Skill-Specific AI Assessments</span>
                </li>
                <li className={style.fixed_height} title="Role-Based Access">
                  <RoleBasedAccessIcon />
                  <span className={style.benefit_text}>Role-Based Access</span>
                </li>
                <li className={style.fixed_height} title="Dedicated Support">
                  <DedicatedSupportIcon />
                  <span className={style.benefit_text}>Dedicated Support</span>
                </li>
                <li className={style.fixed_height} title="Manual Resume Upload">
                  <ManualResumeUploadIcon />
                  <span className={style.benefit_text}>Manual Resume Upload</span>
                </li>
                <li className={style.fixed_height} title="Data Security">
                  <DataSecurityIcon />
                  <span className={style.benefit_text}>Data Security</span>
                </li>
              </ul>
            </div>

            {/* Right Side: Dynamic Plan Options */}
            <div className="col-6 col-md-8 col-lg-9">
              <div className="row g-4 flex-nowrap overflow-auto">
                {plans.map((plan) => {
                  const isCurrentPlan = myCurrentPlan?.subscriptionPlanId === plan.subscriptionPlanId;
                  const isPlanCanceled = isCurrentPlan && myCurrentPlan?.status === SUBSCRIPTION_STATUS.CANCEL_AT_PERIOD_END;
                  return (
                    <div key={plan.subscriptionPlanId} className="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                      <div className={`${style.subscription_plan_card} ${isCurrentPlan ? style.selected_plan : ""}`}>
                        <div className={style.subscription_option}>
                          <h4 className={style.plan_name}>{plan.subscriptionPlanName}</h4>
                          <div>
                            <h4 className={style.plan_price}>${plan.price || "0.00"}</h4>
                            <p className={style.price_type}>Billed Monthly</p>
                          </div>
                          {isCurrentPlan && <span className={style.save_badge}>Current Plan</span>}
                          <div className="mt-3">
                            {plan.subscriptionPlanPaymentType === SUBSCRIPTION_PAYMENT_TYPE.FREE ? (
                              <Button className={"dark-outline-btn "} onClick={() => onClickFreePlan()} disabled={loading || isCurrentPlan}>
                                {isCurrentPlan ? "Current Plan" : "Avail free"}
                              </Button>
                            ) : (
                              <Button
                                className={isPlanCanceled ? "danger-outline-btn-hover-none" : isCurrentPlan ? "danger-outline-btn" : "primary-btn"}
                                onClick={
                                  isPlanCanceled ? handleCanceledPlanClick : isCurrentPlan ? handleCancelPlanClick : () => onClickBuyPlan(plan)
                                }
                                disabled={loading}
                              >
                                {isPlanCanceled ? "Canceled" : isCurrentPlan ? "Cancel Now" : "Buy Now"}
                              </Button>
                            )}
                            {/* {loadingCancelPlan && <Skeleton height={39} width="100%" borderRadius={12} />} */}
                          </div>
                        </div>
                        <ul
                          className={`${style.subscription_plan} ${style.subscription_benefit_text} ${isCurrentPlan ? style.selected_plan : ""}`}
                          // onClick={() => handlePlanSelect(plan.subscriptionPlanName)}
                        >
                          {/* <li className={style.fixed_height}>{plan.subscriptionPlanName}</li> */}
                          <li className={style.fixed_height}>{getBenefitValue(plan, "job_postings")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "resume_screening")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "ai_job_description")}</li>
                          <li className={`${style.fixed_height} ${style.max_height}`}>{getBenefitValue(plan, "candidate_tracking")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "applicant_tracking_system")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "pre_interview_assessment_upload")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "ai_generated_questions")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "realtime_follow_up_questions")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "non_verbal_communication_analysis")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "final_analysis_after_interview")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "ai_powered_interview_summary")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "skill_specific_assessments")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "role_based_access")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "dedicated_support")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "manual_resume_upload")}</li>
                          <li className={style.fixed_height}>{getBenefitValue(plan, "data_security")}</li>
                        </ul>
                      </div>
                    </div>
                  );
                })}

                {/* Skeleton Loader card  */}
                {loadingAllPlans ? (
                  <div className="row g-4 flex-nowrap overflow-auto">
                    <div className="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                      <Skeleton height={1020} width="100%" borderRadius={16} />
                    </div>
                    <div className="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                      <Skeleton height={1020} width="100%" borderRadius={16} />
                    </div>
                    <div className="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                      <Skeleton height={1020} width="100%" borderRadius={16} />
                    </div>
                    <div className="col-12 col-sm-6 col-md-4 col-lg-4 col-xl-3">
                      <Skeleton height={1020} width="100%" borderRadius={16} />
                    </div>
                  </div>
                ) : null}
              </div>
            </div>
          </div>

          {/* {
            <div className="button-align py-5">
              <Button className="primary-btn rounded-md" onClick={handleSubscribeClick} disabled={loading}>
                {`Subscribe to ${selectedPlan} Monthly Plan`}
              </Button>
              <Button className="dark-outline-btn rounded-md" onClick={handleCancel} disabled={loading || !selectedPricingId}>
                Cancel
              </Button>
            </div>
          } */}

          <ConfirmationModal
            isOpen={confirmationModalInfo.isOpen}
            onClose={confirmationModalInfo.onClickCancel}
            onConfirm={confirmationModalInfo.onclickConfirm}
            title={confirmationModalInfo.title}
            message={confirmationModalInfo.message}
            confirmButtonText={confirmationModalInfo.confirmButtonText}
            // cancelButtonText={confirmationModalInfo.cancelButtonText}
            loading={loading}
          />
        </div>
      </div>
    </div>
  );
}

export default BuySubscription;
