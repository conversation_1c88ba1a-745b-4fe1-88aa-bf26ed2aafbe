"use client";

// Internal libraries
import React, { useState, useEffect, useRef } from "react";

import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";

// Components
import Loader from "@/components/loader/Loader";
import Button from "@/components/formElements/Button";

// Services
import { getJobHtmlDescription, saveJobDetails, updateJobDescription } from "@/services/jobRequirements/jobServices";

// Redux, constants, interfaces
import { clearJobRequirement, selectJobRequirement } from "@/redux/slices/jobRequirementSlice";
import { clearJobDetails, selectJobDetails } from "@/redux/slices/jobDetailsSlice";
import { clearSkillsData, selectCareerSkills, selectCultureSpecificSkills, selectRoleSpecificSkills } from "@/redux/slices/jobSkillsSlice";
import { ISkillData } from "@/interfaces/jobRequirementesInterfaces";
import ROUTES from "@/constants/routes";
import { EMPTY_CONTENT_PATTERNS } from "@/constants/commonConstants";
import { SUN_EDITOR_BUTTON_LIST } from "@/constants/jobRequirementConstant";

// CSS
import style from "@/styles/commonPage.module.scss";

// Import SunEditor CSS
import "suneditor/dist/css/suneditor.min.css";
import { useTranslations } from "next-intl";
import CopyIcon from "@/components/svgComponents/CopyIcon";
import { toastMessageError, toastMessageSuccess, getDecryptedData } from "@/utils/helper";
import PdfGenerator from "./PdfGenerator";

// Import SunEditor dynamically to avoid SSR issues
const SunEditor = dynamic(() => import("suneditor-react"), {
  ssr: false,
});

/**
 * JobEditor component for creating and editing job requirements
 * @component
 * @description A rich text editor component for creating and managing job requirements
 * @returns {JSX.Element} The JobEditor component
 */
function JobEditor() {
  const router = useRouter();
  const dispatch = useDispatch();
  const jobRequirement = useSelector(selectJobRequirement);
  const jobDetails = useSelector(selectJobDetails);

  // State for edit mode and job details
  const [isEditMode, setIsEditMode] = useState(false);
  const [jobId, setJobId] = useState<number | null>(null);
  const [jobTitle, setJobTitle] = useState("");

  const [editorContent, setEditorContent] = useState("");
  const editorRef = useRef<unknown>(null);
  const cultureSpecificSkills = useSelector(selectCultureSpecificSkills) || [];
  const careerSkills = useSelector(selectCareerSkills) || [];
  const roleSpecificSkills = useSelector(selectRoleSpecificSkills) || [];

  const [isLoading, setIsLoading] = useState(false);
  const [isEditorLoading, setIsEditorLoading] = useState(true);
  const t = useTranslations();
  const tGenerate = useTranslations("jobRequirement");
  const translate = useTranslations(); // Add translate function for error messages

  /**
   * Initializes editor content from Redux store
   * @function
   * @description Sets the editor content from job requirement data when available
   */
  // Parse URL parameters for edit mode
  useEffect(() => {
    // Check URL parameters for edit mode
    const searchParams = new URLSearchParams(window.location.search);
    const encryptedId = searchParams.get("jobId");

    // Check if we're in edit mode (has jobId) or create mode (no jobId)
    const isEditMode = !!encryptedId;

    // Decrypt and process the job ID if we're in edit mode
    const id = isEditMode
      ? (() => {
          try {
            const decryptedData = getDecryptedData(encryptedId);
            return decryptedData?.jobId ? decryptedData.jobId.toString() : encryptedId;
          } catch (error) {
            console.error("Error decrypting job ID:", error);
            toastMessageError(translate("something_went_wrong"));
            router.back();
            return null;
          }
        })()
      : null;

    // Define an async function inside useEffect
    const loadJobData = async () => {
      try {
        // Set edit mode
        setIsEditMode(true);
        // Make sure id is not null before parsing
        if (id) {
          setJobId(parseInt(id, 10));
          const result = await getJobHtmlDescription(id);
          console.log(result);
          if (result && result.data && result.data.success) {
            const decoder = document.createElement("textarea");
            decoder.innerHTML = result.data.data.htmlDescription;
            const decodedHtml = decoder.value;
            setEditorContent(decodedHtml);
            setJobTitle(result.data.data.title);
          }
        }
      } catch (error) {
        console.error("Error loading job data:", error);
      } finally {
        setIsEditorLoading(false);
      }
    };

    // Only require id to activate edit mode
    if (id) {
      // Call the async function
      loadJobData();
    } else if (jobRequirement && jobRequirement.content) {
      // Normal mode - set from Redux
      setEditorContent(jobRequirement.content);

      setIsEditorLoading(false);
    }
  }, [jobRequirement, router, translate]);

  /**
   * Updates editor content state
   * @function handleEditorChange
   * @param {string} content - The new content from the editor
   * @description Updates the editor content state with new content
   */
  const handleEditorChange = (content: string) => {
    setEditorContent(content);
  };

  /**
   * Checks if editor content is empty or contains only whitespace
   * @function isEditorEmpty
   * @param {string} content - The content to check
   * @returns {boolean} True if content is empty or contains only whitespace
   * @description Handles various empty content patterns including HTML tags
   */
  const isEditorEmpty = (content: string) => {
    if (!content) return true;

    const trimmed = content.trim();
    if (trimmed === "") return true;

    if (EMPTY_CONTENT_PATTERNS.includes(trimmed)) return true;

    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = content;
    const textContent = tempDiv.textContent || tempDiv.innerText || "";
    return textContent.trim() === "";
  };

  /**
   * Saves job requirement data to backend
   * @function handleSave
   * @async
   * @returns {Promise<void>}
   * @throws {Error} If save operation fails
   * @description Validates content, formats job data, and saves to backend
   */
  const handleSave = async () => {
    try {
      if (isEditorEmpty(editorContent)) {
        toastMessageError(t("enter_job_requirement"));
        return;
      }

      setIsLoading(true);

      if (isEditMode && jobId) {
        // Edit mode - prepare data for updating existing job
        const updateData = {
          jobId: jobId,
          finalJobDescriptionHtml: editorContent,
        };
        console.log("updateData===========>>>>>>>>>>>>>>", updateData);
        // Call API to update job details
        const result = await updateJobDescription(updateData); // Using type assertion for now

        if (result && result.data && result.data.success) {
          // Show success message
          toastMessageSuccess(t(result.data.message));
          // empty job editor
          setEditorContent("");
          // Navigate to active jobs page
          router.replace(ROUTES.JOBS.ACTIVE_JOBS);
        } else {
          throw new Error(tGenerate("failed_to_update_job_description"));
        }
      } else {
        // Regular mode - use existing JobEditor logic with proper typing
        console.log("jdlink", jobDetails.jd_link);
        const requestData = {
          title: jobDetails.title,
          employment_type: jobDetails.employment_type,
          salary_range: jobDetails.salary_range,
          salary_cycle: jobDetails.salary_cycle,
          location_type: jobDetails.location_type,
          state: jobDetails.state,
          city: jobDetails.city,
          role_overview: jobDetails.role_overview,
          experience_level: jobDetails.experience_level,
          responsibilities: jobDetails.responsibilities,
          educations_requirement: jobDetails.educations_requirement,
          certifications: jobDetails.certifications,
          skills_and_software_expertise: jobDetails.skills_and_software_expertise,
          experience_required: jobDetails.experience_required,
          ideal_candidate_traits: jobDetails.ideal_candidate_traits,
          about_company: jobDetails.about_company,
          perks_benefits: jobDetails.perks_benefits,
          tone_style: jobDetails.tone_style,
          additional_info: jobDetails.additional_info,
          compliance_statement: jobDetails.compliance_statement,
          show_compliance: jobDetails.show_compliance,
          final_job_description_html: editorContent,
          hiring_type: jobDetails.hiring_type,
          department_id: jobDetails.department_id,
          jd_link: jobDetails.jd_link,
          career_skills: careerSkills.map((skill: ISkillData) => ({
            name: skill.name,
            description: skill.description,
          })),
          role_specific_skills: roleSpecificSkills.map((skill: ISkillData) => ({
            id: skill.id,
            name: skill.name,
            description: skill.description,
          })),
          culture_specific_skills: cultureSpecificSkills.map((skill) => ({
            id: skill.id,
            name: skill.name,
            description: skill.description,
          })),
        };

        console.log("saveJOBData===========>>>>>>>>>>>>>>", requestData);
        const saveJobResponse = await saveJobDetails(requestData);

        if (saveJobResponse && saveJobResponse.data && saveJobResponse.data.success) {
          toastMessageSuccess(t(saveJobResponse.data.message));
          // Empty redux store
          dispatch(clearJobRequirement());
          dispatch(clearJobDetails());
          dispatch(clearSkillsData());
          router.replace(ROUTES.JOBS.ACTIVE_JOBS);
        } else {
          toastMessageError(t(saveJobResponse?.data?.message) || t("save_job_failed"));
        }
      }
    } catch (error) {
      console.error(t("save_job_error_log"), error);
      toastMessageError(isEditMode ? tGenerate("update_job_description_error") : t("save_job_unknown_error"));
    } finally {
      setIsLoading(false);
    }
  };
  console.log("cultureSpecificSkills", cultureSpecificSkills);

  /**
   * Downloads job requirement as PDF
   * @function handleDownloadPDF
   * @async
   * @returns {Promise<void>}
   * @throws {Error} If PDF generation fails
   * @description Generates and downloads job requirement content as PDF
   */
  // we will use later if need or remove
  // const handleDownloadPDF = async () => {
  //   if (isEditorEmpty(editorContent)) {
  //     toastMessageError(t("pdf_job_req_empty"));
  //     return;
  //   }

  //   try {
  //     // Show loading toast
  //     toast.loading(t("pdf_generating"));
  //     const generatePDFResponse = await generatePDF({
  //       jobTitle: jobDetails.title,
  //       editorContent: editorContent,
  //     });
  //     if (generatePDFResponse && generatePDFResponse.data && generatePDFResponse.data.success) {
  //       toast.dismiss();
  //       toastMessageSuccess(t(generatePDFResponse.data.message));
  //     } else {
  //       toast.dismiss();
  //       toastMessageError(t("pdf_generation_failed"));
  //     }
  //   } catch (error) {
  //     console.error("Error generating PDF:", error);
  //     toast.dismiss();
  //     toastMessageError(t("pdf_generation_failed"));
  //   }
  // };

  // Handle copy content
  const handleCopyContent = async () => {
    if (isEditorEmpty(editorContent)) {
      toastMessageError(t("copy_job_req_empty"));
      return;
    }

    try {
      // We'll implement two methods of copying to support both plain text and rich text

      // Method 1: Copy as rich text (HTML)
      // Create a blob with HTML mime type
      const htmlBlob = new Blob([editorContent], { type: "text/html" });
      // Create a temporary div to extract text content as fallback
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = editorContent;
      const textContent = tempDiv.innerText || tempDiv.textContent || "";
      const textBlob = new Blob([textContent], { type: "text/plain" });

      // Try the modern API first (which supports HTML)
      if (navigator.clipboard.write) {
        await navigator.clipboard.write([
          new ClipboardItem({
            "text/html": htmlBlob,
            "text/plain": textBlob,
          }),
        ]);
        toastMessageSuccess(t("copy_html_success"));
      } else {
        // Fallback to the text-only method
        await navigator.clipboard.writeText(textContent);
        toastMessageSuccess(t("copy_text_success"));
      }
    } catch (error) {
      console.error("Copy error:", error);

      // Last resort fallback - if the modern approach fails
      try {
        // Create element to copy from
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = editorContent;
        document.body.appendChild(tempDiv);

        // Select the content
        const range = document.createRange();
        range.selectNodeContents(tempDiv);
        const selection = window.getSelection();
        selection?.removeAllRanges();
        selection?.addRange(range);

        // Execute copy command
        document.execCommand("copy");

        // Clean up
        selection?.removeAllRanges();
        document.body.removeChild(tempDiv);

        toastMessageSuccess(t("copy_success"));
      } catch (fallbackError) {
        toastMessageError(t("copy_fail"));
        console.error(t("copy_error"), fallbackError);
      }
    }
  };

  return (
    <div className={style.job_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                {isEditMode ? (
                  <>
                    {jobTitle ? tGenerate("edit_job_description") : ""} <span>{jobTitle || ""}</span>
                  </>
                ) : (
                  <>
                    {jobDetails.title ? tGenerate("job_requirment_for") : ""} <span>{jobDetails?.title || ""}</span>
                  </>
                )}
              </h2>
              {editorContent && (
                <div className="d-flex">
                  <Button
                    className="clear-btn p-0 m-0"
                    title="Copy job requirement content"
                    onClick={() => handleCopyContent()}
                    disabled={!editorContent || isLoading}
                  >
                    <CopyIcon />
                  </Button>
                  <PdfGenerator content={editorContent} fileName={`job_requirement_${jobTitle}`} onLoadingChange={setIsLoading} />
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="inner-section">
          {/* Display message if no job requirement has been generated */}
          {!editorContent && !isEditorLoading ? (
            <div className="text-center">
              <p>{tGenerate("no_job_requirment_generated")}</p>
            </div>
          ) : isEditorLoading ? (
            /* Skeleton loader for SunEditor */
            <div className="editor-skeleton">
              {/* Toolbar skeleton */}
              <div className="skeleton-toolbar">
                <div className="skeleton-button"></div>
                <div className="skeleton-button"></div>
                <div className="skeleton-button"></div>
                <div className="skeleton-button"></div>
                <div className="skeleton-button"></div>
              </div>
              {/* Content skeleton */}
              <div className="skeleton-content">
                <div className="skeleton-paragraph"></div>
                <div className="skeleton-paragraph"></div>
                <div className="skeleton-paragraph"></div>
                <div className="skeleton-paragraph"></div>
                <div className="skeleton-paragraph"></div>
              </div>
            </div>
          ) : isEditorLoading ? (
            /* Skeleton loader for SunEditor */
            <div className="editor-skeleton">
              {/* Toolbar skeleton */}
              <div className="skeleton-toolbar">
                <div className="skeleton-button"></div>
                <div className="skeleton-button"></div>
                <div className="skeleton-button"></div>
                <div className="skeleton-button"></div>
                <div className="skeleton-button"></div>
              </div>
              {/* Content skeleton */}
              <div className="skeleton-content">
                <div className="skeleton-paragraph"></div>
                <div className="skeleton-paragraph"></div>
                <div className="skeleton-paragraph"></div>
                <div className="skeleton-paragraph"></div>
                <div className="skeleton-paragraph"></div>
              </div>
            </div>
          ) : (
            /* SunEditor component */
            <SunEditor
              setContents={editorContent}
              onChange={handleEditorChange}
              getSunEditorInstance={(sunEditor) => {
                // Store the SunEditor instance in our ref
                editorRef.current = sunEditor;
                // Check if content starts with our HTML marker
                if (editorContent.startsWith("HTML:")) {
                  setTimeout(() => {
                    try {
                      // Clear the editor first
                      sunEditor.setContents("");

                      // Extract the HTML content without the marker
                      const htmlContent = editorContent.substring(5);

                      // Insert as HTML rather than setting contents
                      sunEditor.insertHTML(htmlContent);

                      // Force editor to process the content properly
                      sunEditor.core.focus();
                    } catch (err) {
                      console.error("Error setting HTML content:", err);
                    }
                  }, 100);
                } else if (editorContent) {
                  setTimeout(() => {
                    try {
                      sunEditor.setContents(editorContent);
                    } catch (error) {
                      console.error("Error setting editor content:", error);
                    }
                  }, 100);
                }
              }}
              setOptions={{
                buttonList: SUN_EDITOR_BUTTON_LIST,
                minHeight: "550px",
                defaultStyle: "font-size: 16px; font-family: Arial, sans-serif;",
                formats: ["p", "div", "h1", "h2", "h3", "h4", "h5", "h6"],
                font: ["Arial", "Verdana", "Georgia", "Courier New", "Times New Roman"],
                fontSize: [10, 12, 14, 16, 18, 20, 22, 24, 28, 36, 48],
                toolbarContainer: "#editor-toolbar",
                attributesWhitelist: {
                  all: "style",
                  span: "style",
                },
              }}
            />
          )}
        </div>

        {editorContent && (
          <div className="button-align py-5">
            <Button className="primary-btn rounded-md" onClick={handleSave} disabled={!editorContent || isLoading}>
              {isEditMode ? "Update Job Description" : "Save Job Requirement"} {isLoading && <Loader />}
            </Button>
            <Button
              className="dark-outline-btn rounded-md"
              onClick={() => (isEditMode ? router.push(ROUTES.JOBS.ACTIVE_JOBS) : router.push(`${ROUTES.JOBS.HIRING_TYPE}`))}
            >
              {isEditMode ? t("back_to_jobs") : t("back_to_start")}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

export default JobEditor;
