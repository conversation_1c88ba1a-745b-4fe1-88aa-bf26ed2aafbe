/* eslint-disable react-hooks/exhaustive-deps */
import Button from "@/components/formElements/Button";
import { DEFAULT_LIMIT } from "@/constants/commonConstants";
import { setHasUnreadNotification, setNotificationsData } from "@/redux/slices/notificationSlice";
import { getNotifications, deleteAllNotifications, updateNotificationStatus } from "@/services/notificationServices/notificationService";
import { toastMessageError } from "@/utils/helper";
import { useTranslations } from "next-intl";
import React, { useEffect, useRef, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { useTranslate } from "@/utils/translationUtils";
import NoDataFoundIcon from "@/components/svgComponents/NoDataFoundIcon";

interface NotificationsProps {
  setIsNotificationOpen: (value: boolean) => void;
}

const Notifications = ({ setIsNotificationOpen }: NotificationsProps) => {
  const t = useTranslations();
  const translate = useTranslate();
  const tCommon = useTranslations("common");
  const [loading, setLoading] = useState<boolean>(true);
  const [offset, setOffset] = useState<number>(0);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const notificationRef = useRef<HTMLDivElement>(null);
  const dispatch = useDispatch();
  const { notifications } = useSelector((state: RootState) => state.notification);

  console.log("Notifications:--->>", notifications);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        notificationRef.current &&
        !notificationRef.current.contains(event.target as Node) &&
        (event.target as HTMLElement).id !== "notification-icon-id"
      ) {
        setIsNotificationOpen(false);
        dispatch(setHasUnreadNotification(false));
        dispatch(setNotificationsData(notifications.map((item) => ({ ...item, isWatched: 1 }))));
        updateNotificationStatus();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    dispatch(setNotificationsData([]));
    fetchNotifications(offset, true);
  }, []);

  const fetchNotifications = async (offset: number, reset = false) => {
    try {
      setLoading(true);
      if (reset) {
        dispatch(setNotificationsData([]));
      }
      const response = await getNotifications({ offset, limit: DEFAULT_LIMIT });

      if (response.data?.success) {
        console.log("Notifications fetched successfully:", response.data.data);

        dispatch(setNotificationsData(reset ? response.data.data : [...notifications, ...response.data.data]));
        setOffset(offset + response.data.data.length);
        setHasMore(response.data.data.length === DEFAULT_LIMIT);
        if (response.data.data.length < DEFAULT_LIMIT) {
          setHasMore(false);
        }
      } else {
        toastMessageError(translate(response?.data?.message));
      }
    } catch (error) {
      console.log(error);

      toastMessageError(translate("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  const handleClearAll = async () => {
    try {
      setLoading(true);
      const res = await deleteAllNotifications();

      if (res.data?.success) {
        dispatch(setNotificationsData([])); // Clear UI
      } else {
        toastMessageError(t("failed_to_delete_notifications"));
      }
    } catch {
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="notifications" ref={notificationRef}>
      <div className="header-content">
        <h3>{tCommon("notifications")}</h3>
        <Button onClick={notifications.length > 0 ? handleClearAll : undefined} className="clear-btn p-0">
          {tCommon("clear_all")}
        </Button>
      </div>

      <div className="notification-wrapper" id="notification-scroll-container">
        <InfiniteScroll
          dataLength={notifications.length}
          next={() => fetchNotifications(offset)}
          hasMore={hasMore}
          loader={
            <>
              {[...Array(4)].map((_, rowIndex) => (
                <div className="notification-item" key={rowIndex}>
                  <h4 style={{ margin: 0 }}>
                    <Skeleton height={17} width="100%" borderRadius={4} />
                  </h4>
                  <p>
                    <Skeleton height={14} width="80%" borderRadius={4} />
                  </p>
                  <p className="time">
                    <Skeleton height={10} width="20%" borderRadius={4} />
                  </p>
                </div>
              ))}
            </>
          }
          scrollableTarget="notification-scroll-container"
          endMessage={
            notifications.length > 0 && (
              <p className="p-2 text-center">
                <strong>{tCommon("no_more_notifications")}</strong>
              </p>
            )
          }
        >
          {notifications.length === 0 && !loading ? (
            <div className="p-2 text-center">
              <NoDataFoundIcon width={300} height={300} />
            </div>
          ) : (
            [...notifications]
              .sort((a, b) => new Date(b.createdTs).getTime() - new Date(a.createdTs).getTime())
              .map((item) => (
                <div key={item.id} className={`notification-item ${item.isWatched === 0 ? "unread" : ""}`}>
                  <h4 style={{ margin: 0 }}>{item.title}</h4>
                  <p>{item.description}</p>
                  <p className="time">
                    {new Date(item.createdTs).toLocaleString("en-US", {
                      dateStyle: "medium",
                      timeStyle: "short",
                      hour12: false,
                    })}
                  </p>
                </div>
              ))
          )}
        </InfiniteScroll>
      </div>
    </div>
  );
};

export default React.memo(Notifications);
