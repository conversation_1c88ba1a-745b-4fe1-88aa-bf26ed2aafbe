"use client";

import { ReactNode } from "react";
import { usePathname } from "next/navigation";
import HeaderWrapper from "@/components/header/HeaderWrapper";
import ROUTES from "@/constants/routes";

interface RouteAwareWrapperProps {
  children: ReactNode;
}

export default function RouteAwareWrapper({ children }: RouteAwareWrapperProps) {
  const pathname = usePathname();

  // Remove locale from pathname (e.g., /en/login -> /login)
  const cleanPath = pathname.replace(/^\/[a-z]{2}(?=\/|$)/, "") || "/";

  // Check if current route is a before-login route
  const isBeforeLoginRoute = cleanPath === ROUTES.HOME;

  return (
    <div className={`main-body${isBeforeLoginRoute ? " logout-header" : ""}`}>
      <HeaderWrapper />
      <div className="scroll-content">{children}</div>
    </div>
  );
}
