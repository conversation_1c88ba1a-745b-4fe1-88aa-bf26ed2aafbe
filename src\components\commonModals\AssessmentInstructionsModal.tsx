"use client";
import React, { FC, useState } from "react";
import Button from "../formElements/Button";
import InputWrapper from "../formElements/InputWrapper";
import Textbox from "../formElements/Textbox";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import Loader from "../loader/Loader";
import { toastMessageError } from "@/utils/helper";
import { verifyCandidateEmail } from "@/services/assessmentService";
import { createValidationSchema } from "@/validations/finalAssessmentValidations";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import Image from "next/image";
import InterviewInfoImg from "../../../public/assets/images/interview-info.png";
import { ASSESSMENT_INSTRUCTIONS } from "@/constants/commonConstants";
import { useTranslate } from "@/utils/translationUtils";

// Interface for the form data
interface AssessmentInstructionsFormData {
  email: string;
}

// Interface for component props
interface AssessmentInstructionsModalProps {
  finalAssessmentToken: string;
  onVerificationSuccess: (email: string, finalAssessmentId: number) => void;
  onClickCancel?: () => void;
}

export const AssessmentInstructionsModal: FC<AssessmentInstructionsModalProps> = ({ finalAssessmentToken, onVerificationSuccess, onClickCancel }) => {
  const t = useTranslate();
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationError, setVerificationError] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<AssessmentInstructionsFormData>({
    defaultValues: {
      email: "",
    },
    resolver: yupResolver(createValidationSchema(t)),
    mode: "onChange",
  });

  const onSubmit = async (data: AssessmentInstructionsFormData) => {
    if (!finalAssessmentToken) {
      toastMessageError(t("no_assessment_token_found"));
      return;
    }

    try {
      setIsVerifying(true);
      setVerificationError(null);

      const response = await verifyCandidateEmail({
        email: data.email,
        token: finalAssessmentToken,
      });

      if (response.data && response.data.success && response.data.data) {
        const { finalAssessmentId } = response.data.data;
        onVerificationSuccess(data.email, finalAssessmentId);
      } else {
        const errorMessage = t(response.data?.message || "failed_to_verify_email");
        toastMessageError(errorMessage);
        setVerificationError(errorMessage);
      }
    } catch (error) {
      console.error("Error verifying email:", error);
      toastMessageError(t("an_unexpected_error_occurred_while_verifying_email"));
      setVerificationError(t("an_unexpected_error_occurred_while_verifying_email"));
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered modal-lg">
        <div className="modal-content">
          <div className="modal-header text-start pb-0">
            <h4>
              Assessment <span>Instructions</span>
            </h4>
            <p className="m-0 textMd">Please read the following instructions carefully</p>
            {onClickCancel && (
              <Button className="modal-close-btn" onClick={onClickCancel}>
                <ModalCloseIcon />
              </Button>
            )}
          </div>
          <div className="modal-body position-relative">
            {/* assessment instructions image */}
            <div className="interview-info-img-container">
              <Image
                src={InterviewInfoImg}
                alt="InterviewInfoImg"
                className="interview-info-img"
                width={180}
                height={180}
                style={{ objectFit: "contain" }}
              />
            </div>

            <div className="interview-info">
              {ASSESSMENT_INSTRUCTIONS.map((instruction, index) => (
                <div className="info-item w-75 " key={index}>
                  <h4 className="info-title align-items-start">
                    <span className="dot mt-2" />
                    {instruction.title}
                  </h4>
                  <p className="fs-4">{instruction.content}</p>
                </div>
              ))}
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="mt-4">
              <div className="mb-3">
                <InputWrapper>
                  <InputWrapper.Label required>{t("email_address")}</InputWrapper.Label>
                  <Textbox name="email" control={control} placeholder={t("enter_your_email")} className="form-control" />
                  {errors.email?.message && <InputWrapper.Error message={errors.email?.message || verificationError || ""} />}
                </InputWrapper>
              </div>

              <div className="action-btn justify-content-end">
                {onClickCancel && (
                  <Button className="dark-outline-btn rounded-md" onClick={onClickCancel}>
                    Cancel
                  </Button>
                )}
                <Button type="submit" className="primary-btn rounded-md w-100 cursor-pointer " disabled={isVerifying || !isValid}>
                  <div className="d-flex align-items-center justify-content-center">
                    {isVerifying && <Loader />}
                    <span className={isVerifying ? "ms-2" : ""}>{isVerifying ? t("verifying") : t("start_assessment")}</span>
                  </div>
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssessmentInstructionsModal;
