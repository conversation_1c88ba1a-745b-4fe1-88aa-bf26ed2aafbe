"use client";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { useForm } from "react-hook-form";

import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import routes from "@/constants/routes";
import styles from "@/styles/auth.module.scss";
import logo from "../../../../public/assets/images/logo.svg";

import hidePassword from "../../../../public/assets/images/hide-password.svg";
import showPassword from "../../../../public/assets/images/show-password.svg";
import { SignUpFormValues, signUpValidationSchema } from "@/validations/authValidations";
import { yupResolver } from "@hookform/resolvers/yup";
import { checkUserOrgExist, sendVerificationEmail } from "@/services/authServices";
import { decryptInfo, encryptInfo, toastMessageError, toastMessageSuccess } from "@/utils/helper";
import { useRouter } from "next/navigation";
import { OTP_TYPE } from "@/constants/commonConstants";
import { useTranslate } from "@/utils/translationUtils";
import ROUTES from "@/constants/routes";

const PASSWORD_TYPE = {
  OLD: "old",
  NEW: "new",
};

const Signup = () => {
  const translate = useTranslate();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [sendVerificationLinkLoading, setSendVerificationLinkLoading] = useState(false);
  const [password, setPassword] = useState(true);
  const [userAlreadyExistWithoutOrg, setUserAlreadyExistWithoutOrg] = useState(false);
  const [passwordType, setPasswordType] = useState(PASSWORD_TYPE.NEW);
  const [encodedInfo, setEncodedInfo] = useState<string>("");
  // const [showDropdown, setShowDropdown] = useState(false);
  // const [location, setLocation] = useState<string>("");
  // const [placeId, setPlaceId] = useState<string>("");
  // const wrapperRef = useRef<HTMLInputElement>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<SignUpFormValues>({
    resolver: yupResolver<SignUpFormValues, unknown, unknown>(signUpValidationSchema(translate)),
    mode: "onChange",
  });

  // const locationName = watch("location");

  // const { predictions } = useAutoCompleteAddress(locationName);
  // below is the same as componentDidMount and componentDidUnmount
  // useEffect(() => {
  //   document.addEventListener("click", handleClickOutside, !showDropdown);
  //   return () => {
  //     document.removeEventListener("click", handleClickOutside, !showDropdown);
  //   };
  // }, []);

  // const handleClickOutside = (e: Event) => {
  //   if (
  //     e.target instanceof HTMLElement &&
  //     wrapperRef.current &&
  //     !wrapperRef.current.contains(e.target)
  //   ) {
  //     setShowDropdown(false);
  //   }
  // };

  // useEffect(() => {
  //   if (location) {
  //     fillAddress(location);
  //   }
  // }, [location]);

  // const fillAddress = async (location: string) => {
  //   const existingValues = await getValues();
  //   reset({
  //     ...existingValues,
  //     ...{
  //       location: location || "",
  //     },
  //   });
  //   setShowDropdown(false);
  // };

  const submit = async (data: SignUpFormValues) => {
    try {
      setLoading(true);
      const result = await checkUserOrgExist({
        organizationCode: data.organizationCode,
        organizationName: data.organizationName,
        firstName: data.firstName,
        email: data.email,
      });

      // save info in state
      const info = encryptInfo(
        JSON.stringify({
          ...data,
          type: OTP_TYPE.SIGNUP,
          passwordType: passwordType,
        })
      );
      const encodedInfo = encodeURIComponent(info);
      setEncodedInfo(encodedInfo);

      if (result?.data?.success) {
        // navigate to verify page
        router.push(`${routes.VERIFY}?info=${encodedInfo}&passwordType=${passwordType}`);
      } else {
        if (result?.data?.data?.userAlreadyExistWithoutOrg) {
          setUserAlreadyExistWithoutOrg(true);
        } else {
          toastMessageError(translate(result?.data?.message));
        }
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const sendVerificationLink = async () => {
    try {
      setSendVerificationLinkLoading(true);
      const data = decryptInfo(encodedInfo);
      const parsedData = JSON.parse(data);
      const result = await sendVerificationEmail({
        email: parsedData.email,
        type: OTP_TYPE.SIGNUP,
        name: parsedData.firstName,
      });
      console.log("sendVerificationLink", result);
      if (result?.data?.success) {
        toastMessageSuccess(translate(result?.data?.message));
        router.push(`${routes.VERIFY}?info=${encodedInfo}&passwordType=${passwordType}`);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSendVerificationLinkLoading(false);
    }
  };

  return (
    <div className={styles.auth_main}>
      <div className="container">
        <div className="row py-5">
          <div className={`${styles.user_auth_main} `}>
            <div className="container">
              <div className="row row-center">
                <div className={`${styles.hero_image} col-md-6`}>{/* Hero image could go here */}</div>
                <div className="col-md-6">
                  <div className={styles.form_main}>
                    <div className="text-center">
                      <Image src={logo} alt="logo" className={styles.logo} width={200} height={80} onClick={() => router.push(ROUTES.HOME)} />
                      <h1>{translate("create_account")}</h1>
                    </div>
                    <form onSubmit={handleSubmit((data) => submit(data))} className={styles.sign_up_scrollbar}>
                      <div className="row">
                        <div className="col-md-6">
                          {/* First Name */}
                          <InputWrapper>
                            <InputWrapper.Label htmlFor="firstName" required>
                              {translate("first_name")}
                            </InputWrapper.Label>
                            <Textbox
                              className="form-control"
                              control={control}
                              name="firstName"
                              type="text"
                              placeholder={translate("enter_your_first_name")}
                            />
                            <InputWrapper.Error message={errors?.firstName?.message || ""} />
                          </InputWrapper>
                        </div>

                        <div className="col-md-6">
                          {/* Last Name */}
                          <InputWrapper>
                            <InputWrapper.Label htmlFor="lastName" required>
                              {translate("last_name")}
                            </InputWrapper.Label>
                            <Textbox
                              className="form-control"
                              control={control}
                              name="lastName"
                              type="text"
                              placeholder={translate("enter_your_last_name")}
                            />
                            <InputWrapper.Error message={errors?.lastName?.message || ""} />
                          </InputWrapper>
                        </div>

                        <div className="col-md-12">
                          {/* Email */}
                          <InputWrapper>
                            <InputWrapper.Label htmlFor="email" required>
                              {translate("email")}
                            </InputWrapper.Label>
                            <Textbox
                              className="form-control"
                              control={control}
                              name="email"
                              type="text"
                              placeholder={translate("enter_your_email")}
                            />
                            <InputWrapper.Error message={errors?.email?.message || ""} />
                          </InputWrapper>
                        </div>
                        <div className="col-md-12">
                          {/* Password */}
                          <InputWrapper>
                            <InputWrapper.Label htmlFor="password" required>
                              {translate("password")}
                            </InputWrapper.Label>
                            <Textbox
                              className="form-control"
                              control={control}
                              name="password"
                              iconClass="icon-align"
                              align="right"
                              type={password ? "password" : "text"}
                              placeholder={translate("enter_your_password")}
                            >
                              <InputWrapper.Icon onClick={() => setPassword(!password)}>
                                <Image src={!password ? showPassword : hidePassword} alt="password-icon" />
                              </InputWrapper.Icon>
                            </Textbox>
                            <InputWrapper.Error message={errors?.password?.message || ""} />
                          </InputWrapper>
                        </div>
                        <div className="col-md-6">
                          {/* Organization Domain */}
                          <InputWrapper>
                            <InputWrapper.Label htmlFor="organizationName" required>
                              {translate("organization_name")}
                            </InputWrapper.Label>
                            <Textbox
                              className="form-control"
                              control={control}
                              name="organizationName"
                              type="text"
                              placeholder={translate("enter_organization_name")}
                            />
                            <InputWrapper.Error message={errors?.organizationName?.message || ""} />
                          </InputWrapper>
                        </div>
                        <div className="col-md-6">
                          {/* Org Code */}
                          <InputWrapper>
                            <InputWrapper.Label htmlFor="organizationCode" required>
                              {translate("organization_code")}
                            </InputWrapper.Label>
                            <Textbox
                              className="form-control"
                              control={control}
                              name="organizationCode"
                              type="text"
                              placeholder={translate("enter_org_code")}
                            />
                            <InputWrapper.Error message={errors?.organizationCode?.message || ""} />
                          </InputWrapper>
                        </div>
                        <div className="col-md-6">
                          {/* Organization Domain */}
                          <InputWrapper>
                            <InputWrapper.Label htmlFor="organizationDomain">{translate("website_url")}</InputWrapper.Label>
                            <Textbox
                              className="form-control"
                              control={control}
                              name="websiteURL"
                              type="text"
                              placeholder={translate("enter_website_url")}
                            />
                            <InputWrapper.Error message={errors?.websiteURL?.message || ""} />
                          </InputWrapper>
                        </div>

                        <div className="col-md-6">
                          {/* TIN Number */}
                          <InputWrapper>
                            <InputWrapper.Label htmlFor="tinNumber">{translate("tin_number")}</InputWrapper.Label>
                            <Textbox
                              className="form-control"
                              control={control}
                              name="tinNumber"
                              type="text"
                              placeholder={translate("enter_tin_number")}
                            />
                            <InputWrapper.Error message={errors?.tinNumber?.message || ""} />
                          </InputWrapper>
                        </div>
                        <div className="col-md-6">
                          {/* Branch Name */}
                          <InputWrapper>
                            <InputWrapper.Label htmlFor="branchName">{translate("branch_name")}</InputWrapper.Label>
                            <Textbox
                              className="form-control"
                              control={control}
                              name="branchName"
                              type="text"
                              placeholder={translate("enter_branch_name")}
                            />
                            <InputWrapper.Error message={errors?.branchName?.message || ""} />
                          </InputWrapper>
                        </div>
                        <div className="col-md-6">
                          {/* Branch Code */}
                          <InputWrapper>
                            <InputWrapper.Label htmlFor="branchCode">{translate("branch_code")}</InputWrapper.Label>
                            <Textbox
                              className="form-control"
                              control={control}
                              name="branchCode"
                              type="text"
                              placeholder={translate("enter_branch_code")}
                            />
                            <InputWrapper.Error message={errors?.branchCode?.message || ""} />
                          </InputWrapper>
                        </div>
                        <div className="col-md-12">
                          {/* Location */}
                          <InputWrapper>
                            <InputWrapper.Label htmlFor="location" required>
                              {translate("location")}
                            </InputWrapper.Label>
                            <Textbox
                              className="form-control"
                              control={control}
                              name="location"
                              type="text"
                              placeholder={translate("enter_location")}
                            />
                            <InputWrapper.Error message={errors?.location?.message || ""} />
                          </InputWrapper>

                          {/* <InputWrapper
                            placeholder={translate("type_your_city_&_country")}
                            type="text"
                            name="location"
                            label={translate("your_location")}
                            importantLabel="*"
                            icon={true}
                            control={control}
                            error={errors.location}
                            onKeyDown={() => setShowDropdown(true)}
                            onChange={(e) => {
                              if (e.target.value !== location) {
                                setPlaceId("");
                              }
                              if (!e.target.value) {
                                setLocation("");
                                setPlaceId("");
                              }
                            }}
                          />
                          {showDropdown && predictions.length ? (
                            <div ref={wrapperRef}>
                              <ul className="list-group list-hover">
                                {predictions.map((item) => (
                                  <li
                                    key={""}
                                    className="list-group-item point"
                                    onClick={() => {
                                      setLocation(item.description);
                                      setPlaceId(item.place_id);
                                    }}
                                  >
                                    {item?.description}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          ) : null} */}
                        </div>
                      </div>

                      <Button type="submit" disabled={loading} loading={loading} className="primary-btn rounded-md w-100 mt-4">
                        {translate("sign_up")}
                      </Button>
                      <div className="text-center mt-3">
                        <p>
                          {translate("already_have_account")}{" "}
                          {loading ? (
                            translate("login")
                          ) : (
                            <Link href={routes.LOGIN} className={styles.signup_link}>
                              {translate("login")}
                            </Link>
                          )}
                        </p>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
              {userAlreadyExistWithoutOrg ? (
                <div className="modal theme-modal show-modal">
                  <div className="modal-dialog modal-dialog-centered">
                    <div className="modal-content">
                      <div className="modal-header justify-content-center pb-3">
                        <h2>{translate("choose_password")}</h2>
                        {/* <p className="textMd w100">{translate("The user is already exist without organization. Please choose a password.")}</p>
                        <Button className="modal-close-btn" onClick={() => ""}>
                          <ModalCloseIcon />
                        </Button> */}
                      </div>
                      <div className="modal-body pt-0">
                        <div className="mb-4">
                          <div className="radio-wrapper radio-sm signup-radio">
                            <div className="radio-box" onClick={() => setPasswordType(PASSWORD_TYPE.OLD)}>
                              <input
                                className="form-check-input"
                                type="radio"
                                name="passwordType"
                                value={PASSWORD_TYPE.OLD}
                                checked={passwordType === PASSWORD_TYPE.OLD ? true : false}
                                onChange={() => setPasswordType(PASSWORD_TYPE.OLD)}
                              />
                              <label className="radio-label">{translate("continue_with_existing_password")}</label>
                            </div>
                            <div className="radio-box" onClick={() => setPasswordType(PASSWORD_TYPE.NEW)}>
                              <input
                                className="form-check-input"
                                type="radio"
                                name="passwordType1"
                                value={PASSWORD_TYPE.NEW}
                                checked={passwordType === PASSWORD_TYPE.NEW ? true : false}
                                onChange={() => setPasswordType(PASSWORD_TYPE.NEW)}
                              />
                              <label className="radio-label">{translate("continue_with_new_password")}</label>
                            </div>
                          </div>
                        </div>
                        <div className="action-btn">
                          <Button
                            loading={sendVerificationLinkLoading}
                            disabled={sendVerificationLinkLoading || !passwordType}
                            onClick={() => sendVerificationLink()}
                            className="primary-btn rounded-md w-100"
                          >
                            {translate("continue")}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : null}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Signup;
