import React from "react";

function CalendarIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="26" height="26" viewBox="0 0 32 32" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M27.5554 13.2061H3.79004C3.23804 13.2061 2.79004 12.7581 2.79004 12.2061C2.79004 11.6541 3.23804 11.2061 3.79004 11.2061H27.5554C28.1074 11.2061 28.5554 11.6541 28.5554 12.2061C28.5554 12.7581 28.1074 13.2061 27.5554 13.2061"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21.602 18.4131C21.05 18.4131 20.5967 17.9651 20.5967 17.4131C20.5967 16.8611 21.038 16.4131 21.59 16.4131H21.602C22.154 16.4131 22.602 16.8611 22.602 17.4131C22.602 17.9651 22.154 18.4131 21.602 18.4131"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.685 18.4131C15.133 18.4131 14.6797 17.9651 14.6797 17.4131C14.6797 16.8611 15.121 16.4131 15.673 16.4131H15.685C16.237 16.4131 16.685 16.8611 16.685 17.4131C16.685 17.9651 16.237 18.4131 15.685 18.4131"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.75569 18.4131C9.20369 18.4131 8.74902 17.9651 8.74902 17.4131C8.74902 16.8611 9.19169 16.4131 9.74369 16.4131H9.75569C10.3077 16.4131 10.7557 16.8611 10.7557 17.4131C10.7557 17.9651 10.3077 18.4131 9.75569 18.4131"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21.602 23.5947C21.05 23.5947 20.5967 23.1467 20.5967 22.5947C20.5967 22.0427 21.038 21.5947 21.59 21.5947H21.602C22.154 21.5947 22.602 22.0427 22.602 22.5947C22.602 23.1467 22.154 23.5947 21.602 23.5947"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.685 23.5947C15.133 23.5947 14.6797 23.1467 14.6797 22.5947C14.6797 22.0427 15.121 21.5947 15.673 21.5947H15.685C16.237 21.5947 16.685 22.0427 16.685 22.5947C16.685 23.1467 16.237 23.5947 15.685 23.5947"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.75569 23.5947C9.20369 23.5947 8.74902 23.1467 8.74902 22.5947C8.74902 22.0427 9.19169 21.5947 9.74369 21.5947H9.75569C10.3077 21.5947 10.7557 22.0427 10.7557 22.5947C10.7557 23.1467 10.3077 23.5947 9.75569 23.5947"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21.0586 7.72101C20.5066 7.72101 20.0586 7.27301 20.0586 6.72101V2.33301C20.0586 1.78101 20.5066 1.33301 21.0586 1.33301C21.6106 1.33301 22.0586 1.78101 22.0586 2.33301V6.72101C22.0586 7.27301 21.6106 7.72101 21.0586 7.72101"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.2861 7.72101C9.73413 7.72101 9.28613 7.27301 9.28613 6.72101V2.33301C9.28613 1.78101 9.73413 1.33301 10.2861 1.33301C10.8381 1.33301 11.2861 1.78101 11.2861 2.33301V6.72101C11.2861 7.27301 10.8381 7.72101 10.2861 7.72101"
      />
      <mask id="mask0_11055_2251" style={{ maskType: "luminance" }} maskUnits="userSpaceOnUse" x="2" y="3" width="27" height="27">
        <path fillRule="evenodd" clipRule="evenodd" d="M2.66699 3.43848H28.667V29.9997H2.66699V3.43848Z" fill="white" />
      </mask>
      <g mask="url(#mask0_11055_2251)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.0273 5.43945C6.57002 5.43945 4.66602 7.28345 4.66602 10.6315V22.6968C4.66602 26.1181 6.57002 28.0008 10.0273 28.0008H21.3047C24.762 28.0008 26.666 26.1528 26.666 22.7981V10.6315C26.6713 8.98479 26.2287 7.70479 25.35 6.82479C24.446 5.91812 23.0527 5.43945 21.3167 5.43945H10.0273ZM21.3047 30.0008H10.0273C5.48735 30.0008 2.66602 27.2021 2.66602 22.6968V10.6315C2.66602 6.19412 5.48735 3.43945 10.0273 3.43945H21.3167C23.5953 3.43945 25.4794 4.12212 26.766 5.41145C28.0154 6.66612 28.6727 8.47012 28.666 10.6341V22.7981C28.666 27.2408 25.8447 30.0008 21.3047 30.0008V30.0008Z"
        />
      </g>
    </svg>
  );
}

export default CalendarIcon;
