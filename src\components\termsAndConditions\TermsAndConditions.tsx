import Head from "next/head";
import Link from "next/link";
import ROUTES from "@/constants/routes";
import Image from "next/image";
import Logo from "../../../public/assets/images/logo.svg";
import { useAuth } from "@/hooks/useAuth";

const TermsAndConditions = () => {
  const { isAuthenticated } = useAuth();

  return (
    <>
      <Head>
        <title>Terms and Conditions</title>
        <meta name="description" content="" key="desc" />
        <meta name="keywords" content=""></meta>
      </Head>
      <div className="logo-header text-center">
        <Link className="navbar-brand" href={isAuthenticated ? ROUTES.DASHBOARD : ROUTES.HOME}>
          <Image src={Logo} alt="logo" width={640} height={320} />
        </Link>
      </div>
      <section className="static-page">
        <div className="container">
          <h1>STRATUM 9 – Terms Conditions</h1>
          <h3>1. Acceptance of Terms</h3>
          <p>
            By accessing or using the S9 InnerView platform (“Platform”), you agree to be bound by these Terms and Conditions. If you do not agree,
            you must not use the Platform.
          </p>
          <h3>2. No Guarantee of Outcomes</h3>
          <p>
            STRATUM 9 does not guarantee the success of any hiring, employment, or performance outcomes resulting from the use of this Platform.
            Hiring decisions remain solely the responsibility of the organization.
          </p>
          <h3>3. Compliance with Employment Laws</h3>
          <p>
            Users are solely responsible for ensuring that their use of the Platform complies with all applicable employment, labor, data privacy, and
            anti-discrimination laws. STRATUM 9 does not provide legal, compliance, or regulatory advice. The Platform’s features are provided as
            decision-support tools only, and are not a substitute for independent compliance or HR/legal review.
          </p>
          <h3>4. Anti-Discrimination &amp; Ethical Use</h3>
          <p>
            Users agree not to use the Platform to discriminate based on race, color, religion, sex, gender identity, sexual orientation, national
            origin, age, disability, or other protected status under applicable law. STRATUM 9 expressly disclaims liability for discriminatory,
            unethical, or unlawful use of its Platform by any user
          </p>
          <h3>5. Candidate Data &amp; Privacy</h3>
          <p>
            Organizations are solely responsible for obtaining any required consents from candidates before uploading or processing candidate data on
            the Platform. STRATUM 9 provides reasonable safeguards but does not guarantee against unauthorized access, misuse, or data loss. Users
            agree to comply with applicable data protection regulations.
          </p>
          <h3>6. Independent Decision-Making</h3>
          <p>
            AI-driven insights, interview questions, analyses, and recommendations provided by the Platform are informational only. Final hiring
            decisions rest solely with the employer. STRATUM 9 is not the “employer of record” for any candidate.
          </p>
          <h3>7. Limitation of Liability</h3>
          <p>
            To the fullest extent permitted by law, STRATUM 9 shall not be liable for any damages, losses, or claims arising from the use or misuse of
            the Platform. Users agree to indemnify and hold harmless STRATUM 9, its affiliates, officers, and employees from any claims arising from
            hiring practices or candidate evaluations conducted using the Platform.
          </p>
          <h3>8. No Professional Advice</h3>
          <p>
            Nothing provided by STRATUM 9 constitutes legal, financial, medical, or professional advice. Users are encouraged to consult with
            qualified professionals for compliance, HR, or legal matters.
          </p>
          <h3>9. Intellectual Property</h3>
          <p>
            All intellectual property in the Platform, including AI models, tools, branding, and content, remains the property of STRATUM 9. Users are
            granted a limited, non-transferable license to use the Platform in accordance with these Terms.
          </p>
          <h3>10. Termination of Use</h3>
          <p>STRATUM 9 may suspend or terminate access to the Platform at any time for violation of these Terms or unlawful use.</p>
          <h3>11. Governing Law &amp; Jurisdiction</h3>
          <p>These Terms are governed by the laws of [Insert Jurisdiction]. Any disputes shall be resolved in the courts of [Insert Jurisdiction].</p>
          <h3>12. Amendments</h3>
          <p>
            STRATUM 9 reserves the right to modify or update these Terms at any time. Continued use of the Platform after changes are posted
            constitutes acceptance of the revised Terms.
          </p>
        </div>
      </section>
    </>
  );
};

export default TermsAndConditions;
