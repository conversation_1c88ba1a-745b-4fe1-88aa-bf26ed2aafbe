"use client";

import React from "react";
import styles from "./questionGeneratorLoader.module.css";

interface Props {
  show: boolean;
}

const CalenderLoader: React.FC<Props> = ({ show }) => {
  if (!show) return null;

  return (
    <div className={styles.question_generator_loader_overlay}>
      <div className={styles.loader_wrapper}>
        <div className={styles.loader_container}>
          <span className={styles.loader}></span>
        </div>
        <div className={styles.loader_text}>Loading Interviews...</div>
      </div>
    </div>
  );
};

export default CalenderLoader;
