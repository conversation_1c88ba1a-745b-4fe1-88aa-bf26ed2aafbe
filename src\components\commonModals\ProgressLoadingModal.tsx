"use client";
import React, { FC, useState, useEffect } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";

interface LoadingModalProps {
  isOpen: boolean;
  onClose?: () => void;
  title?: string;
  messages: string[];
  messageInterval?: number;
  showCloseButton?: boolean;
}

const LoadingModal: FC<LoadingModalProps> = ({
  isOpen,
  onClose,
  title = "Processing...",
  messages,
  messageInterval = 2000,
  showCloseButton = false,
}) => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);

  // Dynamic message rotation effect
  useEffect(() => {
    if (!isOpen || !messages || messages.length <= 1) {
      return;
    }

    const interval = setInterval(() => {
      setCurrentMessageIndex((prevIndex) => (prevIndex + 1) % messages.length);
    }, messageInterval);

    return () => clearInterval(interval);
  }, [isOpen, messages, messageInterval]);

  // Reset message index when modal opens
  useEffect(() => {
    if (isOpen) {
      setCurrentMessageIndex(0);
    }
  }, [isOpen]);

  // Add body class to prevent scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("modal-open");
    } else {
      document.body.classList.remove("modal-open");
    }

    return () => {
      document.body.classList.remove("modal-open");
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const getCurrentMessage = () => {
    if (messages && messages.length > 0) {
      return messages[currentMessageIndex];
    }
    return "Processing...";
  };

  return (
    <>
      <div className="modal theme-modal show-modal">
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-header justify-content-center pb-0">
              <h2 className="m-0">{title}</h2>
              {showCloseButton && onClose && (
                <Button className="modal-close-btn" onClick={onClose}>
                  <ModalCloseIcon />
                </Button>
              )}
            </div>
            <div className="modal-body">
              <div className="animated-text-with-loader">
                <div className="loading-text-container">
                  <p className="loading-text">{getCurrentMessage()}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default LoadingModal;
