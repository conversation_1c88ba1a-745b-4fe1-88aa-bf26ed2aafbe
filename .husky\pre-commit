#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

echo '🚀 Running pre-commit code quality checks...'

# Run type checking
echo '🔍 Running TypeScript type check...'
npm run type-check ||
(
   echo '❌ TypeScript type check failed. Fix type errors before committing.'
   false
)
echo '✅ TypeScript types looking good!'
npm run prettier-fix ||
(
    echo '🤢🤮🤢🤮. Prettier fix Failed. You will have to fix prettier issues manually';
    true;
)
echo 'Running prettier fix on all files'

npm run lint-fix ||
(
    echo '🤢🤮🤢🤮. Linting fix Failed. You will have to fix linting issues manually';
    true;
)
echo 'Running lint fix on all files'

# Check Prettier standards
npm run prettier-check ||
(
    echo '🤢🤮🤢🤮. Prettier Check Failed. Run npm run format, add changes, and try the commit again.';
    exit 1;
)
echo '✅ Prettier standards looking good!'

# Check ESLint Standards
npm run lint-check ||
(
    echo '😤🏀👋😤 ESLint Check Failed. Make the required changes listed above, add changes, and try the commit again.'
    exit 1;
)
echo '✅ ESLint standards looking good!'


# If everything passes... Now we can commit
echo '✅✅✅✅ You win this time... I am committing this now. ✅✅✅✅'
exit 0;