"use client";
import React, { FC, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";

import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import InputWrapper from "../formElements/InputWrapper";
import Textarea from "../formElements/Textarea";
import { IAddInterviewSkillQuestion, IUpdateInterviewSkillQuestion } from "@/interfaces/interviewInterfaces";
import { ModalMode } from "../views/conductInterview/PreInterviewQuestionsOverview";
import { addUpdateQuestionValidation } from "@/validations/interviewValidations";
import { useTranslate } from "@/utils/translationUtils";

interface IProps {
  onClickCancel: () => void;
  disabled?: boolean;
  mode: (typeof ModalMode)[keyof typeof ModalMode];
  initialData?: IAddInterviewSkillQuestion | IUpdateInterviewSkillQuestion;
  onSubmit: (data: IAddInterviewSkillQuestion | IUpdateInterviewSkillQuestion) => void;
  isLoading?: boolean;
}

const AddUpdateQuestionModal: FC<IProps> = ({ onClickCancel, mode = ModalMode.ADD, initialData, onSubmit, isLoading = false }) => {
  const t = useTranslate();
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      question: initialData?.question || "",
    },
    resolver: yupResolver(addUpdateQuestionValidation(t)),
  });

  useEffect(() => {
    if (initialData?.question) {
      reset({ question: initialData.question });
    }
  }, []);

  const processSubmit = (data: { question: string }) => {
    if (mode === ModalMode.ADD && initialData) {
      // For adding a new question
      const addData: IAddInterviewSkillQuestion = {
        ...(initialData as IAddInterviewSkillQuestion),
        question: data.question,
      };
      onSubmit(addData);
    } else if (mode === ModalMode.UPDATE && initialData) {
      // For updating an existing question
      const updateData: IUpdateInterviewSkillQuestion = {
        ...(initialData as IUpdateInterviewSkillQuestion),
        question: data.question,
      };
      onSubmit(updateData);
    }
  };

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header pb-0">
            <h2 className="mb-4 mt-0">{mode === ModalMode.ADD ? t("add_question") : t("update_question")}</h2>

            <Button className="modal-close-btn" onClick={onClickCancel}>
              <ModalCloseIcon />
            </Button>
          </div>
          <div className="modal-body pt-0">
            {/* qualification-card */}

            <form className="text-left">
              <InputWrapper>
                <Textarea className="form-control" control={control} name="question" rows={5} placeholder={t("enter_question")} required></Textarea>
                <InputWrapper.Error message={errors?.question?.message || ""} />
              </InputWrapper>
            </form>
            <div className="button-align">
              <Button
                className="primary-btn rounded-md w-100"
                onClick={handleSubmit(processSubmit)}
                type="submit"
                loading={isLoading}
                disabled={isLoading}
              >
                {mode === ModalMode.ADD ? t("add_question") : t("update_question")}
              </Button>
              {/* <Button className="dark-outline-btn rounded-md w-100" disabled={isLoading} onClick={onClickCancel}>
                {t("cancel")}
              </Button> */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default AddUpdateQuestionModal;
