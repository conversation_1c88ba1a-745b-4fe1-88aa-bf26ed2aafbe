import { HTMLAttributes } from "react";
import { Control, Controller, FieldValues, Path } from "react-hook-form";
import Loader from "../loader/Loader";

interface SelectProps<T extends FieldValues> extends HTMLAttributes<HTMLSelectElement> {
  name: Path<T>;
  placeholder?: string;
  disabled?: boolean;
  control: Control<T>;
  options: Array<{ label: string; value: string | number }>;
  isLoading?: boolean;
}

export default function Select<T extends FieldValues>({ options, name, control, disabled, placeholder, isLoading, ...props }: SelectProps<T>) {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <select {...props} disabled={disabled} value={field.value} onChange={field.onChange} aria-label="">
          {/* <option value="" disabled> */}
          <option value="">{placeholder}</option>
          {isLoading ? (
            <option value="0000">
              <Loader />
            </option>
          ) : (
            options.map((data) => (
              <option key={data.value} value={data.value}>
                {data.label}
              </option>
            ))
          )}
        </select>
      )}
    />
  );
}
