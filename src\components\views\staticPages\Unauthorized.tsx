"use client";
import Button from "@/components/formElements/Button";
import UnauthorizedIcon from "@/components/svgComponents/UnauthorizedIcon";
import ROUTES from "@/constants/routes";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import Logo from "../../../../public/assets/images/logo.svg";
import { useAuth } from "@/hooks/useAuth";
import { useTranslate } from "@/utils/translationUtils";

const Unauthorized = () => {
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  const translate = useTranslate();

  return (
    <>
      <div className="logo-header text-center">
        <Link className="navbar-brand" href={isAuthenticated ? ROUTES.DASHBOARD : ROUTES.HOME}>
          <Image src={Logo} alt="logo" width={640} height={320} />
        </Link>
      </div>
      <section className="static-page">
        <div className="container text-center py-5">
          <div className="error-page-container">
            <div className="svg-container mb-4">
              <UnauthorizedIcon />
            </div>
            <h1 className="color-primary mb-3">{translate("access_denied")}</h1>
            <p className="mb-5">{translate("access_denied_desc")}</p>
            <Button onClick={() => router.replace(ROUTES.DASHBOARD)} className="primary-outline-btn rounded-md m-auto">
              {translate("return_to_dashboard")}
            </Button>
          </div>
        </div>
      </section>
    </>
  );
};

export default Unauthorized;
