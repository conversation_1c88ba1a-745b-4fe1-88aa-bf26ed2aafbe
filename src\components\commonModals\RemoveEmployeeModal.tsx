"use client";
import React, { FC, useState } from "react";
import <PERSON>ton from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import { EmployeeInterface } from "@/interfaces/employeeInterface";
import Loader from "../loader/Loader";
import { useTranslations } from "next-intl";
import { deleteEmployee } from "@/services/employeeService";

interface RemoveEmployeeModalProps {
  onClickCancel: () => void;
  onSubmitSuccess: (message: string) => void;
  disabled?: boolean;
  employee: EmployeeInterface | null;
}

const RemoveEmployeeModal: FC<RemoveEmployeeModalProps> = ({ onClickCancel, onSubmitSuccess, disabled = false, employee }) => {
  const t = useTranslations();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const onSubmit = async () => {
    if (!employee) return;

    try {
      setIsSubmitting(true);
      setSubmitError(null);

      // Call the deleteEmployee service function
      const response = await deleteEmployee(employee.id);

      const result = response.data;

      if (result && result.success) {
        // Call onSubmitSuccess with the success message
        const successMessage = t("employee_delete_success", { name: `${employee.firstName} ${employee.lastName}`.trim() });
        onSubmitSuccess(successMessage);
        onClickCancel();
      } else {
        const errorMessage = result?.message || t("failed_employee_delete_operation");
        setSubmitError(errorMessage);
      }
    } catch (error) {
      console.error(error);
      setSubmitError(t("unexpected_error"));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header justify-content-center pb-0">
            <h2 className="m-0">{t("remove_employee")}</h2>
            <Button className="modal-close-btn" onClick={onClickCancel} disabled={isSubmitting}>
              <ModalCloseIcon />
            </Button>
          </div>
          <div className="modal-body">
            <div>
              <p className="text-center mb-4">
                {t("confirm_delete_employee", {
                  name: employee ? `${employee.firstName} ${employee.lastName}`.trim() : "",
                  role: employee?.selectedRole?.name || "",
                })}
              </p>
              {submitError && (
                <div className="alert alert-danger mb-3" role="alert">
                  {submitError}
                </div>
              )}

              <div className="button-align mt-4">
                <Button type="button" className="danger-btn rounded-md w-100" onClick={onSubmit} disabled={isSubmitting || disabled || !employee}>
                  <div className="d-flex align-items-center justify-content-center">
                    {isSubmitting && <Loader />}
                    <span className={isSubmitting ? "ms-2" : ""}>{t("remove_employee")}</span>
                  </div>
                </Button>
                <Button type="button" className="dark-outline-btn rounded-md w-100" onClick={onClickCancel} disabled={isSubmitting || disabled}>
                  {t("cancel")}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RemoveEmployeeModal;
