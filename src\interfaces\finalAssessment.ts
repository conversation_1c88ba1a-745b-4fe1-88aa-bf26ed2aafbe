export interface IFinalAssessmentModalProps {
  onClickCancel: () => void;
  onSubmitSuccess?: () => void;
  finalAssessmentId: number;
  jobApplicationId: number;
  isFromCompleteButton?: boolean;
  disabled?: boolean;
}

// Interfaces for assessment data
export interface Option {
  id: string;
  text: string;
}

export interface QuestionOptions {
  options: Option[];
}

export interface Question {
  id: number;
  question: string;
  questionType: "mcq" | "true_false";
  skillId: number;
  skillTitle: string;
  options: QuestionOptions;
  correctAnswer: string;
  applicantAnswer: string | null;
}

export interface IQuestionData {
  finalAssessmentId: number;
  skillId: number;
  question: string;
  questionType: "mcq" | "true_false";
  options: QuestionOptions;
  correctAnswer: string;
  applicantAnswer: string | null;
  id: number;
  createdTs: string;
  updatedTs: string;
  skillTitle: string;
}

export interface QuestionGroup {
  type: string;
  questions: Question[];
}

export interface AssessmentData {
  assessmentId: number;
  isAssessmentSubmitted: boolean;
  isAssessmentShared: boolean;
  questionGroups: QuestionGroup[];
}

export interface QuestionOption {
  id: string;
  text: string;
}

export interface AddQuestionFormData {
  question: string;
  questionType: "mcq" | "true_false";
  options: QuestionOption[];
  correctAnswer: string;
}

export interface AddQuestionModalProps {
  onClickCancel: () => void;
  onSubmitSuccess: () => void;
  skillId: number;
  skillTitle: string;
  finalAssessmentId: number;
}

export interface ICreateAssessmentQuestionRequest {
  finalAssessmentId: number;
  question: string;
  questionType: string;
  skillId: number;
  options: {
    options: Array<{
      id: string;
      text: string;
    }>;
  };
  correctAnswer: string;
}
export interface IAssessmentSubmission {
  finalAssessmentId: number; // Encrypted assessment ID
  candidateName?: string; // Made optional
  candidateEmail: string;
  answers: {
    questionId: number;
    answer: string;
  }[];
}

/**
 * Interface for candidate answer submission
 */
export interface ICandidateAnswerSubmission {
  finalAssessmentId: number;
  answers: Array<{
    questionId: number;
    selectedAnswer: string;
  }>;
  candidateInfo?: {
    name?: string;
    email: string;
    phone?: string;
  };
}

export interface IShareAssessmentRequest {
  finalAssessmentId: number;
  assessmentLink: string;
  jobApplicationId: number;
}
