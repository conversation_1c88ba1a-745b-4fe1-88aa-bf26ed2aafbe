"use client";

import { useEffect, useState } from "react";
import { NETWORK_STATUS } from "@/constants/commonConstants";

export const useInternetConnection = () => {
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    // Just track the online/offline state

    // Initial check
    setIsOnline(navigator.onLine);

    const handleOnline = () => {
      console.log("Internet restored");
      setIsOnline(true);
    };

    const handleOffline = () => {
      console.log("Internet lost===================>");
      setIsOnline(false);
      // No redirect needed - InternetWrapper will show no-internet page
    };

    window.addEventListener(NETWORK_STATUS.ONLINE, handleOnline);
    window.addEventListener(NETWORK_STATUS.OFFLINE, handleOffline);

    return () => {
      window.removeEventListener(NETWORK_STATUS.ONLINE, handleOnline);
      window.removeEventListener(NETWORK_STATUS.OFFLINE, handleOffline);
    };
  }, []); // Remove router and pathname dependencies

  return { isOnline };
};
