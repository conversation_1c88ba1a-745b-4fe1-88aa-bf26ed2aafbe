"use client";

import React from "react";
import AgoraRTC, { AgoraRTCProvider } from "agora-rtc-react";
import CandidateJoin from "./CandidateJoin";

const CandidateJoinWithAgora = () => {
  // Create the Agora client only on the client side
  return (
    <AgoraRTCProvider client={AgoraRTC.createClient({ mode: "rtc", codec: "vp8" })}>
      <CandidateJoin />
    </AgoraRTCProvider>
  );
};

export default CandidateJoinWithAgora;
