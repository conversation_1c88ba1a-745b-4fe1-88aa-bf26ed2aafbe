import { Permission } from "@/interfaces/authInterfaces";
import { useSelector } from "react-redux";
import { AuthState } from "@/redux/slices/authSlice";

/**
 * Hook to get user permissions from Redux store
 * @returns Array of permissions
 */
export function useUserPermissions() {
  return useSelector((state: { auth: AuthState }) => state.auth.permissions || []);
}

/**
 * Custom hook that checks if the current user has a specific permission
 * @param permissionToCheck The permission slug to check for
 * @returns boolean indicating if the user has the permission
 */
export function useHasPermission(permissionToCheck: string): boolean {
  const permissions = useUserPermissions();
  return hasPermission(permissions, permissionToCheck);
}

/**
 * Checks if a user has a specific permission
 * @param permissions Array of Permission objects or permission strings
 * @param permissionToCheck The permission slug to check for
 * @returns boolean indicating if the user has the permission
 */
export function hasPermission(permissions: Permission[] | string[], permissionToCheck: string): boolean {
  if (!permissions || permissions.length === 0) {
    return false;
  }
  return (permissions as string[]).includes(permissionToCheck);
}
