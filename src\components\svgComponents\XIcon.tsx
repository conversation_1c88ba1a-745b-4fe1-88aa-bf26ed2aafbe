import React, { FC } from "react";

const XIcon: FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" fill="none">
      <rect x="0.666667" y="0.666667" width="62.6667" height="62.6667" rx="31.3333" stroke="white" strokeOpacity="0.22" strokeWidth="1.33333" />
      <g clipPath="url(#clip0_630_2379)">
        <path
          d="M34.9786 29.5498L46.6352 16H43.8729L33.7515 27.7651L25.6676 16H16.3438L28.5682 33.7909L16.3438 48H19.1061L29.7946 35.5756L38.3318 48H47.6557L34.9779 29.5498H34.9786ZM31.1951 33.9477L29.9565 32.1761L20.1015 18.0795H24.3443L32.2975 29.4559L33.5361 31.2275L43.8742 46.0151H39.6314L31.1951 33.9484V33.9477Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_630_2379">
          <rect width="32" height="32" fill="white" transform="translate(16 16)" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default XIcon;
