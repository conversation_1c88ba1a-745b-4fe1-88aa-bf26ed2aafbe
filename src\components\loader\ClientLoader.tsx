"use client";
import React from "react";

interface ClientLoaderProps {
  size?: "sm" | "md" | "lg";
  color?: string;
  fullScreen?: boolean;
  text?: string;
}

const ClientLoader: React.FC<ClientLoaderProps> = ({ size = "md", color = "primary", fullScreen = false, text = "Loading..." }) => {
  const getSpinnerSize = () => {
    switch (size) {
      case "sm":
        return "spinner-border-sm";
      case "lg":
        return "spinner-border spinner-border-lg";
      default:
        return "spinner-border";
    }
  };

  const getSpinnerColor = () => {
    return `text-${color}`;
  };

  if (fullScreen) {
    return (
      <div className="loader-overlay">
        <div className="loader-container">
          <div className={`${getSpinnerSize()} ${getSpinnerColor()}`} role="status">
            <span className="visually-hidden">{text}</span>
          </div>
          {text && <p className="mt-2">{text}</p>}
        </div>
      </div>
    );
  }

  return (
    <div className="d-flex align-items-center justify-content-center">
      <div className={`${getSpinnerSize()} ${getSpinnerColor()}`} role="status">
        <span className="visually-hidden">{text}</span>
      </div>
      {text && <span className="ms-2">{text}</span>}
    </div>
  );
};

export default ClientLoader;
