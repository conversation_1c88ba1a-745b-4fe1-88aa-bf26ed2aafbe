// src/components/commonModals/CalenderEventModal.tsx
"use client";
import React, { FC, useEffect, useRef, useState } from "react";
import "../../styles/eventModal.scss";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import Button from "../formElements/Button";
import InputWrapper from "../formElements/InputWrapper";
import UploadBox from "../commonComponent/UploadBox";
import Image from "next/image";
import uploadedLogo from "../../../public/assets/images/logo.svg";

import { useDispatch, useSelector } from "react-redux";
import { selectProfileData, updateUserProfileData } from "@/redux/slices/authSlice";
import { useTranslate } from "@/utils/translationUtils";
import { IMAGE_EXTENSIONS, inputLogoType, MAX_IMAGE_SIZE, S3_PATHS } from "@/constants/commonConstants";
import { toastMessageError, toastMessageSuccess, uploadFileOnS3 } from "@/utils/helper";
import { updateLogo } from "@/services/logoService";
import Loader from "../loader/Loader";
import { removeAttachmentsFromS3 } from "@/services/commonService";
import chalk from "chalk";
// Note: All form logic/components have been removed for static UI development.

interface EditProfileModalProps {
  onClickCancel: () => void;
  onSubmitSuccess: () => void;
}

const LogoUploadModal: FC<EditProfileModalProps> = ({ onClickCancel, onSubmitSuccess }) => {
  const userProfile = useSelector(selectProfileData);

  console.log("User-----Profil----", userProfile);

  const t = useTranslate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const dispatch = useDispatch();

  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(userProfile?.logo || null);
  const [isLoading, setIsLoading] = useState(false);
  const [formChanged, setFormChanged] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Update form values when logoUrl changes
  useEffect(() => {
    console.error(">>>>>>>>>>>>>>>>>>>>>>UseEffect");
    if (imagePreview) {
      setImagePreview(imagePreview);
    }
  }, [userProfile]);

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log("handleImageChange function<--------------------------------------------->");
    setIsLoading(true);
    setFormChanged(true); // Mark form as changed when image is updated
    const { files } = e.target;
    console.log("files------------------->", files);
    console.log("files length------------>", files?.length);
    console.log("files type-------------->", files?.[0]?.type);
    console.log("files name-------------->", files?.[0]?.name);
    console.log("files size-------------->", files?.[0]?.size);
    console.log("MAX_IMAGE_SIZE---------->", MAX_IMAGE_SIZE);
    console.log("Size check result------->", files?.[0] && files[0].size < MAX_IMAGE_SIZE ? "PASSED" : "FAILED");

    if (files?.length && Number(files?.[0].size) < MAX_IMAGE_SIZE) {
      console.log("Size validation passed, processing file");
      const file = files[0];
      console.log("Selected file details:", {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: new Date(file.lastModified).toLocaleString(),
      });

      if (file) {
        const extension = file?.type?.split("/")[1];
        console.log("File extension---------->", extension);
        console.log("Allowed extensions------>", IMAGE_EXTENSIONS);
        console.log("Extension validation---->", IMAGE_EXTENSIONS.includes(extension?.toLowerCase()) ? "PASSED" : "FAILED");

        if (IMAGE_EXTENSIONS.includes(extension?.toLowerCase())) {
          const objectUrl = URL.createObjectURL(file);
          console.log("objectUrl created------>", objectUrl);

          setImagePreview(objectUrl);
          console.log("imagePreview set to---->", objectUrl);

          setImageFile(file);
          console.log("imageFile state updated");

          // If there's an existing image, we'll delete it when submitting the form
          console.log("Current logo in profile->", userProfile?.logo);
        } else {
          console.log("Extension validation failed, showing error toast");
          toastMessageError(t("invalid_file_format"));
        }
      }
    } else {
      console.log("Size validation failed, showing error toast");
      console.log("File size-------------->", files?.[0]?.size);
      console.log("MAX_IMAGE_SIZE---------->", MAX_IMAGE_SIZE);
      toastMessageError(t("invalid_image_size_format"));
    }

    // Reset the input field
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
      console.log("File input field reset");
    }

    setIsLoading(false);
    console.log("Image loading state set to false");
  };

  const uploadImage = async (file: File): Promise<string> => {
    console.log(chalk.yellow.bgRed.bold("image urlllllllllllllllllllllll"),file);
    try {
      const filePath = S3_PATHS.UPLOAD_LOGO.replace(":orgId", `${userProfile?.orgId}`); // e.g., organization-logos/12345
      
      // Upload the file to S3
      const uploadedFileUrl = (await uploadFileOnS3(file, filePath)) as string;
      console.log(chalk.green.bgRed.bold("uploadedFileUrlllllllllllllllllllll"),filePath);

      return uploadedFileUrl;
    } catch (error) {
      console.error(t("error_uploading_image"), error);
      throw new Error(t("failed_to_upload_image"));
    }
  };

  const onSubmit = async () => {
    console.log("=== onSubmit function started ===");
    console.log("formChanged state:", formChanged);

    if (!formChanged) {
      console.log("No changes detected, returning early");
      return; // Prevent submit if nothing changed
    }

    try {
      console.log("Setting isSubmitting to true");
      setIsSubmitting(true);

      let imageUrl = imagePreview || null;
      console.log(chalk.blue.bgRed.bold("image urlllllllllllllllllllllll"),imageUrl);
      console.log("Current imageUrl from userProfile:", imageUrl);
      console.log("Current imageFile state:", imageFile ? `File: ${imageFile.name}` : "No file selected");

      // If a new image is selected
      if (imageFile) {
        console.log("New image selected, proceeding with upload flow");

        // Prepare for removing old image if exists
        console.log("Removing old image if exists, fileUrlArray:", JSON.stringify([imageUrl]));
        const result = await removeAttachmentsFromS3({ fileUrlArray: JSON.stringify([imageUrl]) });
        console.log("removeAttachmentsFromS3 result:", result);

        // Upload the new image
        console.log("Uploading new image file:", imageFile.name);
        imageUrl = await uploadImage(imageFile);
        console.log("Image uploaded successfully, new URL:", imageUrl);

        // Call API to update profile
        console.log("Calling updateLogo API with imageUrl:", imageUrl);
        const response = await updateLogo(imageUrl || "");
        console.log("updateLogo API response:", response);

        if (response.data && response.data.success) {
          console.log("API call successful, updating Redux store");

          // Update Redux store
          console.log("Dispatching updateUserProfileData with:", { logo: imageUrl });
          dispatch(updateUserProfileData({ logo: imageUrl }));

          // Show success toast and close modal
          console.log("Showing success toast with message:", response.data.message);
          toastMessageSuccess(t(response.data.message));

          console.log("Calling onSubmitSuccess to close modal");
          onSubmitSuccess(); // Close modal after success
        } else {
          console.error("API call returned error:", response.data);
          const errorMessage = t(response.data?.message || "failed_to_update_profile");
          console.error("Showing error toast:", errorMessage);
          toastMessageError(errorMessage);
        }
      } else {
        console.error("No image file selected but formChanged is true - this shouldn't happen");
        console.log("formChanged:", formChanged);
        console.log("imageFile:", imageFile);
        toastMessageError(t("something_went_wrong"));
      }
    } catch (error) {
      console.error("Exception during onSubmit execution:", error);
      console.error(t("error_updating_profile"), error);
      const errorMessage = t("an_error_occurred_while_updating_profile");
      console.error("Showing error toast:", errorMessage);
      toastMessageError(errorMessage);
    } finally {
      console.log("Finally block: Setting isSubmitting back to false");
      setIsSubmitting(false);
    }

    console.log("After submission process completed, userProfile.logo:", userProfile?.logo);
    console.log("=== onSubmit function completed ===");
  };

  // Static modal for UI development
  return (
    <>
      <div className="modal theme-modal show-modal">
        <div className="modal-dialog modal-dialog-centered modal-md">
          <div className="modal-content">
            <div className="modal-header pb-0">
              <h2 className="mt-0 mb-4">{t("upload_organization_logo")}</h2>
              <button className="modal-close-btn" type="button" disabled={isLoading || isSubmitting} onClick={onClickCancel}>
                <ModalCloseIcon />
              </button>
            </div>
            <div className="modal-body">
              <form>
                <InputWrapper>
                  {/* uploaded-item */}
                  <div className="d-flex flex-wrap">
                    <InputWrapper.Label htmlFor="attachments">{t("logo_preview")}</InputWrapper.Label>
                    <div className="uploaded-image">
                      {userProfile?.logo ? (
                        <Image src={imagePreview ? imagePreview : userProfile.logo} alt="uploaded logo" width={100} height={100} />
                      ) : imagePreview ? (
                        <Image src={imagePreview} alt="uploaded logo" width={100} height={100} />
                      ) : (
                        <Image src={uploadedLogo} alt="uploaded logo" width={100} height={100} />
                      )}
                    </div>
                  </div>

                  <UploadBox
                    UploadBoxClassName="upload-card-sm"
                    onChange={handleImageChange}
                    disabled={isSubmitting}
                    isLoading={isSubmitting}
                    type={inputLogoType}
                  />
                </InputWrapper>
                <div className="row mt-4">
                  <div className="col-md-12">
                    {/* <Button type="submit" className="primary-btn w-100 rounded-md">
                      {"Save"}
                    </Button> */}
                    <Button
                      className="primary-btn rounded-md w-100"
                      onClick={onSubmit}
                      disabled={isSubmitting || !formChanged}
                      title={!formChanged ? t("no_changes_made") : ""}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader className="spinner-border-sm me-2" />
                          {t("saving_changes")}
                        </>
                      ) : (
                        t("save_changes")
                      )}
                    </Button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default LogoUploadModal;
