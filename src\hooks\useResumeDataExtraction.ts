import { useState, useCallback } from "react";
import { extractResumeData } from "@/services/screenResumeServices";
import { uploadFileOnS3 } from "@/utils/helper";
import { ExtractedCandidateData, ExtractedDataStore, ExtractionState, UseResumeDataExtractionReturn } from "@/interfaces/candidatesInterface";

/**
 * Custom hook for managing resume data extraction
 * Handles multiple candidate extractions with state management, retry logic, and error handling
 */
export const useResumeDataExtraction = (): UseResumeDataExtractionReturn => {
  const [extractionStates, setExtractionStates] = useState<Record<string, ExtractionState>>({});
  const [extractedData, setExtractedData] = useState<Record<string, ExtractedDataStore>>({});
  const [uploadedUrls, setUploadedUrls] = useState<Record<string, string>>({});

  // Helper function to update extraction state
  const updateExtractionState = useCallback((fieldId: string, updates: Partial<ExtractionState>) => {
    setExtractionStates((prev) => ({
      ...prev,
      [fieldId]: {
        ...prev[fieldId],
        ...updates,
      },
    }));
  }, []);

  // Helper function to set extracted data
  const setExtractedDataForField = useCallback((fieldId: string, data: ExtractedCandidateData) => {
    setExtractedData((prev) => ({
      ...prev,
      [fieldId]: {
        data,
        isUserModified: false,
        extractionTimestamp: Date.now(),
      },
    }));
  }, []);

  // Main extraction function
  const extractData = useCallback(
    async (file: File, fieldId: string, candidateIndex: number, decryptedJobId?: string, candidateName?: string): Promise<void> => {
      console.log(`Starting extraction for fieldId: ${fieldId}, candidateIndex: ${candidateIndex}`);
      // Initialize extraction state
      updateExtractionState(fieldId, {
        isExtracting: true,
        isComplete: false,
        error: null,
      });

      try {
        // Upload file to S3 first
        console.log(`Uploading file to S3 for fieldId: ${fieldId}`);
        const timestamp = Date.now();

        // Use standardized file path format if jobId and candidateName are provided
        let filePath: string;
        if (decryptedJobId) {
          const candidateNameReplace = candidateName?.split(" ")[0];
          filePath = `manual-uploads/${decryptedJobId}/resumes/${candidateNameReplace? candidateNameReplace+ "-" : ""}resume-${timestamp}.pdf`;
        } else {
          // Fallback to original format for backward compatibility
          const fileName = file.name.replace(/\.[^/.]+$/, "").replace(/\s+/g, "_"); // Remove extension
          filePath = `manual-uploads/${fileName}-${timestamp}.pdf`;
        }

        const fileUrl = await uploadFileOnS3(file, filePath);

        if (!fileUrl) {
          throw new Error("Failed to upload file to S3");
        }

        console.log(`File uploaded successfully: ${fileUrl}`);

        // Store the uploaded URL for reuse during form submission
        setUploadedUrls((prev) => ({
          ...prev,
          [fieldId]: fileUrl,
        }));

        // Call extraction API
        console.log(`Calling extraction API for fieldId: ${fieldId}`);
        const response = await extractResumeData({ fileUrl, candidateIndex });

        console.log("response=============>", response);
        console.log("response=============>", response);
        if (response?.data?.success) {
          console.log("inside Response success", response.data);
          // Success - store extracted data
          console.log(`Extraction successful for fieldId: ${fieldId}`, response.data.data);

          setExtractedDataForField(fieldId, response.data.data);

          updateExtractionState(fieldId, {
            isExtracting: false,
            isComplete: true,
            error: null,
          });
        } else {
          // API returned error
          const errorMessage = response.data?.message || response.data?.error || "Failed to extract resume data";
          console.error(`Extraction failed for fieldId: ${fieldId}:`, errorMessage);

          updateExtractionState(fieldId, {
            isExtracting: false,
            isComplete: false,
            error: errorMessage,
          });
        }
      } catch (error) {
        console.error(`Error during extraction for fieldId: ${fieldId}:`, error);

        const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";

        updateExtractionState(fieldId, {
          isExtracting: false,
          isComplete: false,
          error: errorMessage,
        });
      }
    },
    [updateExtractionState, setExtractedDataForField]
  );

  // Clear extraction data for a field
  const clearExtraction = useCallback((fieldId: string) => {
    console.log(`Clearing extraction data for fieldId: ${fieldId}`);
    // Clear state
    setExtractionStates((prev) => {
      const newState = { ...prev };
      delete newState[fieldId];
      return newState;
    });

    setExtractedData((prev) => {
      const newState = { ...prev };
      delete newState[fieldId];
      return newState;
    });

    setUploadedUrls((prev) => {
      const newState = { ...prev };
      delete newState[fieldId];
      return newState;
    });
  }, []);

  // Check if any extraction is in progress
  const isAnyExtracting = Object.values(extractionStates).some((state) => state.isExtracting);

  return {
    extractData,
    extractionStates,
    extractedData,
    uploadedUrls,
    clearExtraction,
    isAnyExtracting,
  };
};

export default useResumeDataExtraction;
