/* Home page scroll-based animations */

/* Base class for all scroll animations */
.scroll-hidden {
  opacity: 0 !important;
  visibility: hidden !important;
  transition:
    opacity 0.9s ease-out,
    visibility 0.9s ease-out,
    transform 0.9s ease-out !important;
}

/* Slide animations */
.slide-in-left-scroll {
  transform: translateX(-50px);
  animation: slideLeft 0.9s all;
}
@keyframes slideLeft {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.slide-in-right-scroll {
  transform: translateX(50px);
}

.slide-in-bottom-scroll {
  transform: translateY(50px);
  visibility: hidden;
  opacity: 0;
  transition:
    transform 1s ease-out,
    visibility 1s,
    opacity 1s !important;
}

.slide-in-top-scroll {
  transform: translateY(-50px);
  visibility: hidden;
  opacity: 0;
  transition:
    transform 1s ease-out,
    visibility 1s,
    opacity 1s !important;
}

/* Zoom animation */
.zoom-in-scroll {
  transform: scale(0.8);
  visibility: hidden;
  opacity: 0;
  transition:
    transform 1s ease-out,
    visibility 1s,
    opacity 1s !important;
}

/* Common visible state for all animations */
.scroll-visible {
  transform: translate(0, 0) scale(1) !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Hover effects */
.candidate-card.hover-primary:hover {
  background-color: #436eb6 !important;
  color: #ffffff !important;
  border-color: #436eb6 !important;
  box-shadow: 0px 10px 25px rgba(67, 110, 182, 0.3) !important;
  h2 {
    text-decoration: underline !important;
  }

  h2,
  p,
  .title,
  .description {
    color: #ffffff !important;
  }

  /* Make all links and buttons white */
  a,
  button,
  .actions a,
  span,
  div[role="button"],
  .link,
  [class*="link"],
  [class*="btn"],
  .set-view-questionnaire {
    color: #ffffff !important;
    border-color: #ffffff !important;

    svg {
      fill: #ffffff !important;
      stroke: #ffffff !important;
      path {
        fill: #ffffff !important;
        stroke: #ffffff !important;
      }
    }
  }
}
