@use "./abstracts" as *;
//new footer style ----------

.footer {
  background: #111;
  padding: 3rem 0;
  @media screen and (max-width: 767px) {
    text-align: center;
  }
  .footer_top {
    border-bottom: solid 1px rgba($white, 0.2);
    padding-bottom: 2.2rem;
    .footer_left {
      .logo_footer {
        width: 180px;
        height: 50px;
        object-fit: contain;

        @media (min-width: 768px) and (max-width: 991px) {
          width: 180px;
          height: 50px;
          margin-top: -5px;
          margin-bottom: 1.5rem;
        }
        @media screen and (max-width: 767px) {
          width: 125px;
          height: 35px;
          margin-top: 0;
        }
      }
      .social_media {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-top: 1.5rem;
        @media screen and (max-width: 767px) {
          justify-content: center;
          margin-bottom: 10px;
        }
        svg {
          width: 35px;
          height: 35px;
          object-fit: contain;
          cursor: pointer;
          transition: all 0.5s ease-in 0s;
          &:hover {
            fill: $secondary;
          }
        }
      }
      @media screen and (max-width: 767px) {
        margin-bottom: 20px;
      }
    }
    .footer_right {
      .links_heading {
        color: $white;
        margin-bottom: 1rem;
      }
      .app_links {
        display: flex;
        gap: 1rem;
        justify-content: flex-start;
        img {
          width: 150px;
          height: 45px;
          object-fit: contain;
        }
        @media screen and (max-width: 767px) {
          justify-content: center;
          img {
            width: 120px;
          }
        }
      }
    }
  }
  .footer_bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 2rem;
    gap: 1rem;
    p {
      color: $white;
      font-weight: $regular;
      font-size: 1.3rem;
    }
    .terms_policy {
      display: flex;
      align-items: center;
      gap: 1rem;
      color: $white;
      a {
        color: $white;
        font-size: 1.3rem;
      }
    }
    @media screen and (max-width: 767px) {
      flex-flow: column;
    }
  }
}
