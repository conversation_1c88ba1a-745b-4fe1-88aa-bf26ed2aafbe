"use client";

import React from "react";
import <PERSON>goraRTC, { AgoraRTCProvider } from "agora-rtc-react";
import Interview from "./Interview";

const InterviewWithAgora = () => {
  // Create the Agora client only on the client side
  return (
    <AgoraRTCProvider client={AgoraRTC.createClient({ mode: "rtc", codec: "vp8" })}>
      <Interview />
    </AgoraRTCProvider>
  );
};

export default InterviewWithAgora;
