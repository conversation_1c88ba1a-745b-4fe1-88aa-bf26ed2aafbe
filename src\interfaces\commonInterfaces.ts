import { TApiState } from "@/types/types";
import { AxiosResponse } from "axios";
import { ILoginResponse } from "./authInterfaces";
import { MessageType } from "@/constants/commonConstants";

/* eslint-disable @typescript-eslint/no-explicit-any */
export interface ApiResponse<T = Record<string, any>> extends Partial<AxiosResponse<T | TApiState>> {
  data: TApiState;
  error: TApiState;
}

export interface IApiResponseCommonInterface<T> {
  data: {
    success: boolean;
    message: string;
    data: T;
  };
  error: TApiState;
}

export interface ISession {
  user: {
    success: boolean;
    message: string;
    data: ILoginResponse;
    code: number;
    iat: number;
    exp: number;
    jti: string;
  };
  expires: string;
}

export interface FilePath {
  filePath: string;
  fileFormat: string;
}

export interface IParsedInfo {
  interviewId: number;
  resumeLink: string;
  time: string;
  jobApplicationId: number;
  interviewType: string;
  isEnded: number;
  jobId: number;
  channelName: string;
  candidateId: number;
  candidateName: string;
  interviewerName: string;
  interviewerId: number;
  personType: string;
  date: string;
  isAuthorized: string;
  isCanceled: number;
}

export interface IInfiniteScrollParams {
  offset?: number;
  prevOffset?: number;
  hasMore?: boolean;
}

export interface IUseInfiniteScrollParams extends IInfiniteScrollParams {
  apiService: (params: any, departmentId?: number) => Promise<ApiResponse>;
  apiParams?: { [key: string]: unknown };
  limit?: number;
  key?: string;
}

export interface IUseInfiniteScrollReturn {
  data: any[];
  hasMore: boolean;
  loading: boolean;
  loadingMore: boolean;
  offset: number;
  message: { type: MessageType; description: string } | any;
  apiResponse?: ApiResponse;
  setLoading: (flag: boolean) => void;
  loadMore: (event?: any) => void;
  setData: (data: any[]) => void;
  fetchData: (firstLoad?: boolean, otherParams?: any, departmentId?: number) => void;
}

// export interface IPredictionsResponse {
// 	success: boolean;
// 	message: string;
// 	data: IFetchPredictionData;
// }

// export interface IFetchPredictionData {
// 	predictions: Prediction[];
// 	status: string;
// 	success?: boolean;
// }

// export interface Prediction {
// 	description: string;
// 	matched_substrings: MatchedSubstring[];
// 	place_id: string;
// 	reference: string;
// 	structured_formatting: StructuredFormatting;
// 	terms: Term[];
// 	types: string[];
// }

// export interface Term {
// 	offset: number;
// 	value: string;
// }

// export interface MatchedSubstring {
// 	length: number;
// 	offset: number;
// }

// export interface StructuredFormatting {
// 	main_text: string;
// 	main_text_matched_substrings: MainTextMatchedSubstring[];
// 	secondary_text: string;
// }

// export type MainTextMatchedSubstring = MatchedSubstring

// export interface IUseAutoCompleteAddress {
// 	predictions: Prediction[];
// }
