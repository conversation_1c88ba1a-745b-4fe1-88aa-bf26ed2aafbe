import React from "react";

function JoinMeetingIcon({ className }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} width="28" height="21" viewBox="0 0 28 21" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.7289 15.2171C19.8367 17.6599 17.8652 19.7255 15.3258 19.8293C15.1388 19.8372 6.02009 19.8188 6.02009 19.8188C3.49296 20.0106 1.28121 18.1947 1.08188 15.7611C1.06686 15.5798 1.07095 5.7956 1.07095 5.7956C0.959001 3.35022 2.92773 1.27933 5.46852 1.17158C5.65829 1.16239 14.7647 1.17947 14.7647 1.17947C17.3041 0.99025 19.5227 2.81936 19.7193 5.26474C19.7329 5.44082 19.7289 15.2171 19.7289 15.2171Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.7334 7.8071L24.1241 4.21377C25.2121 3.3231 26.8441 4.0991 26.8427 5.5031L26.8267 15.3018C26.8254 16.7058 25.1921 17.4751 24.1067 16.5844L19.7334 12.9911"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default JoinMeetingIcon;
