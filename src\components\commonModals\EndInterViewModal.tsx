"use client";
import React, { FC } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import { useTranslations } from "next-intl";

interface IProps {
  onClickCancel: () => void;
  onClickEndInterview: () => void;
  disabled?: boolean;
}

const EndInterViewModal: FC<IProps> = ({ onClickCancel, onClickEndInterview, disabled }) => {
  const t = useTranslations();
  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header justify-content-center">
            <h2>{t("end_interview") + "?"}</h2>
            <p className="textMd w100">{t("leave_interview_confirmation")}</p>
            <Button className="modal-close-btn" onClick={onClickCancel}>
              <ModalCloseIcon />
            </Button>
          </div>
          <div className="modal-body pt-0">
            <div className="action-btn">
              <Button className="primary-btn rounded-md w-100" onClick={onClickEndInterview} loading={disabled} disabled={disabled}>
                {t("end_interview")}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default EndInterViewModal;
