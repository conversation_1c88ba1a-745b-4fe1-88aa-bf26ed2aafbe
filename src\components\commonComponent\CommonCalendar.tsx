"use client";
import React, { useEffect, useMemo, useRef, useState } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import { DatesSetArg, DateSelectArg, EventClickArg, DayHeader<PERSON>ontentArg, MoreLinkArg, EventSegment } from "@fullcalendar/core";
import { IGetInterviewsResponse } from "@/interfaces/interviewInterfaces";
import Button from "../formElements/Button";
import Dark<PERSON><PERSON> from "../svgComponents/DarkCross";
import { EventImpl } from "@fullcalendar/core/internal";
import { useTranslations } from "next-intl";
import "react-loading-skeleton/dist/skeleton.css";
// import Skeleton from "react-loading-skeleton";

interface CalendarProps {
  handleDatesSet: (info: DatesSetArg) => void;
  handleOnSelect: (info: DateSelectArg) => void;
  interviews: Array<IGetInterviewsResponse>;
  handleEventClick: ({ event }: { event: EventImpl }) => void;
  calendarLoading?: boolean;
}

const CommonCalendar: React.FC<CalendarProps> = ({ handleDatesSet, handleOnSelect, interviews, handleEventClick, calendarLoading = false }) => {
  const calendarRef = useRef<FullCalendar>(null);

  const t = useTranslations();
  const [showMoreEventsModal, setShowMoreEventsModal] = useState(false);
  const [events, setEvents] = useState<EventSegment[]>([]);
  const [currentViewType, setCurrentViewType] = useState<string>("dayGridMonth");

  useMemo(() => {
    if (calendarLoading) setShowMoreEventsModal(false);
  }, [calendarLoading]);

  // to disable the tab key when modal is open
  useEffect(() => {
    console.log("showMoreEventsModal @@@@@@@@@", showMoreEventsModal);
    const handleTabKey = (e: KeyboardEvent) => {
      if (showMoreEventsModal && e.key === "Tab") {
        e.preventDefault();
      }
    };
    window.addEventListener("keydown", handleTabKey);
    return () => {
      window.removeEventListener("keydown", handleTabKey);
    };
  }, [showMoreEventsModal]);

  // Apply IDs to calendar elements for the tour
  useEffect(() => {
    // Add ID to calendar navigation buttons (left toolbar chunk)
    const leftToolbar = document.querySelector(".fc-toolbar-chunk:nth-child(1)");
    if (leftToolbar) {
      leftToolbar.id = "calendar-nav-buttons";
    }

    // Add ID to view toggles (right toolbar chunk)
    const rightToolbar = document.querySelector(".fc-toolbar-chunk:nth-child(3)");
    if (rightToolbar) {
      rightToolbar.id = "calendar-view-toggles";
    }
  }, []);

  console.log("CommonCalendar render - calendarLoading:", calendarLoading);

  const renderEventContent = (eventInfo: EventClickArg) => {
    return (
      <div className="fc-event-content">
        <div className="fc-event-time">
          {new Date(eventInfo.event.start!).toLocaleTimeString("en-US", {
            hour: "numeric",
            minute: "2-digit",
            hour12: false,
          })}
        </div>
        <div className="fc-event-title">{eventInfo.event.title}</div>
      </div>
    );
  };

  const renderDayHeader = (args: DayHeaderContentArg) => {
    // Only apply custom header in week view
    const date = args.date;
    const dayNumber = date.getDate();
    const weekday = new Intl.DateTimeFormat("en-US", { weekday: "short" }).format(date);
    if (args.view.type === "timeGridWeek") {
      return (
        <div className="custom-day-header">
          <div className="day-number">{dayNumber}</div>
          <div className="weekday-name">{weekday}</div>
        </div>
      );
    } else {
      return (
        <div className="custom-day-header">
          <div className="weekday-name">{weekday}</div>
        </div>
      );
    }
  };

  // const loading = calendarLoading
  //   ? {
  //       dayCellContent: () => (
  //         <div className="skeleton-day-cell">
  //           <Skeleton width={24} height={18} />
  //           <Skeleton width="90%" height={16} borderRadius={4} className="mt-1" />
  //         </div>
  //       ),
  //     }
  //   : {};

  return (
    <div className={`calendar-container ${showMoreEventsModal ? "side-modal-open" : ""}`}>
      <FullCalendar
        ref={calendarRef}
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
        headerToolbar={{
          left: "prev,next",
          center: "title",
          right: "dayGridMonth,timeGridWeek",
        }}
        initialView={currentViewType}
        editable={false}
        selectable={!showMoreEventsModal}
        fixedWeekCount={false}
        dayMaxEvents={true}
        weekends={true}
        events={interviews.map((interview) => ({
          ...interview,
          className: interview.isCanceled ? "canceledInterview" : interview.isEnded ? "completedInterview" : "incompleteInterview",
        }))}
        select={handleOnSelect}
        eventClick={(info) => {
          if (!showMoreEventsModal) {
            console.log("info===@@@@@@", info);
            handleEventClick(info);
          }
        }}
        height="69vh"
        datesSet={(info) => {
          handleDatesSet(info);
          setCurrentViewType(info.view.type);
        }}
        eventContent={renderEventContent}
        // dayHeaderFormat={{ weekday: "short", day: "numeric" }}
        dayHeaderContent={renderDayHeader}
        slotLabelFormat={{
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        }}
        eventTimeFormat={{
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        }}
        eventDidMount={(info) => {
          // if (info.event.extendedProps.isCanceled) {
          //   info.el.classList.add("canceledInterview");
          // } else {
          //   if (info.event.extendedProps.isEnded) {
          //     info.el.classList.add("completedInterview");
          //   } else {
          //     info.el.classList.add("incompleteInterview");
          //   }
          // }

          // Add time-based class (past, future, today)

          if (info.el.classList.contains("fc-event-today")) {
            info.el.classList.remove("fc-event-today");
            let timeClass = "fc-event-today";
            const endTime = info.event.end ? new Date(info.event.end).getTime() : new Date().getTime();
            const now = new Date().getTime();
            if (endTime) {
              if (endTime < now) {
                timeClass = "fc-event-past";
              } else {
                timeClass = "fc-event-future";
              }
            }
            info.el.classList.add(timeClass);
          }
        }}
        moreLinkClick={(arg: MoreLinkArg) => {
          if (!showMoreEventsModal) {
            console.log("MoreLinkArg==============@@@@@@", arg);
            setEvents(arg.allSegs);
            setShowMoreEventsModal(true);
          }
          return "none";
        }}
      />
      <div id="calendar-more-events" className={`${showMoreEventsModal ? "open" : ""} calendar-more-events-modal`}>
        <div className="events-modal-header">
          <div>
            <p>{t("all_events")}</p>
            {events.length && events[0].event.start ? (
              <p className="date-day">
                {new Date(events[0].event.start).toLocaleDateString("en-US", { weekday: "short", day: "numeric", month: "short" })}
              </p>
            ) : null}
          </div>
          <Button className="clear-btn p-0" onClick={() => setShowMoreEventsModal(false)}>
            <DarkCross />
          </Button>
        </div>
        <div className="events-modal-body">
          {events.map((event) => {
            console.log("==============>@@@@@@", event);
            let timeClass = "today";
            const endTime = event.event.end ? new Date(event.event.end).getTime() : new Date().getTime();
            const now = new Date().getTime();
            if (endTime) {
              if (endTime < now) {
                timeClass = "past";
              } else {
                timeClass = "future";
              }
            }
            console.log("==============>@@@@@@", timeClass);
            return (
              <div
                className={`events-list-card ${timeClass} ${event.event?.extendedProps?.isCanceled ? "canceledInterview" : event.event?.extendedProps?.isEnded ? "completedInterview" : "incompleteInterview"}`}
                key={event.event.id}
                onClick={() => handleEventClick({ event: event.event as EventImpl })}
              >
                {event.event.start ? (
                  <span className="time-text">
                    {new Date(event.event.start).toLocaleTimeString("en-US", { hour: "numeric", minute: "2-digit", hour12: false })}
                  </span>
                ) : null}
                <p className="event-text">{event.event.title}</p>
              </div>
            );
          })}
        </div>
      </div>
      {/* calendar color indication   */}
      <ul id="calendar-events-area" className="calendar-color-indication">
        <li>
          <span className="upcoming" />
          {t("upcoming")}
        </li>
        <li>
          <span className="incompleteInterview" />
          {t("incomplete")}
        </li>
        <li>
          <span className="completedInterview" />
          {t("completed")}
        </li>
        <li>
          <span className="canceledInterview" />
          {t("canceled")}
        </li>
      </ul>
    </div>
  );
};

export default React.memo(CommonCalendar);
