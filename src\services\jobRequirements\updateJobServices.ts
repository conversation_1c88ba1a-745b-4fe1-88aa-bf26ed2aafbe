import endpoint from "@/constants/endpoint";
import * as http from "@/utils/http";
import { ApiResponse } from "@/interfaces/commonInterfaces";

/**
 * Updates the job's active status on the backend
 * @param jobId ID of the job to update
 * @param status Boolean indicating if job is active or not
 * @returns Promise resolving to ApiResponse
 */
export const updateJobStatus = (jobId: number, status: boolean): Promise<ApiResponse> => {
  // Assuming endpoint.jobRequirements.UPDATE_JOB_STATUS is something like '/jobs/updateJob'
  return http.put(`${endpoint.jobRequirements.UPDATE_JOB_STATUS}/${jobId}`, { status });
};
