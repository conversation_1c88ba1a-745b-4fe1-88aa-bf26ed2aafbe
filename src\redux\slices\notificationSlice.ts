import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { NotificationItem } from "@/interfaces/notificationInterface";

const initialState: {
  notifications: NotificationItem[];
  hasUnreadNotifications: boolean;
} = {
  notifications: [],
  hasUnreadNotifications: false,
};

export const notificationSlice = createSlice({
  name: "notification",
  initialState,
  reducers: {
    setNotificationsData: (state, action: PayloadAction<NotificationItem[]>) => {
      state.notifications = action.payload;
    },
    setHasUnreadNotification: (state, action: PayloadAction<boolean>) => {
      state.hasUnreadNotifications = action.payload;
    },
  },
});

export const { setNotificationsData, setHasUnreadNotification } = notificationSlice.actions;
export default notificationSlice.reducer;
